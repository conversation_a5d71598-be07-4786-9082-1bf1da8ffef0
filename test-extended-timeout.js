#!/usr/bin/env node

/**
 * Test extended timeout functionality for deep analysis, ticker queries, and TLDR
 */

import { delegateToMisterAction } from './packages/plugin-mister/dist/actions/delegateToMister.js';

async function testExtendedTimeout() {
    console.log('🧪 Testing Extended Timeout Functionality...\n');
    
    // Mock runtime
    const mockRuntime = {};
    
    // Test cases that should get extended timeout (2 minutes)
    const extendedTimeoutQueries = [
        "I need deep analysis on $SNEK",
        "What's the ticker?",
        "Give me a ticker",
        "What's the play?",
        "Any alpha?",
        "TLDR on Bitcoin",
        "TL;DR market update",
        "Summarize HOSKY news",
        "Risk analysis for MIN",
        "Cabal check on ADA"
    ];
    
    // Test cases that should get normal timeout (30 seconds)
    const normalTimeoutQueries = [
        "What's the price of ADA?",
        "How much is Bitcoin worth?",
        "ADA looks bullish",
        "Bitcoin news"
    ];
    
    console.log('🔍 Testing Extended Timeout Queries (2 minutes)...\n');
    
    for (const query of extendedTimeoutQueries) {
        console.log(`Testing: "${query}"`);
        
        const mockMessage = {
            content: { text: query },
            userId: "test-user",
            roomId: "test-room"
        };
        
        try {
            const shouldDelegate = await delegateToMisterAction.validate(mockRuntime, mockMessage);
            console.log(`   🔍 Validation: ${shouldDelegate ? '✅ Will delegate' : '❌ Will NOT delegate'}`);
            
            if (shouldDelegate) {
                console.log('   ⏱️ Expected timeout: 2 minutes (extended for complex analysis)');
            }
        } catch (error) {
            console.log(`   ❌ Validation Error: ${error.message}`);
        }
        console.log();
    }
    
    console.log('🔍 Testing Normal Timeout Queries (30 seconds)...\n');
    
    for (const query of normalTimeoutQueries) {
        console.log(`Testing: "${query}"`);
        
        const mockMessage = {
            content: { text: query },
            userId: "test-user",
            roomId: "test-room"
        };
        
        try {
            const shouldDelegate = await delegateToMisterAction.validate(mockRuntime, mockMessage);
            console.log(`   🔍 Validation: ${shouldDelegate ? '✅ Will delegate' : '❌ Will NOT delegate'}`);
            
            if (shouldDelegate) {
                console.log('   ⏱️ Expected timeout: 30 seconds (normal for quick queries)');
            }
        } catch (error) {
            console.log(`   ❌ Validation Error: ${error.message}`);
        }
        console.log();
    }
    
    console.log('=' .repeat(60));
    console.log('✨ Extended Timeout Test Complete!\n');
    console.log('📊 Summary:');
    console.log('• Deep analysis queries → 2 minute timeout');
    console.log('• Ticker research queries → 2 minute timeout');
    console.log('• TLDR/Summary requests → 2 minute timeout');
    console.log('• Simple price/news queries → 30 second timeout');
    console.log('\n🚀 MISTER now has appropriate timeouts for different query types!');
}

testExtendedTimeout().catch(console.error);
