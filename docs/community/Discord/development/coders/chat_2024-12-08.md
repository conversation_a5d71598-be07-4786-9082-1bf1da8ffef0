# 💻-coders 2024-12-08

## Summary

The chat focused primarily on configuring and running the openai-compatible model, specifically with .env file adjustments. <PERSON><PERSON> sought guidance for this configuration process while yo<PERSON><PERSON><PERSON><PERSON> shared his experience of successfully utilizing a large OLLAMA 39gig model on high RAM MacBook Pro hardware.

## FAQ

- How to configure with openai-compatible model? Not local ollama, what should I do in .env file? (asked by @micha<PERSON><PERSON>)
- I have a RTX 3090. How can it be used for testing OLLAMA models? (asked by @young<PERSON><PERSON>)
- What are the benefits of buying a MacBook over building your own PC? What makes it worthwhile for certain users like artists and creatives, but not coders or builders? (asked by [<PERSON><PERSON><PERSON>] (03:15))
- Why do people buy macbooks despite custom built pcs being cheaper with more hardware options? Is there a specific reason why some prefer the reliability of MacBooks over PC builds, even if they are aware that it's essentially an expensive cell phone processor? (asked by [agwnl](03:20))
- How long are the session tokens valid for generally? For Twitter. (asked by [<PERSON><PERSON><PERSON> (04:44)])
- Is <PERSON> able to make a trading bot? (asked by [Ȑ̵͘S̷͂̋(05:09)])
- Can the eliza-starter run in WSL terminal? Or does it lack graphical interface support on localhost:3000, resulting in 'Cannot GET / Failed to load resource' error? (asked by [<PERSON><PERSON> (05:08)])
- In a custom action, how can I access the user handle of the person that triggered an action? Is it within the character file? (asked by fOfer (05:26))
- Is there a way to get text before making a request and after, but before sending response back to client? This would be useful for database interactions. (asked by [꧁Ninja_Dev꧂] (05:36))
- On the other hand, if I want to generate messages within plugin is there an easy way currently available? (asked by [fOfer] (05:59))

## Who Helped Who

- @JamLong | Gaia 🌱 helped @michaelben with Configure OLLAMA models using environment variables by providing Michaelben asked about configuring with openai-compatible model and received guidance on checking .env file.
- [agwnl](03:16) helped [Shisho] (03:20) with Discussing laptop options by providing Shisho provided advice on considering custom-built laptops for better performance and cost efficiency
- Shisho helped Grivier with Resolve JSON parsing error by providing Debugging non-JSON characters in response data
- techfren helped Thanks for the info on VPS pricing. with Provided information about low-cost Linux VPS options and AWS grant. by providing [Shisho (04:10)]
- Konstantine helped Reassured that Reddit will be handled before PR. with Provided reassurance about handling a task. by providing [AIFlow.ML (04:59)]
- Ȑ̵͘S̷͂̋ helped Advised to use the client for Eliza-starter. with Provided guidance on using a specific tool. by providing [AIFlow.ML (05:20)]
- Provided information about the client folder and its use for building front-end. helped Ȑ̵͘S̷͂̋ with [fOfer] (05:26) by providing [AIFlow.ML] (05:39)
- Confirmed that the agent replies to comments on tweets, but mentioned an issue with testing. helped Bunchu with [yodamaster726] by providing [꧁Ninja_Dev꧄](07:10)
- [agwnl](08:23) helped [big trav](07:15) with Configuring Eliza agents to post on X platform by providing dotbear89 provided guidance to big trav about setting up Twitter client in agent's character.json and .env file.
- @Kevin Mok helped @AIFlow.ML with Stuck querying when @'ing it in Discord by providing Help with issue #921 in Eliza.

## Action Items

### Technical Tasks

- Configure .env for openai-compatible model (mentioned by michaelben)
- Change default character in `pnpm start` to use ollama 39gig model on macbook pro m4 with 48GB RAM (mentioned by yodamaster726)
- Consider building a custom laptop for better performance (mentioned by [Shisho](03:15))
- Developer should investigate unexpected "\<" character causing JSON parsing error (mentioned by Grivier)
- Apply for an AWS grant (mentioned by [Shisho (04:11)])
- Improve access to user handle within custom actions (mentioned by [fOfer, Grivier])
- Enhance pre-request text retrieval and post-response storage in database for user interactions with bot (mentioned by [fOfer, AIFlow.ML])
- Develop plugin message generation using existing composeContext and generateText methods or create a runtime method for prompt handling (mentioned by [fOfer, Bunchu])
- Investigate issue with bot not responding to replies in Tweets and optimize if necessary (mentioned by [꧁Ninja_Dev꧂])
- Add Twitter client configuration in agent's character.json and .env file for X platform posting. (mentioned by [agwnl](08:23))
- Resolve Tweetbot error on ttwitter (mentioned by @copycat)
- Activate Solana plugin for Bird Eye data service API key (mentioned by @st4rgard3n)
- Investigate plugin parameters issues (mentioned by @DL)

### Documentation Needs

- Clear session tokens and start a new one after hitting 404 error. (mentioned by [Shisho(04:13)])
- Review code contribution process for the repository, focusing on replies and active search (mentioned by [sam-developer, Bunchu])
- Update reaction role bot with new character info and emoji roles. (mentioned by @jin)
- Activation process for plugins to be clarified in the index setup file. (mentioned by @TheHunter@12:30)

### Feature Requests

- Explore MLX Eliza for running models efficiently despite constant changes and fast model runtimes. (mentioned by AIFlow.ML)
- Evaluate the benefits of MacBook's reliability and ease-of-use over PC builds (mentioned by [agwnl](03:20))
- Implement a feature for Eliza agents that allows them to post on X platform. (mentioned by [dotbear89](08:19))
