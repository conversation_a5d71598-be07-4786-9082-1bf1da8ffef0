# 💻-coders 2024-12-09

## Summary

The chat focused on optimizing Telegram integration, retrieving the farcaster cast hash in plugin developments and getting approved reviews for PR merge. There were also discussions about joining core contributors.

## FAQ

- How to get another approved review for PR merge? (asked by @niki<PERSON>_<PERSON><PERSON>)
- Agent not responding in version alpha-1? (asked by Oliver | Hats.finance)
- What is the TypeError when starting agent with plugins? How to fix it? (asked by @dotbear89 (02:39, 04:15))
- Where in codebase should I look for farcaster plugin implementation and examples of updating relationships between entities, any fork available? (asked by @YoungPhlo)
- How can I focus on adding new features? What documentation should be reviewed to achieve this goal? (asked by @shaw)
- What is the current workaround for tweet generation without an API, and how does it work with different setups like SQLite or other databases? (asked by @0xn1c0)
- When fine-tuning, how do you handle cookies on a VPS? What provider are you using for the VPS? (asked by @dotbear89)
- (asked by @Zyro)
- How did you do it? Is it in the character file? (asked by [<PERSON> (08:22)])
- What is causing this error? (asked by [Dan69 (08:23)])

## Who Helped Who

- @leeren helped [Chat Members] with Optimize for throttling and occasional posting by providing Discussion on TG integration optimization
- @bufan helped [Plugins Developers] with Plugin development by providing Retrieving Farcaster cast hash from action's handler.
- @iBuyRare (03:30) helped @dotbear89 (02:41) with Resolving TypeError when starting an agent by providing iBuyRare helped dotbear89 to run the agent with plugins successfully
- [Dolla Llama](07:24) helped [WAWE] SotoAlt | WAWE (07:36) with Investigate issue with agent posting multiple messages by providing Inquiry about running web client
- @shaw helped @SMA with Codebase improvement by providing Reviewing documentation to focus on adding new features
- @braydie helped @dotbear89 with Tweet Generation Workaround by providing Providing a temporary solution for tweet generation without an API, and discussing its compatibility with different database setups.
- @peachy helped @dotbear89 with by providing Peachy shared their experience with creating mainCharacter.ts file and importing it to index.ti, which helped dotbear89 avoid errors.
- [Peachy (08:26)] helped [iBuyRare] with Troubleshooting by providing Peachy helped iBuyRare with setting up Twitter plugin and suggested asking chatgpt or claude for running error logs.
- [Dolla Llama] helped HoneyPotSmoker🐻⛓🍯, dotbear89 with Modify Telegram chat prompts by providing Dolla Llama provided information on modifying prompts in post.ts to change AI openers.
- [Jo] helped [iBuyRare] with Update Twitter Agent by providing iBuyRare and Jo discussed updating the Twitter agent to retweet/like posts.

## Action Items

### Technical Tasks

- Optimize TG integration to handle throttling, occasional posting (mentioned by @leeren)
- Resolve TypeError when starting agent with plugins (mentioned by @dotbear89 (02:39, 04:15))
- Contribute to pyliza project (mentioned by [py16z] safetyBot | 🍚⛓ (05:16))
- Resolve TypeError related to undefined 'actions' (mentioned by @shaw)
- Investigate plugin configuration issue causing tweet posting failure (mentioned by dotbear89)
- Modify Twitter post template for single statement (mentioned by [Dolla Llama (08:19)])
- Modify AI openers for Telegram chat (mentioned by [HoneyPotSmoker🐻⛓🍯, dotbear89])
- Integrate Solana with the project, clone packages folder into starter folder. (mentioned by [iBuyRare])
- Create a new custom character with Twitter, Telegram, and Discord clients (mentioned by [0xn1c0](8:42))
- Adjust bot permissions in Discord groups for agents (mentioned by @꧁Ninja_Dev꧂)

### Documentation Needs

- Update relationships in codebase for farcaster plugin (mentioned by @braydie (03:44))
- Investigate running web client at localhost:5173/ (mentioned by [0xn1c0] Dolla Llama, [WAWE])
- Create a tutorial on adding Eliza plugins to the project setup, based off Peachy's experience with Nader Dabit’s YouTube guide (mentioned by iBuyRare)
- Manually add packages or find an easy way to set them up. (mentioned by [iBuyRare (08:20)])

### Feature Requests

- Retrieve Farcaster cast hash from action's handler in plugin development. (mentioned by @bufan)
- Implement API for tweet generation (mentioned by @dotbear89)
- Update Twitter agent to retweet and like posts (mentioned by [Jo])
- Explore Sepolia testnet for Ethereum transactions. (mentioned by [0xn1c0, iBuyRare])
- Enable ETH transfers for the web client feature. (mentioned by [0xn1c0](8:45))
- Create an agent that listens to group discussions, codes tasks based on conversations, then submits PRs to GitHub. (mentioned by @james*andrew*)
