# agent-dev-school 2024-12-07

## Summary

The technical discussion focused primarily around database schema design, with <PERSON><PERSON> suggesting that creating concrete schemas for tables expected to grow significantly would be beneficial. This approach could help avoid potential scaling issues in the future.

## FAQ

- Anyone hiring junior devs? I have experience in business development, marketing and sales as well. Any suggestions for where to look or how to proceed with job search? asked by @chevronkey
- I didn't see much there for junior devs roles in business development, marketing and sales - any other suggestions? I will look again. (asked by @chevronkey (21:53))
- Where can one post their resume to find job opportunities? (asked by @Odilitime)
- (asked by [@chevronkey](21:53))
- Where can one find job opportunities or get help with finding a role? (asked by @Odilitime (20:20))
- How can one post their resume on the platform? (asked by @Odilitime (22:41))
- (asked by @chevronkey

## Who Helped Who

- @chevronkey(21:53) helped [@chevronkey](21:53) with Finding a role in business development, marketing and sales by providing @Odilitime (20:20) suggested #bountys-gigs-jobs for job opportunities
- [@Odilitime] helped @chevronkey with Posting a Resume by providing @Odilitime (22:41) advised to post resume on the platform

## Action Items

### Technical Tasks

- Create concrete schemas for tables with known growth potential (mentioned by [<PERSON><PERSON>](02:36))

### Feature Requests

- Post resume on #bountys-gigs-jobs for junior dev or biz development roles (mentioned by [Odilitime](22:41))
