# agent-dev-school 2024-12-01

## Summary

Discussion focused on extending functionality of a Discord bot using actions, plugins (mentioned by <PERSON><PERSON>_<PERSON><PERSON><PERSON>), solving an Unauthorized error when linking Solana wallet (<PERSON><PERSON>'s issue resolved with <PERSON><PERSON>’s help). Dorian<PERSON> inquired about AI models and image-text generation separation. <PERSON> suggested focusing on image generation for development school.

## FAQ

- Why am I getting an Unauthorized error when linking a Solana wallet? How can it be resolved? (asked by @<PERSON><PERSON> Duhzit)
- What is the most used AI model currently, and how to separate image generation from text gen in Discord using X Grok or OpenAI API key for different purposes? (asked by [<PERSON><PERSON>])

## Who Helped Who

- [<PERSON><PERSON>] helped @DorianD with Image generation with fal.ai and custom lora models by providing @<PERSON>ie Duhzit
- [<PERSON> (23:45)] helped [<PERSON><PERSON>] with Fixing an issue with TOGETHER API key overwriting OpenAI's settings by providing Identifying and fixing the incorrect order of API keys in .env file to resolve image generation error.

## Action Items

### Technical Tasks

- Extend functionality with actions, plugins (mentioned by [<PERSON>3_<PERSON><PERSON><PERSON>])
- Reorder TOGETHER API key before OpenAI key in .env file (mentioned by [<PERSON><PERSON> (23:45)])

### Documentation Needs

- Update .env file for image generation settings and API keys. (mentioned by [<PERSON><PERSON>])
- Update generation.ts to include missing Heurist condition for image provider selection. (mentioned by [shaw, <PERSON><PERSON>])
