# agent-dev-school 2024-12-06

## Summary

The chat focused on understanding differences in memory management for documents and fragments. @djdabs clarified that 'documents' are higher-level mappings, while 'knowledge' is chunked up with embeds.

## FAQ

- What's the difference between knowledge manager & document manager? Is it outdated code since I don’t see tables for documents or fragments in default startup? 🤔 (asked by @djdabs)

## Who Helped Who

- @Odilitime helped @djdabs with Understanding the difference between knowledge manager & document manager by providing @djdabs explained how to use MemoryManager and where to find relevant functions.

## Action Items

### Technical Tasks

- Review code for document/fragment management (mentioned by @djdabs)

### Documentation Needs

- Watch Dev School Part 3 and share with junior dev team members. (mentioned by @Robin)
