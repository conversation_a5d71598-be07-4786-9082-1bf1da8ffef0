# agent-dev-school 2024-12-02

## Summary

Dorian<PERSON> successfully implemented together/LLAMACLOUD image generation and is working on resolving an openai dependency issue with the Twitter model. <PERSON><PERSON><PERSON> sought help understanding plugin management, which <PERSON>dilitime provided guidance for.

## FAQ

- Is it better to start with eliza or eliza-starter? What factors should be considered when making this decision? (asked by [passion])
- (asked by [<PERSON><PERSON><PERSON><PERSON>])

## Who Helped Who

- <PERSON><PERSON><PERSON><PERSON> helped agora with Understanding how plugins are managed in <PERSON> by providing Odi<PERSON><PERSON> provided a link to the GitHub repository for plugin management
- [<PERSON><PERSON><PERSON><PERSON>] helped [passion] with by providing Advice on whether to start with <PERSON> or eliza-starter based on source modification plans

## Action Items

### Technical Tasks

- Investigate openai dependency issue with Twitter model (mentioned by <PERSON><PERSON>)
- Decide between starting with eliza or eliza-starter based on source modification plans (mentioned by [<PERSON><PERSON><PERSON><PERSON>])

### Documentation Needs

- Update documentation to reflect the decision between using <PERSON> and <PERSON> for new projects. (mentioned by )
