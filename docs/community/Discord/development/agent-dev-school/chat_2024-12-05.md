# agent-dev-school 2024-12-05

## Summary

The most significant technical discussions revolved around a bug causing `pnpm start` to crash due to excessive data, and the difference between Solana plugin vs Goat one. The community provided solutions for accessing YouTube captions by uploading vtt or srt files.

## FAQ

- What's the difference between Solana plugin and Goat one? What was mentioned as a possible solution? (asked by @SotoAlt | WAWE (02:02))
- Is Dev School happening on YouTube or Discord, @shaw (18:36)? (asked by @Bunchu)
- How can I navigate to relevant parts of the video using transcripts? What workaround was suggested? (asked by @boyaloxer)

## Who Helped Who

- @boyaloxer helped Dev School attendees with Accessibility of video transcripts by providing @YoungPhlo provided a solution for accessing captions on YouTube videos by uploading vtt or srt files.

## Action Items

### Technical Tasks

- Address bug causing `pnpm start` crash due to excessive data (mentioned by @coinwitch (ai16z intern))

### Documentation Needs

- Prepare vtt or srt file for YouTube video transcript accessibility. (mentioned by @YoungPhlo)
