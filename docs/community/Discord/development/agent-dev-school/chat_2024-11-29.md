# agent-dev-school 2024-11-29

## Summary

The main technical discussion revolved around implementing an array to store large sets of data as knowledge for a character. <PERSON> suggested this approach, and MetaMike confirmed its implementation in the new branch. <PERSON> provided additional context by sharing relevant documentation on GitHub regarding 'knowledge' system details.

## FAQ

- Any recs on including large sets of data as knowledge for a character? (asked by @marcus)
- Array of strings go into the character file under 'knowledge'? (asked by @MetaMike)
- Wanna do book report series to learn about knowledge system and create NFTs for it? (asked by @yikesawjeez)

## Who Helped Who

- @jin helped @MetaMike with Understanding of character file structure. by providing Sharing a link on how the 'knowledge' feature works

## Action Items

### Technical Tasks

- Implement an array to store large sets of data as knowledge for a character (mentioned by @shaw)

### Documentation Needs

- Update documentation on how the new feature works and its implementation details. (mentioned by )
