# agent-dev-school 2024-12-08

## Summary

The conversation revolves around troubleshooting a specific technical problem (issue #921) related to the bot '<PERSON>'. <PERSON> is experiencing difficulties while using '@eliza', and st4rgard3n provided guidance on checking API keys, Discord Bot token setup in environment variables, and ensuring correct permissions. The issue remains unresolved.

## FAQ

- Hi, I'm looking for help with issue #921 `Stuck querying when @'ing it in Discord` with <PERSON>. (asked by @<PERSON>)

## Who Helped Who

- @st4rgard3n helped @KevinMok with Troubleshoot issue #921 `Stuck querying when @'ing it in Discord` with <PERSON>. by providing st4rgard3n provided troubleshooting steps and asked <PERSON> to confirm if the bot has correct permissions.

## Action Items

### Technical Tasks

- Investigate issue #921 `Stuck querying when @'ing it in Discord` (mentioned by <PERSON>)

### Documentation Needs

- Review documentation for adding bot to Discord and ensure all steps are followed correctly. (mentioned by st4rgard3n)
