# agent-dev-school 2024-11-28

## Summary

The main technical discussion revolved around creating a solution to periodically extract coders' questions from the chat, synthesize 'next class topic', manage Extract-Transform-Load (ETL) processes using GitHub & Discord data. The proposed approach involves setting up cron jobs and building repositories for easy accessibility of this information.

## FAQ

- What does it mean to pass the providers as in yesterday's video? Is data ingested automatically by the agent, and what endpoints are exposed after pnpm start for clients interacting directly with agents? (asked by @shaw (00:15))

## Who Helped Who

- @yikesawjeez (13:57) helped @shaw with Building an ETL pipeline for Discord data extraction and management. by providing @Odilitime@jin will work together to build a solution based on yike<PERSON>w<PERSON>z's suggestion.

## Action Items

### Technical Tasks

- Set up a cron job to periodically dump coders' questions, synthesize 'next class topic', and manage ETL from Discord. (mentioned by @yikesawjeez)

### Documentation Needs

- Create a repository to extract data from both GitHub and Discord for easy accessibility, transformation, and utilization. (mentioned by @Odilitime)
