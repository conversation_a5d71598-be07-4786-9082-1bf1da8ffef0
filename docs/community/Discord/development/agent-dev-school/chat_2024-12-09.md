# agent-dev-school 2024-12-09

## Summary

Discussion focused on resolving issues related to Supabase DB and a custom agent's plugin causing errors. Suggestions included rebuilding the project, saving off configurations/env vars, investigating git status output for potential causes of problems.

## FAQ

- How to resolve 'ERR_PNPM_RECURSIVE_RUN_FIRST_FAIL' error? 🤔 (asked by @SotoAlt | WAWE)
- What could be causing the plugin to cause errors when deployed? (asked by @djda<PERSON>, @Agent Joshua $₱)

## Who Helped Who

- @<PERSON><PERSON> helped @0xArata, @djdabs with Resolve agent not creating rooms issue with Supabase DB. by providing SotoAlt | WAWE suggested deleting db sqlite and rebuilding.
- @<PERSON> helped @djdabs, @st4rgard3n with Resolve error when running repo with new plugin. by providing Agent Joshua $₱ suggested saving off character config and env vars then starting from scratch.
- [<PERSON> $] (21:37) helped [djdabs] with Resolving git changes by providing Adding unstaged files and building/starting the agent

## Action Items

### Technical Tasks

- <PERSON> (@st4rgard3n) and djdabs to debug the error related to new plugin. (mentioned by @djdabs, @Agent <PERSON> $₱)
- <PERSON> (@st4rgard3n) and djda<PERSON> to investigate the issue with `git status` output. (mentioned by @djdabs, @Agent <PERSON> $₱)
- Add all modified, new, or deleted files to staging area (mentioned by [djdabs])
- Build and start the agent after adding unstaged changes (mentioned by [Agent Joshua $] (21:37))

### Feature Requests

- djdabs to investigate plugin causing error (mentioned by @st4rgard3n, @Agent Joshua $₱)
