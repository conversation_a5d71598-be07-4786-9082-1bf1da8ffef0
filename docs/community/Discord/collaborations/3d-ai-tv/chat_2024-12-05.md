# 3d-ai-tv 2024-12-05

## Summary

The discussion revolved around creating fictional characters for an AI-driven chat room, #🤖-the-arena. The main focus was on whether to make a cohesive plot or maintain chaotic interactions like in the current setup of the show.

## FAQ

- Can AIs read .txt attachments on Discord? Is the chat room joinable for character creation discussion? (asked by @SM Sith Lord)
- Should we aim to make a cohesive, plot-driven show or maintain an entertaining yet chaotic dynamic like #🤖-the-arena ? (asked by @whobody)
- <PERSON> and <PERSON> still write South Park? Do they curse more in it now that SNL guys are involved? (asked by [<PERSON><PERSON>])
- How does the format work for creating a video prompt with this approach, specifically regarding threading plots together using 'THEN' structure (asked by [boom])
- How can I improve camera logic? What should cameras be tagged with instead of actor <PERSON> alone? (asked by @boom)
- Shouldn't the Family Guy AI use a general room-viewing camera if an actor isn’t in its scene, or is there another approach to consider for this logic? (asked by @SM <PERSON>h <PERSON>)
- Is it okay for partners to have write access? Can they just lurk instead of contributing? (asked by @jin)
- How does one become a partner, and do you get any special recognition like hats or initiation rituals (jumping in)? (asked by @<PERSON> Sith Lord)
- What are the items in box? Are they being sold on eBay? (https://a.co/d/bG7jEjD link provided by whobody) (asked by [boom](12:25))
- Do you need this item if it's not already owned? (https://a.co/d/jagjuhZ link provided to boom) (asked by [whobody](12:26))

## Who Helped Who

- @SM Sith Lord helped @whobody with Determining the direction of show's character dynamics by providing @jin provided information on AI chat room and .txt attachment reading capabilities.
- [whobody (09:11)] helped [SM Sith Lord] with Exploring new ideas and possibilities by providing Discussing the potential for surprise discovery in AI script generation
- [SM Sith Lord](09:13) & [whobody](09:14) helped with by providing Discussed the concept of 'the geocities effect' and its relation to art
- [boom] helped [SM Sith Lord] with Discussing the format for creating video prompts by providing SM Sith Lord provided insights on South Park writing and 3-act storytelling.
- @SM Sith Lord helped @boom with Improving camera registration logic by providing @SM Sith Lord provided advice on using entity tags and considering location when registering cameras.
- @SM Sith Lord helped @boom with Camera selection based on actors by providing @SM Sith Lord suggested looking at actor ID in the scene to determine which camera should be used.
- @boom helped @SM Sith Lord and others with Improving scene cuts with multiple cameras by providing Provided guidance on camera management isolation
- @whobody helped All members mentioned (@Alsara2k, @jin, and @boom) with Identifying possible new contributors for the project by providing Shared information about potential AI studio resources becoming available due to funding issues.
- @jin, @SM Sith Lord, @boom helped All members with by providing Boosting morale and encouragement
- @Odilitime helped @whobody with by providing Discussed the importance of leaving an audience wanting more in content creation.

## Action Items

### Technical Tasks

- Ask the full cast of AIs in #🤖-the-arena about improving show plot and character ideas. (mentioned by @SM Sith Lord)
- Develop triggers for agents to help write show episodes (mentioned by [SM Sith Lord] (09:06))
- Create a script writer AI that receives short descriptions from each agent and generates show scripts (mentioned by [SM Sith Lord] (09:09))
- Allow agents to revise their roles in the script generated by the writer AI, improving before each episode (mentioned by [SM Sith Lord] (09:08))
- Develop a method for allowing surprise discovery during script generation by open-source AIs (mentioned by [whobody (09:11)])
- Implement a system for adjusting scripts through multiple rewrites, similar to Hollywood (mentioned by [SM Sith Lord (09:12)])
- Develop a system that accepts any method of writing scripts (mentioned by [SM Sith Lord](09:14))
- Implement multi-passing through layers of human thinking for better results with LLMs (Large Language Models) (mentioned by [whobody](09:17))
- Implement a 3-act structure for episode prompts (mentioned by [SM Sith Lord (09:21)])
- Create professional sitcom writer AI model based on the given format and approach. (mentioned by [boom, SM Sith Lord (09:18 - 21)])
- Implement entity tags for camera locations (mentioned by @boom)
- Isolate CameraManager from SpeakingManager (mentioned by @boom)
- Review camera management with @SM Sith Lord in VC (voice call) (mentioned by @boom)
- Investigate compatibility of Xbox One with PC for audio equipment (mentioned by @boom)
- Boom needs a decent headset with over-ear comfort, mic, USB interface (mentioned by [boom])
- Order cables for mic gear (mentioned by [boom](12:26))

### Documentation Needs

- Revise the registration of cameras to consider location, not just actor ID. (mentioned by @SM Sith Lord)

### Feature Requests

- Create fictional characters for AI agents to fit into group dynamic (mentioned by @SM Sith Lord)
- Create a basic show script format for AIs to output scripts and compare their performance (mentioned by [SM Sith Lord (09:12)])
- Create a black box model that includes actors improvising and digesting material (mentioned by [whobody](09:15))
