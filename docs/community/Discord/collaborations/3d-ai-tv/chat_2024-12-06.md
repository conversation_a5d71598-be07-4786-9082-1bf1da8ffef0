# 3d-ai-tv 2024-12-06

## Summary

The conversation revolved around identifying potential spaces for a hackerspace and news station. The participants suggested various room types, such as recording studios, warehouse music video setups, streamer rooms, lofi rooms, bars, alleyways, podcast cleanrooms, science labs with whiteboards, board of directors' meeting areas, water cooler talking spots and a forest gump bench scene. Alsara2k assigned artists to create dework tasks and moodboards for these spaces.

## FAQ

- What camera overrides basics have been made for zoom, panning or cropping? (18:51) (asked by @jin @SM Sith Lord)
- Are you going to discuss what has been done this week in the standup meeting tonight? (asked by [boom])
- How to handle multiple users with same name? Any suggestions for implementation strategy or existing solutions we can use as a reference? Thanks! :) - bloom (20:46) (asked by @bloom)
- What's the best way to update our documentation regarding new authentication flow changes introduced in recent sprint? Any specific sections we should focus on or any existing templates that can be used as a starting point? (asked by [username])
- (asked by [username])

## Who Helped Who

- @Alsara2k helped General Discord chat members with Assigning specific design-related responsibilities to the team by providing Allocation of artists for dework tasks and moodboards creation by <PERSON>sara2<PERSON>.
- [boom] helped Community members needing camera auto-switch functionality. with Order and configure an orbit cam with restricted degrees of movement. Suggested by boom (16:23) by providing Camera AutoSwitching fallback feature implementation by boom (16:08)
- [Username] (20:47) helped @bloom with Discussing implementation strategies for feature request regarding user names duplication issue. by providing @boom provided guidance to @bloom on handling multiple users with same name
- [Username] (20:48) helped [User] with Providing solution for feature request regarding user names duplication issue. by providing @username suggested using a unique identifier system to differentiate users with same name
- [Username] (20:51) helped [User] with Assisting with finding resources for documenting new authentication flow. by providing @username provided a link to an existing template that can be used as reference while updating documentation

## Action Items

### Technical Tasks

- Assign artists to create dework tasks and moodboards for various spaces (mentioned by @Alsara2k)
- Implement Camera AutoSwitching fallback feature (mentioned by [boom (16:08)])

### Documentation Needs

- Order and configure an orbit camera with restricted degrees of movement. (mentioned by [boom (16:23)])
- Update documentation for new authentication flow (mentioned by [username])

### Feature Requests

- Consider implementing a system in both 2D (hackathon) before transitioning to 3D design. (mentioned by @jin)
- Implement feature to handle multiple users with same name (mentioned by [username])
