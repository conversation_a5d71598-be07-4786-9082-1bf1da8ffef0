# 3d-ai-tv 2024-12-02

## Summary

The chat segment revolves around <PERSON><PERSON><PERSON><PERSON><PERSON> announcing a collaboration on AI-3D integration and Eliza Agents platform. <PERSON> seeks advice about simplification of his 'JSONLoader' class, which is confirmed by <PERSON><PERSON> as <PERSON>’s way to work with JSON.

## FAQ

- How can I best contribute or what should be tackled first? (asked by tcm390 (15:37))
- Is the JSONLoader simplification approach correct for LoadScenePayload and SpeakPayload? (asked by boom(19:21))

## Who Helped Who

- Boom helped Understanding of Unity's way to work with JSON. with Simplifying the process for loading different payloads by providing SM Sith Lord (19:20)
- [<PERSON> Sith Lord] helped [boom] with Implementing JSO<PERSON>oader class for managing scenes. by providing guidance on handling scene loading and speaking events without timing logic.
- [<PERSON> <PERSON><PERSON> (19:28)] helped [boom (19:30) with Successful by providing Implementing TTS for scene loading and speaking lines

## Action Items

### Technical Tasks

- Collaborate on AI-3D integration (mentioned by <PERSON>sar<PERSON><PERSON><PERSON> (15:19))
- Implement a new class to manage scene loading, speaking events, and timer logic. (mentioned by [boom])
- Implement TTS handling for scene loading, speaking lines asynchronously (mentioned by [<PERSON> <PERSON><PERSON> (19:28)])
- Update textbox when a character speaks and modularize events for clean code structure (mentioned by [boom (19:29, 19:30)])

### <PERSON>umentation Needs

- Update documentation for JSONLoader with the latest changes made by [boom]. (mentioned by [SM Sith Lord])

### Feature Requests

- Integration of Eliza Agents with erth.ai platform (mentioned by Alsara2k( 15 : 19 ))
- Implement beacon or animation to indicate last speaker and clear previous speakers (mentioned by [<PERSON> Sith Lord (19:30)])
