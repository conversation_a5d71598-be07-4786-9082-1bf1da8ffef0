# 3d-ai-tv 2024-12-03

## Summary

Discussion focused on using Newtonsoft.Json plugin to handle nested JSON objects within Unity, with @boom creating a working prototype for data ingestion & state management.

## FAQ

- What plugin can help with accessing nested objects? What's the syntax for Newtonsoft.Json in Unity? (asked by @boom)
- Does using Newtonsoft.Json require defining structure beforehand, and how does it compare to vanilla Unity JSON handling? (asked by @SM Sith Lord)
- Will we feed in scheduled programming like what <PERSON> was trading during the day and shit too? And I know <PERSON> or someone working on telling bots in Discord to go tweet for you. Could really be ultimate lols, right? (asked by @whobody)
- For an example of 'The crew running hacker lab' show - it’s shaw & marc & jin working on <PERSON> in a computer lab trying to make her sound more human. They were skiing. (asked by @SM <PERSON>h <PERSON>)
- How is the movie review show setup? (asked by [whobody] (15:24))
- What occurs in this AI-based script rewriting system besides bot interactions? (asked by [whobody] (15:38))
- How do you see the movie review working? (15:38, 16:02) (asked by [whobody])
- The AI has deep knowledge of movies that have already come out. So I'm having it review old movies.(15:47)? (asked by [<PERSON>])
- Could the AI-generated show be fed with chat transcript instead of live video? And how would this affect data gathering for vision processing? (asked by [whobody](15:50))
- How long is the intended runtime (e.g., 24/7) and what direction can it take in future iterations? (asked by [SM Sith Lord](15:52))

## Who Helped Who

- @SM Sith Lord helped with Data Ingestion and State Management by providing @boom provided a working prototype in UNity for data ingestion & state management.
- @memelotsqui helped @boom with Demo and documentation of new feature by providing Offered help for demo preparation
- @whobody helped Discord community members with Understanding the format by providing @SM Sith Lord provided an example of 'The crew running hacker lab' show concept and discussed potential formats for different types of content.
- [whobody] (15:38) helped [SM Sith Lord](15:27) with Understanding AI-based script rewriting system for movie review show by providing SM Sith Lord explained how the movie review show is based on a daily chat room of bots and their banter.
- [SM Sith Lord](15:39, 16:02) helped [whobody] with Explaining technical aspects of a new feature by providing [SM Sith Lord] explains the concept and current testing phase for movie reviews using AI.
- [whobody] helped General Discord community members with Implementing the feature to allow viewers interaction with content by providing a solution for generating an AI-generated live feed of chat logs
- [SM Sith Lord](15:52) helped [whobody] with Deciding on show format by providing Discussed pros/cons of continuous vs episodic content
- [Alsara2k] (18:13) helped [SM Sith Lord] with Aiding in finding resources by providing Shared a link to an external resource for AI training.
- [Alsara2k] helped [SotoAlt | WAWE] with Finding alternative news sources by providing a link to an AI TV platform

## Action Items

### Technical Tasks

- Implement Newtonsoft.Json plugin for Unity to access nested objects (mentioned by @SM Sith Lord)
- Document the new blood mode and its associated codes in wiki. (mentioned by @boom)
- Explore solutions for event bridge generation (mentioned by @SM Sith Lord)
- Develop a system to repeat feeding of shows into AI for slight rewrites, adding scenes & characters (mentioned by [SM Sith Lord](15:27))
- Test movie review feature using AI with deep knowledge of movies (mentioned by [SM Sith Lord](15:39))
- Switch between multiple shows using a new system (mentioned by [SM Sith Lord](15:52))
- Implement episodic shows with weekly data consumption (mentioned by [SM Sith Lord](15:52))
- Implement AI interaction with separate prompts for each response. (mentioned by [SM Sith Lord](15:58))
- Organize different methods for AI training (mentioned by [whobody] (16:00))

### Documentation Needs

- Create serializers/deserializers using Newtonsoft.Json features in Unity. (mentioned by @alextoti)
- Explore collaboration with DeepWriter or other teams to improve AI script rewriting capabilities. (mentioned by [whobody](15:36))
- Organize and document variables for episodes vs live streams, context consideration. (mentioned by [whobody](15:53))
- Assign a higher-level person to help with organization and planning. (mentioned by [whobody] (16:01))

### Feature Requests

- Create a cheat code feature for 'bloodmode' (mentioned by @boom)
- Decide on the number of episodes to generate or live-generate after each episode. (mentioned by @SM Sith Lord)
- Create a daily chat room of bots for the basis of movie review show (mentioned by [SM Sith Lord](15:37))
- Implement frame-based trailer review feature for future development. (mentioned by [whobody](15:48, 16:02))
- Implement a system that allows viewers to steer show topics between episodes (mentioned by [SM Sith Lord](15:50))
- Create an AI-generated live feed of the chat logs for viewers to watch and interact with (vote, move content) (mentioned by [whobody](15:50))
- Develop a near-live stream of chat to comic-style TV show (mentioned by [SM Sith Lord](15:57))
