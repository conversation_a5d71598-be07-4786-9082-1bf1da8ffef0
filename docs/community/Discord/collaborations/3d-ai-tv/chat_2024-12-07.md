# 3d-ai-tv 2024-12-07

## Summary

The conversation focused on integrating @bigdookie's artwork as bumpers in their platform. The team discussed automation possibilities, with the sky being 'the limit.' They also considered using TLDraw to create a visual mindmap for project planning and tracking.

## FAQ

- Can we add @bigdookie's work as bumpers? What role does it require? (asked by @boom)
- Should the visual mindmap be created in Google Docs or Figma for better collaboration and tracking of ideas? (asked by @whobody)
- Do writers need to have experience with 3D modeling? No, they don't. Writers can participate without having a background in 3d modelling. (asked by @whobody)
- What are the specific skills needed for JSON wrangling?, (asked by [whobody](09:46))
- How can we make our cameras more robust to prevent fading during shows? (asked by [boom](09:47))
- Is managing the new data source going to be challenging?, // FAQs are limited due to lack of significant questions in chat transcript. (asked by [whobody](09:47))
- Do we need video producers? Why is it complicated for comfy stuff to be fast-paced? (asked by [boom](09:56))
- What are the next steps in establishing a Creative Studio and bidding on projects? How does budget influence project success? (asked by [whobody, boom](10:27))
- How will the open-source approach help us? How can Banodoco handle bids on their end? (asked by [boom (10:00)])
- Can we prompt an engineer to help the story arc or main punchlines for AI-assisted writing? How does it come together with human and AI collaboration in filmmaking? (asked by [boom] (10:05))

## Who Helped Who

- @boom helped @whobody with Creating a visual mindmap for project planning and tracking. by providing @boom suggests using TLDraw, an open-source infinite canvas tool.
- helped [boom](08:26) with No significant help interactions by providing
- @boom helped @jin and @whobody with Finding suitable contributors for the project by providing Boom suggested a 'git-gud' challenge to filter participants
- Reassured boom that managing the new data source won't be too difficult. helped [boom] with Manage JSON wrangling for a new Data Source by providing [whobody](09:47)
- [boom] helped [whobody] with Discussing creative ideas for integrating technology into an arts space. by providing Boom supported Whobody's idea of AI art bots and contributed to the concept with suggestions like 'AI Art Walkthru', 'Art Battles'.
- helped [boom](09:56) with Discussing project details, budget & rates by providing
- [boom] helped [whoever is interested in video production and project bidding process] with Discussed the need for a Creative Studio with people able to bid on projects, emphasizing budget as an important factor by providing [whobody, boom](10:27)
- [boom] helped [whobody] with Problem-solving regarding project direction by providing Discussing the importance of directorial skills in creating a decentralized platform, with reference to iconic directors like Steven Spielberg and Stanley Kubrick.
- [boom] (10:05) helped [whobody] with Creating a better understanding and approach to incorporate AI into filmmaking. by providing Discussing potential of using AI assistance in creative processes like scriptwriting.
- [whobody](10:07) helped boom with Discussing potential solutions for Hollywood industry issues by providing boom suggests using AI writing to mitigate effects of writers' strike

## Action Items

### Technical Tasks

- Automate bumpers with @bigdookie's artwork (mentioned by @whobody)
- Investigate potential water pipe issues (mentioned by [boom](08:26))
- Develop an example of work in the field to aid understanding and contribution back from members. (mentioned by @boom)
- Create a script to extract data from Discord channels (mentioned by @jin)
- Outline of JSON wrangling skills needed for data source management (mentioned by [boom](09:46))
- Develop AI art bots for automated creative processes (mentioned by [whobody])
- Automate audio manipulation for digital graffiti (mentioned by [whobody](09:51))
- Establishing video production team (mentioned by [boom](09:56))
- Open source all project components (mentioned by [boom (09:59)])
- Inject film/crew/studio energy into movements (mentioned by [whobody])

### Documentation Needs

- Create a visual mindmap for project planning and tracking, using Google Docs or Figma. (mentioned by @whobody)
- Create an official onboarding route for the Discord room. (mentioned by @whobody)
- Create basic documentation for virtual production roles (mentioned by @boom)
- Develop bounty template for prompt engineering tasks (mentioned by @jin)
- Reach out to potential collaborator and discuss project details, budget & rates. (mentioned by [boom](09:56))
- Consider bounties for sourcing the team and their rates. (mentioned by [whobody, boom](10:27))
- Form solid teams for handling bids on Banodoco repository. (mentioned by [whobody, boom])

### Feature Requests

- Implement a 'git-gud' challenge to filter participants (mentioned by @boom)
- Develop robust cameras to prevent fading during shows. (mentioned by [boom](09:47))
- Create a space where brands can showcase their marketing efforts through AI art battles and automated displays. (mentioned by [boom, whobody])
