---
title: "Weekly Contributor Meeting Notes"
date: 2025-02-04
description: "Discussion on API key management, plugin architecture changes, code review tools (Windsurf, Graphite, CodeRabbit), and database adapter standardization. Key decisions on version numbering and plugin repository separation."
---

# Weekly Contributor Meeting Notes

(February 4, 2025 4:00 PM PST)

**Navigating API Keys, Plugin Re-Arch, and Database Adapter Standardization**


## Summary

**Action Items:**

*   Troubleshoot the "Merge Queue" issue for forked repositories.
*   Continue efforts to standardize on Biome and clean up ESLint configurations in older plugins.
*   Thoroughly test database adapters in conjunction with knowledge base changes.
*   Investigate and potentially implement consolidation of adapter code (using an ORM like Drizzle).
*   Spin up a new copy of the Eliza website and configure the domain.
*   Await updates on the plugin re-architecture effort (moving plugins to separate repos).
*   Provide OpenAI key to Yags, and figure out a plan for providing keys for testing and development.
*   Fix the minify script to omit specific files that cause GitHub warnings (related to smart contract code).
*   One contributor to check in with a colleague who was focusing on an AWS Docker setup.
*   One contributor to ping Jin for an open router key.

**Main Discussion Points:**

*   **Runtime Error Detection and API Keys:** The meeting began with a discussion about the importance of running code in runtime to catch errors that static analysis might miss. A key issue raised was that many plugins rely on API keys, and if a user lacks credits or a valid key, actions won't execute, making it difficult to test and debug properly. The idea was brought to know and fix coding errors even before actions take place.
*   **Code Review Tools:** Several code review tools were discussed:
    *   **Windsurf:** A PR reviewer tool from the Codeium team, still in beta (and therefore free). It was suggested as a potential tool for reviewers, and even the possibility of creating a VSCode extension to integrate it was raised.
    *   **Graphite:** Another tool mentioned, with a CLI for creating PRs locally and integration with VS Code. It was noted that Graphite, along with CodeRabbit, had been helpful in catching typos in PRs.
    *   **CodeRabbit:** Another tool that was mentioned as a helper for making PRs.
*   **Merge Queue:** A contributor had trouble getting their PRs merged and was advised to use the "Merge Queue" label to add PRs to the merge queue. Troubleshooting was needed as the contributor encountered an error related to forked repositories.
*   **Biome vs. ESLint:** There was a discussion about the transition from ESLint to Biome for code linting and formatting. The contributor confirmed that new contributors should be using Biome, but acknowledged that some older plugins might still have ESLint configurations due to copying from existing plugins. The importance of cleaning up and standardizing on Biome was emphasized.
*   **New Features and Adapters Testing:** The team discussed how new features can affect adapters and that there is a necessity to test all adapters with the new knowledge before deploying the tool. It was mentioned that PG family and Vector family adapters should have their own classes.
*   **Database Adapter Standardization:** Concerns were raised about the knowledge base changes and their potential impact on database adapters (SuperBase, PG, Light, Quadrant, etc.). The need for thorough testing and alignment of adapters, especially those related to Postgres (PG), was stressed. A proposal was made to consolidate and standardize adapter code, potentially using an ORM (like Drizzle, which was mentioned as a good option).
*   **Version Numbering Change:** The team decided to change the version numbering scheme to reflect the year and week of release (e.g., 0.25.6 for year 2025, week 6).
*   **Graphite Integration:** A contributor requested access to the Graphite app within the Eliza OS organization, and it was confirmed that the integration was likely already in place.
*   **Plugin Re-architecture:** A high-priority task was reiterated: moving plugins to separate repositories (outside the main Eliza OS repo). This is a major architectural change, and the team is awaiting an update from the group working on this. The intention is for each plugin to reside in its own repo and be discoverable via GitHub tags. Concerns were raised about how this shift might affect community contributions, and how tags must be properly handled.
*   **Eliza Website (eliza.gg, elizas.com):** The website was down, and a contributor volunteered to spin up a new copy. There was a discussion about domain names, and a contributor offered to provide access to `eliza.xyz` via Cloudflare.
*   **OpenAI Key:** One of the contributors asked for an OpenAI Key.
