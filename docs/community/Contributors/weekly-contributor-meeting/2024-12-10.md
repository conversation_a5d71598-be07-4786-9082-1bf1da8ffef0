---
title: "Weekly Contributor Meeting Notes"
date: 2024-12-10
description: "Notes from the first ai16z Eliza contributor meeting, addressing key challenges in stability, plugins, and codebase organization, with proposed solutions for improvement."
---

# Weekly Contributor Meeting Notes

(December 10, 2024 4:00 PM PST)

**Taming the Beast, The Quest for AI Agent Stability**


## Summary

This first weekly meeting of maintainers and contributors for the ai16z Eliza repo, organized by Odilitime. The discussion revolved around improving the stability, maintainability, and organization of the rapidly growing project.

**Key Issues and Concerns:**

*   **Instability and Breakages:** Frequent crashes and bugs were reported, especially after merging new code. Main was breaking repeatedly despite fixes.
*   **Plugin Management:** There was a hold on merging new plugins due to concerns about quality, maintainability, and the need for a more robust process.
*   **Code Quality and Modularity:** Concerns were raised about the overall quality and modularity of the codebase, particularly regarding how plugins were integrated. The lack of clear separation between core logic and plugins was causing issues.
*   **Testing:** The absence of comprehensive testing, especially for plugins and adapters, was identified as a major problem contributing to instability.
*   **Documentation:** The documentation was incomplete and outdated, making it difficult for new contributors to understand the project and how to use plugins.
*   **Organization and Planning:** The need for better organization, planning, and communication was highlighted, particularly for coordinating contributions and defining a clear roadmap.
*   **Contributor Incentives:** The discussion touched on how to properly incentivize and reward contributors, with considerations for retroactive rewards and avoiding a "mercenary" culture.

**Proposed Solutions and Action Items:**

*   **Branching Strategy:** Enforce pushing new features to the `develop` branch instead of `main` to prevent constant breakages. Close PRs targeting `main` that are not bug fixes.
*   **Plugin V2 API:** Consider creating a new V2 API for plugins that better distinguishes between clients and adapters, allowing for a cleaner separation of concerns.
*   **Monorepo Tools:** Explore using monorepo tools like Turbo or NX to better manage dependencies and potentially break down the project into smaller, more manageable modules.
*   **Testing Framework:** Develop a testing framework to make it easier to write tests for plugins, adapters, and other components. Implement end-to-end tests to ensure basic functionality.
*   **Plugin Requirements:** Draft new requirements for plugins, including contact information for maintainers and possibly a migration path to a V2 API.
*   **JSON Configuration:** Allow specifying plugins in the character JSON configuration file to enable easier multi-tenant agent setup.
*   **Documentation Improvement:** Prioritize improving the documentation, potentially with the help of an AI agent to keep it up-to-date.
*   **Organization and Communication:** Use GitHub issues more effectively for tracking tasks and discussions. Consider using tools like Gorse to visualize code contributions.
*   **Contributor Roles:** Clarify roles and responsibilities, particularly regarding release engineering, architecture, and DevOps.
*   **Contributor Rewards:** Develop a system for rewarding contributors, potentially using quadratic funding and considering retroactive rewards.
*   **Regular Meetings:** Establish regular meetings (weekly, as suggested) to improve communication and coordination among maintainers and contributors.
*   **AI Agents:** Utilize AI agents to assist with documentation, onboarding, and other tasks.

**Participants:**

The meeting involved several key participants, including Odilitime (the organizer), Jin, 0x8664,and several others who actively contributed to the discussion.

**Overall Tone:**

The tone of the meeting was constructive and collaborative, with participants expressing a shared desire to improve the Eliza Repo and address the challenges of its rapid growth. There was a sense of urgency to implement solutions and establish better processes to ensure the project's long-term success.


## Quotables

1. **"We kind of put a hold on merging new plugins and I started going through the repo last night and actually flagging which PRs are just straight up new plugins." (00:01:55)**
    *   This is a hot take because it signals a significant shift in the project's approach to handling new plugins. It implies that the previous process was not sustainable, and the decision to halt new plugin merges could be controversial among contributors eager to add their work. It raises questions about the criteria for accepting plugins and the long-term vision for plugin integration.

2. **"Yeah, what I would love if we can manage- it is to get people pushing to develop and not main because main keeps like- we fix main and then it re breaks and this is the same." (00:02:19)**
    *   This highlights a major pain point in the development process and suggests a stricter control over the `main` branch. This could be controversial as it restricts contributors' ability to directly merge into `main` and might be perceived as a slowdown in development, even though the intention is to increase stability.

3. **"So one thing we were kind of thinking about is, you know, how we could potentially segment the repository because there's functionally like the core of this repository is agent folder, right?" (00:08:05)**
    *   This is a hot take as it proposes a fundamental change to the structure of the repository. Segmenting the repository could be a significant undertaking and might face resistance or debate about the best way to implement modularity. It suggests a potential shift from a monolithic architecture to a more modular one, which has implications for development and maintenance.

4. **"I think we could use some kind of organization, I know Rudolph and other folks are doing, and then the ability to execute against it. I'm not sure if there's any kind of, you know, documents we have any, you know, Google Sheets, you know, any sort of structure so that we can kind of not lose things in the discord." (00:16:55)**
    *   This take openly criticizes the current state of organization and communication within the project. The call for a more structured approach, moving beyond Discord chats, implies that the current methods are insufficient and potentially hindering progress. This could spark debate about the best tools and processes for project management.

5. **"If you're doing things that are adding value, I do want to do some stuff with quadratic funding in the future. Because right now, it's just very deterministic, based on objective data." (00:39:45)**
    *   The introduction of quadratic funding is a hot take, especially within an open-source context. It's a relatively novel approach to funding that can be complex to implement and may be met with skepticism or misunderstanding. The discussion around contributor rewards and avoiding a "mercenary" culture adds another layer of complexity and potential controversy to this topic. It's unclear how this would work, given that quadratic funding typically requires tokens, and they stated they are not creating a token.

These takes represent key areas of change, potential conflict, and important decisions that will shape the future of the ai16z Eliza project. They often challenge the status quo, propose significant changes, and touch on sensitive topics like code quality, project organization, and contributor compensation.
