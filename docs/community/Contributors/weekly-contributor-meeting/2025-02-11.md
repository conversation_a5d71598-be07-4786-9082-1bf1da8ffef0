---
title: "Weekly Contributor Meeting Notes"
date: 2025-02-11
description: "Discussion on Discord server consolidation, v2 development progress including local AI plugin improvements, upcoming Hong Kong promotional events, and new plugin registry system implementation. Updates on v2 release timeline targeting April."
---

# Weekly Contributor Meeting Notes

(February 11, 2025 4:00 PM PST)

**Local AI Progress, Discord Changes, and Hong Kong Events**


## Summary

**Action Items:**

*   Fix contributor roles and permissions on the Eliza OS Discord server.
*   Inform contributors about the server migration and provide instructions.
*   Work on the local AI plugin in the v2 development branch, improving its functionality and performance.
*   Coordinate with the team member attending the Hong Kong events to provide promotional materials and messaging.
*   One team member will start looking into the v2 develop branch.
*   Continue development and testing of v2, aiming for the April release target.
*   Review the v2 develop branch and become familiar with the codebase.
*   <PERSON>reate a better marketing campaign to promote calls.

**Main Discussion Points:**

*   **Discord Server Consolidation:** The team discussed the need to consolidate their development communication to the official Eliza OS Discord server. Currently, there are two servers in use, causing confusion. The goal is to migrate everyone to the Eliza OS server, which requires setting up appropriate roles and permissions (like a "contributor" role) and informing contributors.
*   **V2 Development and Local AI Plugin:** A major focus was on the development of version 2 (v2) of Eliza OS.
    *   <PERSON><PERSON><PERSON> agreed to join the V2 team.
    *   The team discussed needed improvements, one of which was the "local AI plugin". The plan is to move the existing local AI functionality from the "plugin node" into the main system of v2, improve its performance, and ensure feature parity with the current Eliza version.
    *   There are some small, well-performing models that do not require outside dependencies.
    *   The v2 development branch already includes some working plugins (OpenAI, Anthropic), but the local AI plugin needs significant work.
*   **Hong Kong Trip and Promotion:**
    *   One team member will be attending events in Hong Kong and plans to promote Eliza OS.
    *   The individual spoke with someone from Google's Web 3.0 development division that may be able to provide Eliza support and exposure.
    *   There are plans to create and distribute Eliza OS t-shirts at an event.
    *   The team member requested clear messaging and information about v2 to share with people, as there's high interest in the new version.
*   **Plugin System and Registry:**
    *   The team discussed the new plugin system and registry.
    *   A new repository called "Eliza OS Plugins" has been created to house official plugins.
    *   A separate "Registry" repository contains an `index.json` file that acts as a directory of all available plugins and their GitHub locations.
    *   This new system allows plugin developers to maintain their own plugins without relying on the core Eliza OS team for merging and code reviews.
    *   A command-line utility has been created to simplify plugin installation for v1.
*   **V2 Release Timeline:**
    *   The team is still aiming for an April release of v2.
    *   There's a desire to have v2 ready for a major convention in Dubai (April 25th - May 1st), but no promises were made.
