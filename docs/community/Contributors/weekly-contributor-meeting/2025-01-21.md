---
title: "Weekly Contributor Meeting Notes"
date: 2025-01-21
description: "Major architectural discussions on implementing a dynamic plugin system and addressing challenges from the Biome linter migration."
---

# Weekly Contributor Meeting Notes

(January 21, 2025 4:00 PM PST)

**Dynamic Plugin System & Biome Migration: Major Architecture Changes Ahead**


## Summary

This was a weekly contributors meeting for the open-source agent framework, ElizaOS. Participants discussed various technical topics, primarily focusing on issues with the codebase, recent changes, and future development plans.

*   **Linting Issues:** The team recently switched from ESLint to Biome as their linter tool. While Biome is faster, it introduced around 2,000 linting errors in the codebase. This led to discussions about how to best address these errors—whether to fix them all at once or progressively. There was also a concern that merging a large pull request (PR) related to Biome might have exacerbated the issue.
*   **Merge Queue and CI/CD:** Participants talked about improving the merge queue process and Continuous Integration/Continuous Deployment (CI/CD) workflows. They considered using remote caching with Turbo to speed up builds and discussed the possibility of setting up their own Drone CI instance.
*   **Agent Discussions:** The use of different PR agents like Devon, CodeRabbit, and AI Flows was brought up. There were mixed opinions on Devon's effectiveness, while CodeRabbit received positive feedback for catching errors in a PR.
*   **Dynamic Plugin System:** A significant portion of the meeting was dedicated to discussing a new dynamic plugin system proposed by a contributor representing Upstreet and Avere. This system aims to move plugins out of the main codebase and into separate repositories, making the core lighter and faster. The system would allow for both "official" plugins maintained by the ElizaOS team and community-submitted plugins.
*   **Security Concerns:** The dynamic plugin system raised security concerns, especially regarding community plugins hosted on any GitHub repository. Ideas to mitigate these risks included maintaining a blacklist of malicious plugins, using automated code scanning tools, and requiring plugins to be loaded from specific release tags rather than directly from the main branch.
*   **Version Compatibility:** With the introduction of the dynamic plugin system, version compatibility between ElizaOS releases and plugin versions became a crucial point of discussion. The team discussed potential approaches to handle versioning, such as aligning plugin versions with ElizaOS releases or using a range-based system similar to NPM.

Other topics touched upon included:

*   The Node.js container, which was deemed unnecessary.
*   Key management issues that need to be addressed.
*   The possibility of adding a REST endpoint to list all registered actions for better oversight.

The meeting concluded with an agreement to prioritize resolving the linting errors introduced by Biome and to further discuss the dynamic plugin system, focusing on its implementation and security aspects.
