---
title: "Weekly Contributor Meeting Notes"
date: 2024-12-31
description: "Meeting notes covering ElizaOS's contributor discussions on improving developer onboarding, CI/CD pipelines, funding models, and technical improvements to the core platform."
---

# Weekly Contributor Meeting Notes

(December 31, 2024 4:00 PM PST)

**Streamlining Contributor Experience and Funding Models**


## Summary

This was a weekly contributor meeting for ElizaOS, an open-source project by ai16z building an AI agent framework.

**1. Discussion on Improving the Onboarding Experience:**

*   A contributor raised concerns about the default use of local Llama as an inference provider, which caused issues for users without GPUs or sufficient hardware.
*   They proposed offering alternative free API providers (like Together, Google AI Studio, and others) with clear documentation to make the initial setup smoother for beginners.
*   The idea was to have a dedicated "inference" channel on Discord to list free inference options and pin them for easy access.
*   They also discussed the possibility of a user-friendly Electron app for launching agents in the future.

**2. Priorities and Challenges in the Codebase:**

*   **CI/CD Pipeline:** The current CI/CD process was identified as a major bottleneck. Merging PRs was slow and complex, requiring multiple maintainers to focus on a single PR at a time. They needed a way to run tests without pulling the entire repo for every commit and to ensure that passing tests wouldn't break the `develop` branch.
*   **Developer Experience (DevEx) vs. New Features:** There was a discussion about the need to prioritize DevEx improvements and bug fixes over adding new features. Many contributors were focused on building new plugins rather than improving the core codebase.
*   **Incentivizing Contributions:** The group discussed the need for better incentives to encourage contributions to DevEx and core improvements. Ideas included bounties, micro-bounties, and clearer communication about priorities through issue tagging.
*   **Financials and Funding:** The lack of a clear process for allocating funds and paying contributors was a significant blocker. They needed a system for tracking contributions and distributing rewards, possibly through a retroactive public goods funding (RetroPGF) model.

**3. Deep Dive into Funding and RetroPGF:**

*   The main obstacle to implementing RetroPGF was the lack of a system to map Discord users to wallet addresses and GitHub accounts.
*   A contributor volunteered to work on this issue full-time, as it was crucial for both rewarding past contributions and establishing a framework for future funding.
*   They discussed using account abstraction (smart accounts) to create wallets based on social logins (GitHub or Google), potentially simplifying the process.
*   The tip bot was also considered as a possible solution for distributing rewards.

**4. Technical Discussion on Embeddings and RAG:**

*   A contributor asked about improving the RAG (Retrieval-Augmented Generation) pipeline, inspired by the performance of AIXBT on Twitter.
*   They discussed the location of embedding-related code in the Eliza codebase (searching for `get_remote_embeddings`) and the issue of zero vectors being used in place of actual data.
*   There was a brief discussion about how to feed external data (PDFs, Markdown files) into Eliza for context, with a mention of the `knowledge` field in character files and the limitations of the current room-based scoping of knowledge.

**5. Action Items and Next Steps:**

*   **Prioritize Issues:** Create and categorize issues into DevEx, product stability, and feature requests to guide contributions.
*   **Improve CI/CD:** Investigate ways to streamline the CI/CD pipeline and automate testing.
*   **Funding and Wallet Mapping:** Work on mapping Discord/GitHub accounts to wallets to enable RetroPGF and other funding mechanisms.
*   **Badges:** Explore the possibility of creating custom GitHub badges to incentivize contributions.
*   **Investigate Embeddings:** Research and address the zero vector issue and improve the RAG pipeline.
*   **Shaw's Conversation on X:** Gather information about Shaw's conversation on X regarding bot monetization and platform guidelines.

**6. Concerns about Bot Behavior and Monetization:**

*   The group discussed Shaw's concerns about agents being used as "reply guys" on Twitter and the need to be good stewards of the platform.
*   They acknowledged that the current Twitter client implementation might violate Twitter's terms of service and discussed the importance of educating users about the risks.
*   There was a consensus that monetizing bots could negatively impact the X platform and that this should be avoided.

**7. Closing Remarks:**

*   Participants emphasized the value of these meetings for new developers and the importance of clear communication and documentation.
*   They reiterated the need to focus on core improvements and DevEx, in addition to building new features.
*   The meeting concluded with a sense of optimism and a commitment to addressing the identified challenges.

In essence, the meeting was a productive discussion about the challenges and opportunities facing the ElizaOS project. The participants identified key areas for improvement, including the onboarding experience, CI/CD pipeline, funding mechanisms, and the need to balance new feature development with core improvements. They also discussed the ethical implications of AI agents on social media platforms and the importance of responsible development.
