---
title: "Weekly Contributor Meeting Notes"
date: 2025-01-28
description: "Cracking Down on Chaos - Biome, Merges, and Quality Control. Discussion on enforcing contributor guidelines, Biome adoption, and quality improvements."
---

# Weekly Contributor Meeting Notes

(January 28, 2025 4:00 PM PST)

**Cracking Down on Chaos - Biome, Merges, and Quality Control**


## Summary

This was a weekly contributors meeting for the open-source agent framework ElizaOS.

**1. Enforcing Contributor Guidelines and Repo Rules:**

*   The meeting started with a discussion about implementing stricter contributor guidelines and enforcing rules on the repository.
*   A key point was to enforce code formatting using Biome to improve code quality and readability.
*   The problem of contributors submitting "draft" Pull Requests (PRs) with non-working or poorly typed code was highlighted.
*   There was agreement on the need to stop merging PRs that don't meet the standards and to ask contributors to fix issues themselves.
*   The goal is to have community guidelines for submissions hammered out and ramped up by next Tuesday.

**2. Biome Adoption and TypeScript Enforcement:**

*   The recent switch to Biome for linting was a major topic. While Biome itself is considered powerful and good, the transition has been messy.
*   The main issue is that the Biome integration was merged without ensuring all plugins and configurations were updated to support it. This resulted in a large number of errors (2700 mentioned) and broken tests.
*   AIFlow is working on fixing Biome errors plugin by plugin, and these PRs need to be merged quickly to avoid further issues and conflicts.
*   The lack of TypeScript typing in contributions is a significant problem, leading to errors and making code harder to maintain. Enforcing stricter typing is seen as crucial, potentially using tools like Cursor to guide contributors.

**3. Plugin Issues and Solana Plugin Unification:**

*   "Draft" PRs with missing parameters and methods are being submitted, indicating a rush to contribute without proper testing or completion.
*   The meeting discussed the issue of having multiple Solana plugins doing the same thing with potentially different library versions.
*   The consensus was to unify these plugins into a single, well-maintained Solana plugin to avoid library version conflicts and improve maintainability. Bounties were suggested to encourage this unification.

**4. Testing and Workflow Improvements:**

*   Fixing broken workflow tests is a priority. The goal is to get back to a state where only PRs with green checkmarks are merged.
*   Smoke tests are being worked on to improve functional testing.
*   Pre-commit hooks to run linters before commits are desired to normalize code formatting and improve PR readability.
*   The meeting discussed workflow tests and aiming to have green checkmarks to prevent merging broken code.

**5. V2 and V1.5 Development:**

*   V2 development is underway, but currently, the focus is shifting towards V1.5 which seems to incorporate elements of V2.
*   There was a request for visibility into the V2 codebase, but access is currently limited. A window to discuss access with Shaw (likely the project lead) is planned.
*   A "develop-v2" branch was mentioned as potentially accessible for contributors to view, though permissions are unclear.

**6. NPM Package Issues and Releases:**

*   Issues with NPM packages have been fixed, and version 0.1.8 alpha 1 is now available.
*   A 1.9 release is planned for Friday to include recent work and allow for testing and patching.
*   Testing of the new release is crucial to avoid issues for users upgrading from previous versions.
*   Merging of new code will be slowed down to allow for stabilization and to avoid contributors' fixes becoming obsolete due to rapid changes.

**7. New Contributor and Community:**

*   A new contributor, Kesa, introduced herself, offering to help with documentation and "chum work" to build her portfolio.
*   She was welcomed and encouraged to contribute, with the team emphasizing a supportive community for mentorship and help.

**8. Tooling and Infrastructure:**

*   Graphite is now online and available. It offers features like a merge queue, which can be used optionally to manage PRs that depend on subsequent fixes (like the Biome transition).
*   SNYK (security vulnerability scanning) is being explored for future integration.
*   Remote cache server setup is in progress to improve build and workflow efficiency.

**9. Security Concerns:**

*   Recent security incidents, like the "Dog Wif Tools" wallet compromise due to injected JavaScript, highlighted the importance of security.
*   The discussion about minified JavaScript and line length checks in plugins is related to security concerns. Exceptions for smart contract code with long lines are needed to re-enable these checks.

**10. Open Issues and Callouts:**

*   A contributor reported an issue where OpenAI embeddings were being used by default even when Anthropic was configured, leading to errors. This needs to be investigated and fixed.
*   OpenAI keys need to be rotated and new keys provided to contributors who need them for tools and integrations.
*   An inquiry was made about a Ruby project assigned by Shaw to someone on the team, to understand who is working on it.
*   A question was raised about the timeline for responses to applications for the Eliza OS Vault Hackathon at ETH Denver.

**Overall, the meeting focused on stabilizing the project after the Biome integration, improving code quality and contributor workflow, planning upcoming releases, and addressing security concerns. There was a positive outlook for the future of the Eliza OS framework, with excitement about upcoming changes and improvements.**
