---
title: "Weekly Contributor Meeting Notes"
date: 2025-01-14
description: "Addressing starter repo challenges, streamlining plugin development process, and improving CI/CD pipeline with Biome integration."
---

# Weekly Contributor Meeting Notes

(January 14, 2025 4:00 PM PST)

**Plugin Development & Organizational Structure**


## Summary

This was a weekly contributor meeting held in the ai16z Discord, primarily discussing development processes, issues, and organization within ElizaOS.

**1. Development and Engineering Concerns:**

*   **Linting and CI/CD Pipeline:** Contributors discussed challenges with the CI/CD pipeline and the integration of a new, faster linter called Biome to improve development time.
*   **YAML and GitHub Workflows:** A junior developer sought guidance on how to contribute to the project and learned about GitHub workflows and YAML files.
*   **Merge Conflicts and PR Management:** The team discussed tools and strategies for managing merge conflicts and pull requests (PRs), including a PR remake tool and a merge queue. The need for more eyeballs on PRs to maintain code quality and security was emphasized.
*   **Lock File Issues:** The team identified the lock file as a recurring point of contention in PRs and discussed potential solutions, such as auto-generation or ignoring it.
*   **Docker Integration:** They discussed using a Node.js container to improve build caching in GitHub actions and debated whether it was redundant with existing caching mechanisms. Progress on Docker Hub integration was mentioned.
*   **Starter Repo and Plugin Development:** A significant portion of the discussion revolved around the challenges posed by the starter repo. Contributors expressed concerns about its maintainability, the influx of issues related to it, and the confusion it caused for new developers, especially those trying to create plugins. They discussed strategies to streamline plugin development, such as disabling PRs on the starter repo, providing clearer guidelines, and potentially unifying the development process with the main repo.

**2. Organizational and Communication Issues:**

*   **Hiring and Roles:** The team touched upon ongoing discussions about hiring, defining roles and responsibilities, and determining salaries. They acknowledged the need for better internal communication and organization.
*   **Bounties:** The management of bounties was discussed, with concerns raised about the lack of a formal system, payment issues, and the need for a more structured process.
*   **Bug Squashing:** The idea of a dedicated bug squash team was proposed to address the backlog of issues and improve code quality.
*   **Product Management:** The team discussed the need for a dedicated product manager for ElizaOS and the role of the newly hired head of product. They explored using a Kanban board for issue tracking and potentially making it public.
*   **V1 vs. V2 Development:** There was a discussion about the balance between maintaining and improving V1 while also preparing for V2. The team acknowledged the need for a transition plan to migrate users and plugins to the new version.
*   **Community Plugins Repo:** The team debated the timing and strategy for moving plugins to a separate community repo, considering the potential workload of migrating existing plugins.

**3. Technical Discussions:**

*   **Security:** The importance of code security was highlighted, with mentions of ImmuneFi and the use of AI tools for identifying potential security issues.
*   **Testing:** The team discussed the need for better testing practices and the challenges of testing plugins in isolation versus within the larger system.
*   **ESM and Linting Configuration:** Contributors discussed issues related to differing ESM and linting configurations among developers and proposed solutions involving TrunkCheck and Biome.
*   **JSR:** The team considered publishing to JSR, a TypeScript-native package registry, in addition to NPM.
*   **Plugin Schema:** The idea of creating a schema for V2 plugins to standardize their structure and potentially automate the migration of V1 plugins was discussed.
*   **Action Naming:** The team recognized the potential for conflicts arising from multiple plugins using the same action names and the need to address this issue in the future.

**4. Action Items and Next Steps:**

*   **Improve Linting and CI/CD:** Continue working on integrating Biome and improving the CI/CD pipeline.
*   **Address Lock File Issues:** Explore solutions for managing the lock file more effectively.
*   **Streamline Plugin Development:** Disable PRs on the starter repo, provide clearer guidelines for plugin development, and potentially unify the process with the main repo.
*   **Formalize Bounty Process:** Develop a more structured system for managing bounties.
*   **Improve Documentation:** Create separate documentation tracks for users who want to simply use Eliza versus those who want to develop plugins.
*   **Evaluate V2 Plans:** Gain a better understanding of the V2 roadmap and timeline.
*   **Explore Plugin Schema:** Investigate the possibility of creating a schema for V2 plugins to facilitate migration and standardization.
*   **Review PRs and Test Develop:** Encourage contributors to review PRs and test the develop branch for issues.

The meeting concluded with a plan to continue working on these action items and revisit the discussion in the following week's meeting.
