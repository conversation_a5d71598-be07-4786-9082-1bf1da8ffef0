---
title: Contributing Guide
---

# Contributor Guide

Welcome! This document is designed to help you understand how you can be part of building the future of autonomous AI agents, regardless of your background or skillset.

---

## Technical Contributions

### For Developers

1. **Extend Eliza's Capabilities**

    - Develop new actions, evaluators, and providers
    - Improve existing components and modules

2. **Enhance Infrastructure**

    - Review open issues and submit PRs
    - Test and update documentation
    - Optimize performance
    - Improve deployment solutions

3. **Conduct Code Reviews**
    - Review pull requests from other contributors
    - Provide constructive feedback and suggestions
    - Help maintain code quality and consistency

### For Designers

1. **Improve User Experience**

    - Conduct user research and usability testing
    - Design intuitive user interfaces and interactions
    - Create high-fidelity mockups and prototypes

2. **Enhance Visual Design**
    - Develop a consistent visual language and style guide
    - Create engaging illustrations, icons, and graphics
    - Optimize designs for accessibility and inclusivity

---

## Non-Technical Contributions

### For Writers and Storytellers

1. **Craft Compelling Narratives**

    - Write blog posts, articles, and stories that communicate our vision
    - Develop characters and scenarios that showcase the potential of AI agents
    - Collaborate with artists to create immersive, multimedia experiences

2. **Improve Documentation**
    - Write clear, concise, and accessible documentation
    - Create tutorials, guides, and FAQs to help users get started
    - Provide examples and use cases to demonstrate <PERSON>'s capabilities

### For Artists and Creators

1. **Illustrate the Future**

    - Create concept art, illustrations, and infographics that bring our vision to life
    - Design characters, avatars, and virtual environments for AI agents
    - Experiment with new mediums and formats to communicate ideas

2. **Produce Multimedia Content**
    - Create videos, animations, and interactive experiences
    - Develop podcasts, interviews, and audio content
    - Collaborate with writers and developers to create integrated campaigns

### For Community Builders

1. **Foster Engagement**

    - Moderate forums, chat rooms, and social media channels
    - Organize events, meetups, and hackathons
    - Facilitate discussions and collaborations among contributors

2. **Provide Support**
    - Answer questions and provide guidance to new contributors
    - Triage issues and help prioritize bug fixes and feature requests
    - Act as a liaison between the community and the core development team
