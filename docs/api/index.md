# @elizaos/core v0.25.8

## Enumerations

- [GoalStatus](enumerations/GoalStatus.md)
- [ModelClass](enumerations/ModelClass.md)
- [ModelProviderName](enumerations/ModelProviderName.md)
- [CacheStore](enumerations/CacheStore.md)
- [IrysMessageType](enumerations/IrysMessageType.md)
- [IrysDataType](enumerations/IrysDataType.md)
- [ServiceType](enumerations/ServiceType.md)
- [LoggingLevel](enumerations/LoggingLevel.md)
- [TokenizerType](enumerations/TokenizerType.md)
- [TranscriptionProvider](enumerations/TranscriptionProvider.md)
- [ActionTimelineType](enumerations/ActionTimelineType.md)
- [KnowledgeScope](enumerations/KnowledgeScope.md)
- [CacheKeyPrefix](enumerations/CacheKeyPrefix.md)

## Classes

- [MemoryCacheAdapter](classes/MemoryCacheAdapter.md)
- [FsCacheAdapter](classes/FsCacheAdapter.md)
- [DbCacheAdapter](classes/DbCacheAdapter.md)
- [CacheManager](classes/CacheManager.md)
- [DatabaseAdapter](classes/DatabaseAdapter.md)
- [MemoryManager](classes/MemoryManager.md)
- [RAGKnowledgeManager](classes/RAGKnowledgeManager.md)
- [AgentRuntime](classes/AgentRuntime.md)
- [Service](classes/Service.md)

## Interfaces

- [ICacheAdapter](interfaces/ICacheAdapter.md)
- [GenerationOptions](interfaces/GenerationOptions.md)
- [Content](interfaces/Content.md)
- [ActionExample](interfaces/ActionExample.md)
- [ConversationExample](interfaces/ConversationExample.md)
- [Actor](interfaces/Actor.md)
- [Objective](interfaces/Objective.md)
- [Goal](interfaces/Goal.md)
- [State](interfaces/State.md)
- [Memory](interfaces/Memory.md)
- [MessageExample](interfaces/MessageExample.md)
- [Action](interfaces/Action.md)
- [EvaluationExample](interfaces/EvaluationExample.md)
- [Evaluator](interfaces/Evaluator.md)
- [Provider](interfaces/Provider.md)
- [Relationship](interfaces/Relationship.md)
- [Account](interfaces/Account.md)
- [Participant](interfaces/Participant.md)
- [Room](interfaces/Room.md)
- [IAgentConfig](interfaces/IAgentConfig.md)
- [ModelConfiguration](interfaces/ModelConfiguration.md)
- [TwitterSpaceDecisionOptions](interfaces/TwitterSpaceDecisionOptions.md)
- [IDatabaseAdapter](interfaces/IDatabaseAdapter.md)
- [IDatabaseCacheAdapter](interfaces/IDatabaseCacheAdapter.md)
- [IMemoryManager](interfaces/IMemoryManager.md)
- [IRAGKnowledgeManager](interfaces/IRAGKnowledgeManager.md)
- [ICacheManager](interfaces/ICacheManager.md)
- [IAgentRuntime](interfaces/IAgentRuntime.md)
- [IImageDescriptionService](interfaces/IImageDescriptionService.md)
- [ITranscriptionService](interfaces/ITranscriptionService.md)
- [IVideoService](interfaces/IVideoService.md)
- [ITextGenerationService](interfaces/ITextGenerationService.md)
- [IBrowserService](interfaces/IBrowserService.md)
- [ISpeechService](interfaces/ISpeechService.md)
- [IPdfService](interfaces/IPdfService.md)
- [IAwsS3Service](interfaces/IAwsS3Service.md)
- [UploadIrysResult](interfaces/UploadIrysResult.md)
- [DataIrysFetchedFromGQL](interfaces/DataIrysFetchedFromGQL.md)
- [GraphQLTag](interfaces/GraphQLTag.md)
- [IrysTimestamp](interfaces/IrysTimestamp.md)
- [IIrysService](interfaces/IIrysService.md)
- [ITeeLogService](interfaces/ITeeLogService.md)
- [RAGKnowledgeItem](interfaces/RAGKnowledgeItem.md)
- [ActionResponse](interfaces/ActionResponse.md)
- [ISlackService](interfaces/ISlackService.md)
- [DirectoryItem](interfaces/DirectoryItem.md)
- [ChunkRow](interfaces/ChunkRow.md)

## Type Aliases

- [EmbeddingProviderType](type-aliases/EmbeddingProviderType.md)
- [EmbeddingConfig](type-aliases/EmbeddingConfig.md)
- [EnvConfig](type-aliases/EnvConfig.md)
- [CharacterConfig](type-aliases/CharacterConfig.md)
- [UUID](type-aliases/UUID.md)
- [ModelSettings](type-aliases/ModelSettings.md)
- [ImageModelSettings](type-aliases/ImageModelSettings.md)
- [EmbeddingModelSettings](type-aliases/EmbeddingModelSettings.md)
- [Model](type-aliases/Model.md)
- [Models](type-aliases/Models.md)
- [Handler](type-aliases/Handler.md)
- [HandlerCallback](type-aliases/HandlerCallback.md)
- [Validator](type-aliases/Validator.md)
- [Media](type-aliases/Media.md)
- [ClientInstance](type-aliases/ClientInstance.md)
- [Client](type-aliases/Client.md)
- [Adapter](type-aliases/Adapter.md)
- [Plugin](type-aliases/Plugin.md)
- [TelemetrySettings](type-aliases/TelemetrySettings.md)
- [TemplateType](type-aliases/TemplateType.md)
- [Character](type-aliases/Character.md)
- [CacheOptions](type-aliases/CacheOptions.md)
- [KnowledgeItem](type-aliases/KnowledgeItem.md)

## Variables

- [EmbeddingProvider](variables/EmbeddingProvider.md)
- [envSchema](variables/envSchema.md)
- [CharacterSchema](variables/CharacterSchema.md)
- [evaluationTemplate](variables/evaluationTemplate.md)
- [knowledge](variables/knowledge.md)
- [elizaLogger](variables/elizaLogger.md)
- [models](variables/models.md)
- [messageCompletionFooter](variables/messageCompletionFooter.md)
- [shouldRespondFooter](variables/shouldRespondFooter.md)
- [booleanFooter](variables/booleanFooter.md)
- [stringArrayFooter](variables/stringArrayFooter.md)
- [postActionResponseFooter](variables/postActionResponseFooter.md)
- [settings](variables/settings.md)
- [uuidSchema](variables/uuidSchema.md)

## Functions

- [composeActionExamples](functions/composeActionExamples.md)
- [formatActionNames](functions/formatActionNames.md)
- [formatActions](functions/formatActions.md)
- [composeContext](functions/composeContext.md)
- [addHeader](functions/addHeader.md)
- [composeRandomUser](functions/composeRandomUser.md)
- [getEmbeddingConfig](functions/getEmbeddingConfig.md)
- [getEmbeddingType](functions/getEmbeddingType.md)
- [getEmbeddingZeroVector](functions/getEmbeddingZeroVector.md)
- [embed](functions/embed.md)
- [validateEnv](functions/validateEnv.md)
- [validateCharacterConfig](functions/validateCharacterConfig.md)
- [formatEvaluatorNames](functions/formatEvaluatorNames.md)
- [formatEvaluators](functions/formatEvaluators.md)
- [formatEvaluatorExamples](functions/formatEvaluatorExamples.md)
- [formatEvaluatorExampleDescriptions](functions/formatEvaluatorExampleDescriptions.md)
- [trimTokens](functions/trimTokens.md)
- [generateText](functions/generateText.md)
- [generateShouldRespond](functions/generateShouldRespond.md)
- [splitChunks](functions/splitChunks.md)
- [splitText](functions/splitText.md)
- [generateTrueOrFalse](functions/generateTrueOrFalse.md)
- [generateTextArray](functions/generateTextArray.md)
- [generateObjectDeprecated](functions/generateObjectDeprecated.md)
- [generateObjectArray](functions/generateObjectArray.md)
- [generateMessageResponse](functions/generateMessageResponse.md)
- [generateImage](functions/generateImage.md)
- [generateCaption](functions/generateCaption.md)
- [generateObject](functions/generateObject.md)
- [handleProvider](functions/handleProvider.md)
- [generateTweetActions](functions/generateTweetActions.md)
- [getGoals](functions/getGoals.md)
- [formatGoalsAsString](functions/formatGoalsAsString.md)
- [updateGoal](functions/updateGoal.md)
- [createGoal](functions/createGoal.md)
- [getActorDetails](functions/getActorDetails.md)
- [formatActors](functions/formatActors.md)
- [formatMessages](functions/formatMessages.md)
- [formatTimestamp](functions/formatTimestamp.md)
- [getModelSettings](functions/getModelSettings.md)
- [getImageModelSettings](functions/getImageModelSettings.md)
- [getEmbeddingModelSettings](functions/getEmbeddingModelSettings.md)
- [getEndpoint](functions/getEndpoint.md)
- [parseShouldRespondFromText](functions/parseShouldRespondFromText.md)
- [parseBooleanFromText](functions/parseBooleanFromText.md)
- [parseJsonArrayFromText](functions/parseJsonArrayFromText.md)
- [parseJSONObjectFromText](functions/parseJSONObjectFromText.md)
- [extractAttributes](functions/extractAttributes.md)
- [normalizeJsonString](functions/normalizeJsonString.md)
- [cleanJsonResponse](functions/cleanJsonResponse.md)
- [parseActionResponseFromText](functions/parseActionResponseFromText.md)
- [truncateToCompleteSentence](functions/truncateToCompleteSentence.md)
- [formatPosts](functions/formatPosts.md)
- [getProviders](functions/getProviders.md)
- [createRelationship](functions/createRelationship.md)
- [getRelationship](functions/getRelationship.md)
- [getRelationships](functions/getRelationships.md)
- [formatRelationships](functions/formatRelationships.md)
- [findNearestEnvFile](functions/findNearestEnvFile.md)
- [configureSettings](functions/configureSettings.md)
- [loadEnvConfig](functions/loadEnvConfig.md)
- [getEnvVariable](functions/getEnvVariable.md)
- [hasEnvVariable](functions/hasEnvVariable.md)
- [validateUuid](functions/validateUuid.md)
- [stringToUuid](functions/stringToUuid.md)
