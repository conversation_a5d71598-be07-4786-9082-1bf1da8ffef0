[@elizaos/core v0.25.8](../index.md) / HandlerCallback

# Type Alias: HandlerCallback()

> **HandlerCallback**: (`response`, `files`?) => `Promise`\<[`Memory`](../interfaces/Memory.md)[]\>

Callback function type for handlers

## Parameters

• **response**: [`Content`](../interfaces/Content.md)

• **files?**: `any`

## Returns

`Promise`\<[`Memory`](../interfaces/Memory.md)[]\>

## Defined in

[packages/core/src/types.ts:425](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L425)
