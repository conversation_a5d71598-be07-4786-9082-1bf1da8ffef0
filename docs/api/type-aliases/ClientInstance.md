[@elizaos/core v0.25.8](../index.md) / ClientInstance

# Type Alias: ClientInstance

> **ClientInstance**: `object`

Client instance

## Type declaration

### stop()

> **stop**: (`runtime`) => `Promise`\<`unknown`\>

Stop client connection

#### Parameters

• **runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

#### Returns

`Promise`\<`unknown`\>

## Defined in

[packages/core/src/types.ts:617](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L617)
