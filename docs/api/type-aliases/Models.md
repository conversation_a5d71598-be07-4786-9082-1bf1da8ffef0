[@elizaos/core v0.25.8](../index.md) / Models

# Type Alias: Models

> **Models**: `object`

Model configurations by provider

## Type declaration

### openai

> **openai**: [`Model`](Model.md)

### eternalai

> **eternalai**: [`Model`](Model.md)

### anthropic

> **anthropic**: [`Model`](Model.md)

### grok

> **grok**: [`Model`](Model.md)

### groq

> **groq**: [`Model`](Model.md)

### llama\_cloud

> **llama\_cloud**: [`Model`](Model.md)

### together

> **together**: [`Model`](Model.md)

### llama\_local

> **llama\_local**: [`Model`](Model.md)

### lmstudio

> **lmstudio**: [`Model`](Model.md)

### google

> **google**: [`Model`](Model.md)

### mistral

> **mistral**: [`Model`](Model.md)

### claude\_vertex

> **claude\_vertex**: [`Model`](Model.md)

### redpill

> **redpill**: [`Model`](Model.md)

### openrouter

> **openrouter**: [`Model`](Model.md)

### ollama

> **ollama**: [`Model`](Model.md)

### heurist

> **heurist**: [`Model`](Model.md)

### galadriel

> **galadriel**: [`Model`](Model.md)

### falai

> **falai**: [`Model`](Model.md)

### gaianet

> **gaianet**: [`Model`](Model.md)

### ali\_bailian

> **ali\_bailian**: [`Model`](Model.md)

### volengine

> **volengine**: [`Model`](Model.md)

### nanogpt

> **nanogpt**: [`Model`](Model.md)

### hyperbolic

> **hyperbolic**: [`Model`](Model.md)

### venice

> **venice**: [`Model`](Model.md)

### nvidia

> **nvidia**: [`Model`](Model.md)

### nineteen\_ai

> **nineteen\_ai**: [`Model`](Model.md)

### akash\_chat\_api

> **akash\_chat\_api**: [`Model`](Model.md)

### livepeer

> **livepeer**: [`Model`](Model.md)

### deepseek

> **deepseek**: [`Model`](Model.md)

### infera

> **infera**: [`Model`](Model.md)

### bedrock

> **bedrock**: [`Model`](Model.md)

### atoma

> **atoma**: [`Model`](Model.md)

### secret\_ai

> **secret\_ai**: [`Model`](Model.md)

### nearai

> **nearai**: [`Model`](Model.md)

## Defined in

[packages/core/src/types.ts:203](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L203)
