[@elizaos/core v0.25.8](../index.md) / Adapter

# Type Alias: Adapter

> **Adapter**: `object`

Database adapter initialization

## Type declaration

### init()

> **init**: (`runtime`) => [`IDatabaseAdapter`](../interfaces/IDatabaseAdapter.md) & [`IDatabaseCacheAdapter`](../interfaces/IDatabaseCacheAdapter.md)

Initialize the adapter

#### Parameters

• **runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

#### Returns

[`IDatabaseAdapter`](../interfaces/IDatabaseAdapter.md) & [`IDatabaseCacheAdapter`](../interfaces/IDatabaseCacheAdapter.md)

## Defined in

[packages/core/src/types.ts:642](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L642)
