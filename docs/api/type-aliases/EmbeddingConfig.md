[@elizaos/core v0.25.8](../index.md) / EmbeddingConfig

# Type Alias: EmbeddingConfig

> **EmbeddingConfig**: `object`

## Type declaration

### dimensions

> `readonly` **dimensions**: `number`

### model

> `readonly` **model**: `string`

### provider

> `readonly` **provider**: [`EmbeddingProviderType`](EmbeddingProviderType.md)

## Defined in

[packages/core/src/embedding.ts:28](https://github.com/elizaOS/eliza/blob/main/packages/core/src/embedding.ts#L28)
