[@elizaos/core v0.25.8](../index.md) / Media

# Type Alias: Media

> **Media**: `object`

Represents a media attachment

## Type declaration

### id

> **id**: `string`

Unique identifier

### url

> **url**: `string`

Media URL

### title

> **title**: `string`

Media title

### source

> **source**: `string`

Media source

### description

> **description**: `string`

Media description

### text

> **text**: `string`

Text content

### contentType?

> `optional` **contentType**: `string`

Content type

## Defined in

[packages/core/src/types.ts:591](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L591)
