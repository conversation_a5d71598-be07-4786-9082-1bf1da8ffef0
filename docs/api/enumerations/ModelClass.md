[@elizaos/core v0.25.8](../index.md) / ModelClass

# Enumeration: ModelClass

Model size/type classification

## Enumeration Members

### SMALL

> **SMALL**: `"small"`

#### Defined in

[packages/core/src/types.ts:132](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L132)

***

### MEDIUM

> **MEDIUM**: `"medium"`

#### Defined in

[packages/core/src/types.ts:133](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L133)

***

### LARGE

> **LARGE**: `"large"`

#### Defined in

[packages/core/src/types.ts:134](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L134)

***

### EMBEDDING

> **EMBEDDING**: `"embedding"`

#### Defined in

[packages/core/src/types.ts:135](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L135)

***

### IMAGE

> **IMAGE**: `"image"`

#### Defined in

[packages/core/src/types.ts:136](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L136)
