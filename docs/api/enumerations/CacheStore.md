[@elizaos/core v0.25.8](../index.md) / CacheStore

# Enumeration: CacheStore

## Enumeration Members

### REDIS

> **REDIS**: `"redis"`

#### Defined in

[packages/core/src/types.ts:1254](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1254)

***

### DATABASE

> **DATABASE**: `"database"`

#### Defined in

[packages/core/src/types.ts:1255](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1255)

***

### FILESYSTEM

> **FILESYSTEM**: `"filesystem"`

#### Defined in

[packages/core/src/types.ts:1256](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1256)
