[@elizaos/core v0.25.8](../index.md) / ModelProviderName

# Enumeration: ModelProviderName

Available model providers

## Enumeration Members

### OPENAI

> **OPENAI**: `"openai"`

#### Defined in

[packages/core/src/types.ts:244](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L244)

***

### ETERNALAI

> **ETERNALAI**: `"eternalai"`

#### Defined in

[packages/core/src/types.ts:245](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L245)

***

### ANTHROPIC

> **ANTHROPIC**: `"anthropic"`

#### Defined in

[packages/core/src/types.ts:246](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L246)

***

### GROK

> **GROK**: `"grok"`

#### Defined in

[packages/core/src/types.ts:247](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L247)

***

### GROQ

> **GROQ**: `"groq"`

#### Defined in

[packages/core/src/types.ts:248](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L248)

***

### LLAMACLOUD

> **LLAMACLOUD**: `"llama_cloud"`

#### Defined in

[packages/core/src/types.ts:249](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L249)

***

### TOGETHER

> **TOGETHER**: `"together"`

#### Defined in

[packages/core/src/types.ts:250](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L250)

***

### LLAMALOCAL

> **LLAMALOCAL**: `"llama_local"`

#### Defined in

[packages/core/src/types.ts:251](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L251)

***

### LMSTUDIO

> **LMSTUDIO**: `"lmstudio"`

#### Defined in

[packages/core/src/types.ts:252](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L252)

***

### GOOGLE

> **GOOGLE**: `"google"`

#### Defined in

[packages/core/src/types.ts:253](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L253)

***

### MISTRAL

> **MISTRAL**: `"mistral"`

#### Defined in

[packages/core/src/types.ts:254](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L254)

***

### CLAUDE\_VERTEX

> **CLAUDE\_VERTEX**: `"claude_vertex"`

#### Defined in

[packages/core/src/types.ts:255](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L255)

***

### REDPILL

> **REDPILL**: `"redpill"`

#### Defined in

[packages/core/src/types.ts:256](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L256)

***

### OPENROUTER

> **OPENROUTER**: `"openrouter"`

#### Defined in

[packages/core/src/types.ts:257](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L257)

***

### OLLAMA

> **OLLAMA**: `"ollama"`

#### Defined in

[packages/core/src/types.ts:258](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L258)

***

### HEURIST

> **HEURIST**: `"heurist"`

#### Defined in

[packages/core/src/types.ts:259](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L259)

***

### GALADRIEL

> **GALADRIEL**: `"galadriel"`

#### Defined in

[packages/core/src/types.ts:260](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L260)

***

### FAL

> **FAL**: `"falai"`

#### Defined in

[packages/core/src/types.ts:261](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L261)

***

### GAIANET

> **GAIANET**: `"gaianet"`

#### Defined in

[packages/core/src/types.ts:262](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L262)

***

### ALI\_BAILIAN

> **ALI\_BAILIAN**: `"ali_bailian"`

#### Defined in

[packages/core/src/types.ts:263](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L263)

***

### VOLENGINE

> **VOLENGINE**: `"volengine"`

#### Defined in

[packages/core/src/types.ts:264](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L264)

***

### NANOGPT

> **NANOGPT**: `"nanogpt"`

#### Defined in

[packages/core/src/types.ts:265](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L265)

***

### HYPERBOLIC

> **HYPERBOLIC**: `"hyperbolic"`

#### Defined in

[packages/core/src/types.ts:266](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L266)

***

### VENICE

> **VENICE**: `"venice"`

#### Defined in

[packages/core/src/types.ts:267](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L267)

***

### NVIDIA

> **NVIDIA**: `"nvidia"`

#### Defined in

[packages/core/src/types.ts:268](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L268)

***

### NINETEEN\_AI

> **NINETEEN\_AI**: `"nineteen_ai"`

#### Defined in

[packages/core/src/types.ts:269](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L269)

***

### AKASH\_CHAT\_API

> **AKASH\_CHAT\_API**: `"akash_chat_api"`

#### Defined in

[packages/core/src/types.ts:270](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L270)

***

### LIVEPEER

> **LIVEPEER**: `"livepeer"`

#### Defined in

[packages/core/src/types.ts:271](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L271)

***

### LETZAI

> **LETZAI**: `"letzai"`

#### Defined in

[packages/core/src/types.ts:272](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L272)

***

### DEEPSEEK

> **DEEPSEEK**: `"deepseek"`

#### Defined in

[packages/core/src/types.ts:273](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L273)

***

### INFERA

> **INFERA**: `"infera"`

#### Defined in

[packages/core/src/types.ts:274](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L274)

***

### BEDROCK

> **BEDROCK**: `"bedrock"`

#### Defined in

[packages/core/src/types.ts:275](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L275)

***

### ATOMA

> **ATOMA**: `"atoma"`

#### Defined in

[packages/core/src/types.ts:276](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L276)

***

### SECRETAI

> **SECRETAI**: `"secret_ai"`

#### Defined in

[packages/core/src/types.ts:277](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L277)

***

### NEARAI

> **NEARAI**: `"nearai"`

#### Defined in

[packages/core/src/types.ts:278](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L278)
