[@elizaos/core v0.25.8](../index.md) / GoalStatus

# Enumeration: GoalStatus

Status enum for goals

## Enumeration Members

### DONE

> **DONE**: `"DONE"`

#### Defined in

[packages/core/src/types.ts:100](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L100)

***

### FAILED

> **FAILED**: `"FAILED"`

#### Defined in

[packages/core/src/types.ts:101](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L101)

***

### IN\_PROGRESS

> **IN\_PROGRESS**: `"IN_PROGRESS"`

#### Defined in

[packages/core/src/types.ts:102](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L102)
