[@elizaos/core v0.25.8](../index.md) / ServiceType

# Enumeration: ServiceType

## Enumeration Members

### IMAGE\_DESCRIPTION

> **IMAGE\_DESCRIPTION**: `"image_description"`

#### Defined in

[packages/core/src/types.ts:1525](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1525)

***

### TRANSCRIPTION

> **TRANSCRIPTION**: `"transcription"`

#### Defined in

[packages/core/src/types.ts:1526](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1526)

***

### VIDEO

> **VIDEO**: `"video"`

#### Defined in

[packages/core/src/types.ts:1527](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1527)

***

### TEXT\_GENERATION

> **TEXT\_GENERATION**: `"text_generation"`

#### Defined in

[packages/core/src/types.ts:1528](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1528)

***

### BROWSER

> **BROWSER**: `"browser"`

#### Defined in

[packages/core/src/types.ts:1529](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1529)

***

### SPEECH\_GENERATION

> **SPEECH\_GENERATION**: `"speech_generation"`

#### Defined in

[packages/core/src/types.ts:1530](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1530)

***

### PDF

> **PDF**: `"pdf"`

#### Defined in

[packages/core/src/types.ts:1531](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1531)

***

### INTIFACE

> **INTIFACE**: `"intiface"`

#### Defined in

[packages/core/src/types.ts:1532](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1532)

***

### AWS\_S3

> **AWS\_S3**: `"aws_s3"`

#### Defined in

[packages/core/src/types.ts:1533](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1533)

***

### BUTTPLUG

> **BUTTPLUG**: `"buttplug"`

#### Defined in

[packages/core/src/types.ts:1534](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1534)

***

### SLACK

> **SLACK**: `"slack"`

#### Defined in

[packages/core/src/types.ts:1535](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1535)

***

### VERIFIABLE\_LOGGING

> **VERIFIABLE\_LOGGING**: `"verifiable_logging"`

#### Defined in

[packages/core/src/types.ts:1536](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1536)

***

### IRYS

> **IRYS**: `"irys"`

#### Defined in

[packages/core/src/types.ts:1537](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1537)

***

### TEE\_LOG

> **TEE\_LOG**: `"tee_log"`

#### Defined in

[packages/core/src/types.ts:1538](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1538)

***

### GOPLUS\_SECURITY

> **GOPLUS\_SECURITY**: `"goplus_security"`

#### Defined in

[packages/core/src/types.ts:1539](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1539)

***

### WEB\_SEARCH

> **WEB\_SEARCH**: `"web_search"`

#### Defined in

[packages/core/src/types.ts:1540](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1540)

***

### EMAIL\_AUTOMATION

> **EMAIL\_AUTOMATION**: `"email_automation"`

#### Defined in

[packages/core/src/types.ts:1541](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1541)

***

### NKN\_CLIENT\_SERVICE

> **NKN\_CLIENT\_SERVICE**: `"nkn_client_service"`

#### Defined in

[packages/core/src/types.ts:1542](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1542)
