[@elizaos/core v0.25.8](../index.md) / GraphQLTag

# Interface: GraphQLTag

## Properties

### name

> **name**: `string`

#### Defined in

[packages/core/src/types.ts:1467](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1467)

***

### values

> **values**: `any`[]

#### Defined in

[packages/core/src/types.ts:1468](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1468)
