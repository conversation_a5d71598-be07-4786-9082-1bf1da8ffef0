[@elizaos/core v0.25.8](../index.md) / Account

# Interface: Account

Represents a user account

## Properties

### id

> **id**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Unique identifier

#### Defined in

[packages/core/src/types.ts:548](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L548)

***

### name

> **name**: `string`

Display name

#### Defined in

[packages/core/src/types.ts:551](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L551)

***

### username

> **username**: `string`

Username

#### Defined in

[packages/core/src/types.ts:554](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L554)

***

### details?

> `optional` **details**: `object`

Optional additional details

#### Index Signature

 \[`key`: `string`\]: `any`

#### Defined in

[packages/core/src/types.ts:557](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L557)

***

### email?

> `optional` **email**: `string`

Optional email

#### Defined in

[packages/core/src/types.ts:560](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L560)

***

### avatarUrl?

> `optional` **avatarUrl**: `string`

Optional avatar URL

#### Defined in

[packages/core/src/types.ts:563](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L563)
