[@elizaos/core v0.25.8](../index.md) / EvaluationExample

# Interface: EvaluationExample

Example for evaluating agent behavior

## Properties

### context

> **context**: `string`

Evaluation context

#### Defined in

[packages/core/src/types.ts:470](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L470)

***

### messages

> **messages**: [`ActionExample`](ActionExample.md)[]

Example messages

#### Defined in

[packages/core/src/types.ts:473](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L473)

***

### outcome

> **outcome**: `string`

Expected outcome

#### Defined in

[packages/core/src/types.ts:476](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L476)
