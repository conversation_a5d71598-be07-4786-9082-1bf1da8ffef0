[@elizaos/core v0.25.8](../index.md) / Relationship

# Interface: Relationship

Represents a relationship between users

## Properties

### id

> **id**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Unique identifier

#### Defined in

[packages/core/src/types.ts:522](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L522)

***

### userA

> **userA**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

First user ID

#### Defined in

[packages/core/src/types.ts:525](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L525)

***

### userB

> **userB**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Second user ID

#### Defined in

[packages/core/src/types.ts:528](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L528)

***

### userId

> **userId**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Primary user ID

#### Defined in

[packages/core/src/types.ts:531](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L531)

***

### roomId

> **roomId**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Associated room ID

#### Defined in

[packages/core/src/types.ts:534](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L534)

***

### status

> **status**: `string`

Relationship status

#### Defined in

[packages/core/src/types.ts:537](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L537)

***

### createdAt?

> `optional` **createdAt**: `string`

Optional creation timestamp

#### Defined in

[packages/core/src/types.ts:540](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L540)
