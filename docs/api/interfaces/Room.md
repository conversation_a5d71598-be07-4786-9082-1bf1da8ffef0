[@elizaos/core v0.25.8](../index.md) / Room

# Interface: Room

Represents a conversation room

## Properties

### id

> **id**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Unique identifier

#### Defined in

[packages/core/src/types.ts:582](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L582)

***

### participants

> **participants**: [`Participant`](Participant.md)[]

Room participants

#### Defined in

[packages/core/src/types.ts:585](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L585)
