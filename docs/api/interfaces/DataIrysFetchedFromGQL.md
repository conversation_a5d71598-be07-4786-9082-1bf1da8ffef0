[@elizaos/core v0.25.8](../index.md) / DataIrysFetchedFromGQL

# Interface: DataIrysFetchedFromGQL

## Properties

### success

> **success**: `boolean`

#### Defined in

[packages/core/src/types.ts:1461](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1461)

***

### data

> **data**: `any`

#### Defined in

[packages/core/src/types.ts:1462](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1462)

***

### error?

> `optional` **error**: `string`

#### Defined in

[packages/core/src/types.ts:1463](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1463)
