[@elizaos/core v0.25.8](../index.md) / ConversationExample

# Interface: ConversationExample

Example conversation content with user ID

## Properties

### userId

> **userId**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

UUID of user in conversation

#### Defined in

[packages/core/src/types.ts:50](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L50)

***

### content

> **content**: [`Content`](Content.md)

Content of the conversation

#### Defined in

[packages/core/src/types.ts:53](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L53)
