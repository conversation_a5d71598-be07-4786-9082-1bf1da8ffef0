[@elizaos/core v0.25.8](../index.md) / TwitterSpaceDecisionOptions

# Interface: TwitterSpaceDecisionOptions

## Properties

### maxSpeakers?

> `optional` **maxSpeakers**: `number`

#### Defined in

[packages/core/src/types.ts:968](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L968)

***

### topics?

> `optional` **topics**: `string`[]

#### Defined in

[packages/core/src/types.ts:969](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L969)

***

### typicalDurationMinutes?

> `optional` **typicalDurationMinutes**: `number`

#### Defined in

[packages/core/src/types.ts:970](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L970)

***

### idleKickTimeoutMs?

> `optional` **idleKickTimeoutMs**: `number`

#### Defined in

[packages/core/src/types.ts:971](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L971)

***

### minIntervalBetweenSpacesMinutes?

> `optional` **minIntervalBetweenSpacesMinutes**: `number`

#### Defined in

[packages/core/src/types.ts:972](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L972)

***

### businessHoursOnly?

> `optional` **businessHoursOnly**: `boolean`

#### Defined in

[packages/core/src/types.ts:973](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L973)

***

### randomChance?

> `optional` **randomChance**: `number`

#### Defined in

[packages/core/src/types.ts:974](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L974)

***

### enableIdleMonitor?

> `optional` **enableIdleMonitor**: `boolean`

#### Defined in

[packages/core/src/types.ts:975](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L975)

***

### enableSttTts?

> `optional` **enableSttTts**: `boolean`

#### Defined in

[packages/core/src/types.ts:976](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L976)

***

### enableRecording?

> `optional` **enableRecording**: `boolean`

#### Defined in

[packages/core/src/types.ts:977](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L977)

***

### voiceId?

> `optional` **voiceId**: `string`

#### Defined in

[packages/core/src/types.ts:978](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L978)

***

### sttLanguage?

> `optional` **sttLanguage**: `string`

#### Defined in

[packages/core/src/types.ts:979](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L979)

***

### speakerMaxDurationMs?

> `optional` **speakerMaxDurationMs**: `number`

#### Defined in

[packages/core/src/types.ts:980](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L980)
