[@elizaos/core v0.25.8](../index.md) / Participant

# Interface: Participant

Room participant with account details

## Properties

### id

> **id**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Unique identifier

#### Defined in

[packages/core/src/types.ts:571](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L571)

***

### account

> **account**: [`Account`](Account.md)

Associated account

#### Defined in

[packages/core/src/types.ts:574](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L574)
