[@elizaos/core v0.25.8](../index.md) / Objective

# Interface: Objective

Represents a single objective within a goal

## Properties

### id?

> `optional` **id**: `string`

Optional unique identifier

#### Defined in

[packages/core/src/types.ts:87](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L87)

***

### description

> **description**: `string`

Description of what needs to be achieved

#### Defined in

[packages/core/src/types.ts:90](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L90)

***

### completed

> **completed**: `boolean`

Whether objective is completed

#### Defined in

[packages/core/src/types.ts:93](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L93)
