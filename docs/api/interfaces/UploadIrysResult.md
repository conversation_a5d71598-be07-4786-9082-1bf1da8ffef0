[@elizaos/core v0.25.8](../index.md) / UploadIrysResult

# Interface: UploadIrysResult

## Properties

### success

> **success**: `boolean`

#### Defined in

[packages/core/src/types.ts:1454](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1454)

***

### url?

> `optional` **url**: `string`

#### Defined in

[packages/core/src/types.ts:1455](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1455)

***

### error?

> `optional` **error**: `string`

#### Defined in

[packages/core/src/types.ts:1456](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1456)

***

### data?

> `optional` **data**: `any`

#### Defined in

[packages/core/src/types.ts:1457](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L1457)
