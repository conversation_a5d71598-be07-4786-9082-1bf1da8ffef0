[@elizaos/core v0.25.8](../index.md) / Actor

# Interface: Actor

Represents an actor/participant in a conversation

## Properties

### name

> **name**: `string`

Display name

#### Defined in

[packages/core/src/types.ts:61](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L61)

***

### username

> **username**: `string`

Username/handle

#### Defined in

[packages/core/src/types.ts:64](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L64)

***

### details

> **details**: `object`

Additional profile details

#### tagline

> **tagline**: `string`

Short profile tagline

#### summary

> **summary**: `string`

Longer profile summary

#### quote

> **quote**: `string`

Favorite quote

#### Defined in

[packages/core/src/types.ts:67](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L67)

***

### id

> **id**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

Unique identifier

#### Defined in

[packages/core/src/types.ts:79](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L79)
