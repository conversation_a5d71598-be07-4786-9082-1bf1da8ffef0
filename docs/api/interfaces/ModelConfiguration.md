[@elizaos/core v0.25.8](../index.md) / ModelConfiguration

# Interface: ModelConfiguration

## Properties

### temperature?

> `optional` **temperature**: `number`

#### Defined in

[packages/core/src/types.ts:715](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L715)

***

### maxOutputTokens?

> `optional` **maxOutputTokens**: `number`

#### Defined in

[packages/core/src/types.ts:716](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L716)

***

### frequency\_penalty?

> `optional` **frequency\_penalty**: `number`

#### Defined in

[packages/core/src/types.ts:717](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L717)

***

### presence\_penalty?

> `optional` **presence\_penalty**: `number`

#### Defined in

[packages/core/src/types.ts:718](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L718)

***

### maxInputTokens?

> `optional` **maxInputTokens**: `number`

#### Defined in

[packages/core/src/types.ts:719](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L719)

***

### experimental\_telemetry?

> `optional` **experimental\_telemetry**: [`TelemetrySettings`](../type-aliases/TelemetrySettings.md)

#### Defined in

[packages/core/src/types.ts:720](https://github.com/elizaOS/eliza/blob/main/packages/core/src/types.ts#L720)
