[@elizaos/core v0.25.8](../index.md) / formatMessages

# Function: formatMessages()

> **formatMessages**(`__namedParameters`): `string`

Format messages into a string

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.messages**: [`Memory`](../interfaces/Memory.md)[]

• **\_\_namedParameters.actors**: [`Actor`](../interfaces/Actor.md)[]

## Returns

`string`

string

## Defined in

[packages/core/src/messages.ts:60](https://github.com/elizaOS/eliza/blob/main/packages/core/src/messages.ts#L60)
