[@elizaos/core v0.25.8](../index.md) / getEmbeddingModelSettings

# Function: getEmbeddingModelSettings()

> **getEmbeddingModelSettings**(`provider`): [`EmbeddingModelSettings`](../type-aliases/EmbeddingModelSettings.md) \| `undefined`

## Parameters

• **provider**: [`ModelProviderName`](../enumerations/ModelProviderName.md)

## Returns

[`EmbeddingModelSettings`](../type-aliases/EmbeddingModelSettings.md) \| `undefined`

## Defined in

[packages/core/src/models.ts:1241](https://github.com/elizaOS/eliza/blob/main/packages/core/src/models.ts#L1241)
