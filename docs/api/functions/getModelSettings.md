[@elizaos/core v0.25.8](../index.md) / getModelSettings

# Function: getModelSettings()

> **getModelSettings**(`provider`, `type`): [`ModelSettings`](../type-aliases/ModelSettings.md) \| `undefined`

## Parameters

• **provider**: [`ModelProviderName`](../enumerations/ModelProviderName.md)

• **type**: [`ModelClass`](../enumerations/ModelClass.md)

## Returns

[`ModelSettings`](../type-aliases/ModelSettings.md) \| `undefined`

## Defined in

[packages/core/src/models.ts:1226](https://github.com/elizaOS/eliza/blob/main/packages/core/src/models.ts#L1226)
