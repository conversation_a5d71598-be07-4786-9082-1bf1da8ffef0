[@elizaos/core v0.25.8](../index.md) / generateCaption

# Function: generateCaption()

> **generateCaption**(`data`, `runtime`): `Promise`\<`object`\>

## Parameters

• **data**

• **data.imageUrl**: `string`

• **runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

## Returns

`Promise`\<`object`\>

### title

> **title**: `string`

### description

> **description**: `string`

## Defined in

[packages/core/src/generation.ts:2103](https://github.com/elizaOS/eliza/blob/main/packages/core/src/generation.ts#L2103)
