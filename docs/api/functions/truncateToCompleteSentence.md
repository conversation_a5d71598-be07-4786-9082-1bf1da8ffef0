[@elizaos/core v0.25.8](../index.md) / truncateToCompleteSentence

# Function: truncateToCompleteSentence()

> **truncateToCompleteSentence**(`text`, `maxLength`): `string`

Truncate text to fit within the character limit, ensuring it ends at a complete sentence.

## Parameters

• **text**: `string`

• **maxLength**: `number`

## Returns

`string`

## Defined in

[packages/core/src/parsing.ts:317](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L317)
