[@elizaos/core v0.25.8](../index.md) / configureSettings

# Function: configureSettings()

> **configureSettings**(`settings`): `void`

Configures environment settings for browser usage

## Parameters

• **settings**: `Settings`

Object containing environment variables

## Returns

`void`

## Defined in

[packages/core/src/settings.ts:73](https://github.com/elizaOS/eliza/blob/main/packages/core/src/settings.ts#L73)
