[@elizaos/core v0.25.8](../index.md) / parseShouldRespondFromText

# Function: parseShouldRespondFromText()

> **parseShouldRespondFromText**(`text`): `"RESPOND"` \| `"IGNORE"` \| `"STOP"`

## Parameters

• **text**: `string`

## Returns

`"RESPOND"` \| `"IGNORE"` \| `"STOP"`

## Defined in

[packages/core/src/parsing.ts:17](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L17)
