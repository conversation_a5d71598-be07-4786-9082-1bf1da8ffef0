[@elizaos/core v0.25.8](../index.md) / parseActionResponseFromText

# Function: parseActionResponseFromText()

> **parseActionResponseFromText**(`text`): `object`

## Parameters

• **text**: `string`

## Returns

`object`

### actions

> **actions**: [`ActionResponse`](../interfaces/ActionResponse.md)

## Defined in

[packages/core/src/parsing.ts:279](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L279)
