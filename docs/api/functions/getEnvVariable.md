[@elizaos/core v0.25.8](../index.md) / getEnvVariable

# Function: getEnvVariable()

> **getEnvVariable**(`key`, `defaultValue`?): `string` \| `undefined`

Gets a specific environment variable

## Parameters

• **key**: `string`

The environment variable key

• **defaultValue?**: `string`

Optional default value if key doesn't exist

## Returns

`string` \| `undefined`

The environment variable value or default value

## Defined in

[packages/core/src/settings.ts:116](https://github.com/elizaOS/eliza/blob/main/packages/core/src/settings.ts#L116)
