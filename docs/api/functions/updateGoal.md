[@elizaos/core v0.25.8](../index.md) / updateGoal

# Function: updateGoal()

> **updateGoal**(`__namedParameters`): `Promise`\<`void`\>

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.goal**: [`Goal`](../interfaces/Goal.md)

## Returns

`Promise`\<`void`\>

## Defined in

[packages/core/src/goals.ts:45](https://github.com/elizaOS/eliza/blob/main/packages/core/src/goals.ts#L45)
