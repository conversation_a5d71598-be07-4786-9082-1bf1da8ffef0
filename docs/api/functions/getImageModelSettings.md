[@elizaos/core v0.25.8](../index.md) / getImageModelSettings

# Function: getImageModelSettings()

> **getImageModelSettings**(`provider`): [`ImageModelSettings`](../type-aliases/ImageModelSettings.md) \| `undefined`

## Parameters

• **provider**: [`ModelProviderName`](../enumerations/ModelProviderName.md)

## Returns

[`ImageModelSettings`](../type-aliases/ImageModelSettings.md) \| `undefined`

## Defined in

[packages/core/src/models.ts:1233](https://github.com/elizaOS/eliza/blob/main/packages/core/src/models.ts#L1233)
