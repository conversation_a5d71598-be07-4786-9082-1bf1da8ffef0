[@elizaos/core v0.25.8](../index.md) / formatEvaluatorExamples

# Function: formatEvaluatorExamples()

> **formatEvaluatorExamples**(`evaluators`): `string`

Formats evaluator examples into a readable string, replacing placeholders with generated names.

## Parameters

• **evaluators**: [`Evaluator`](../interfaces/Evaluator.md)[]

An array of evaluator objects, each containing examples to format.

## Returns

`string`

A string that presents each evaluator example in a structured format, including context, messages, and outcomes, with placeholders replaced by generated names.

## Defined in

[packages/core/src/evaluators.ts:55](https://github.com/elizaOS/eliza/blob/main/packages/core/src/evaluators.ts#L55)
