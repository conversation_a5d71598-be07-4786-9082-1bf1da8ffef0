[@elizaos/core v0.25.8](../index.md) / createRelationship

# Function: createRelationship()

> **createRelationship**(`__namedParameters`): `Promise`\<`boolean`\>

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.userA**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

• **\_\_namedParameters.userB**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

## Returns

`Promise`\<`boolean`\>

## Defined in

[packages/core/src/relationships.ts:3](https://github.com/elizaOS/eliza/blob/main/packages/core/src/relationships.ts#L3)
