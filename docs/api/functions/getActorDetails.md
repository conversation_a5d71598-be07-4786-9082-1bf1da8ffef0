[@elizaos/core v0.25.8](../index.md) / getActorDetails

# Function: getActorDetails()

> **getActorDetails**(`__namedParameters`): `Promise`\<[`Actor`](../interfaces/Actor.md)[]\>

Get details for a list of actors.

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.roomId**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

## Returns

`Promise`\<[`Actor`](../interfaces/Actor.md)[]\>

## Defined in

[packages/core/src/messages.ts:12](https://github.com/elizaOS/eliza/blob/main/packages/core/src/messages.ts#L12)
