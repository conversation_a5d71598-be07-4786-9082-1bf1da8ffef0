[@elizaos/core v0.25.8](../index.md) / handleProvider

# Function: handleProvider()

> **handleProvider**(`options`): `Promise`\<`GenerationResult`\>

Handles AI generation based on the specified provider.

## Parameters

• **options**: `ProviderOptions`

Configuration options specific to the provider.

## Returns

`Promise`\<`GenerationResult`\>

- A promise that resolves to an array of generated objects.

## Defined in

[packages/core/src/generation.ts:2238](https://github.com/elizaOS/eliza/blob/main/packages/core/src/generation.ts#L2238)
