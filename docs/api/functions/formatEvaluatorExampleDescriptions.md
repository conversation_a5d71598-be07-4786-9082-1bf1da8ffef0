[@elizaos/core v0.25.8](../index.md) / formatEvaluatorExampleDescriptions

# Function: formatEvaluatorExampleDescriptions()

> **formatEvaluatorExampleDescriptions**(`evaluators`): `string`

Generates a string summarizing the descriptions of each evaluator example.

## Parameters

• **evaluators**: [`Evaluator`](../interfaces/Evaluator.md)[]

An array of evaluator objects, each containing examples.

## Returns

`string`

A string that summarizes the descriptions for each evaluator example, formatted with the evaluator name, example number, and description.

## Defined in

[packages/core/src/evaluators.ts:110](https://github.com/elizaOS/eliza/blob/main/packages/core/src/evaluators.ts#L110)
