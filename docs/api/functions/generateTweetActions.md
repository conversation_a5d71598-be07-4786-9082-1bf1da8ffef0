[@elizaos/core v0.25.8](../index.md) / generateTweetActions

# Function: generateTweetActions()

> **generateTweetActions**(`__namedParameters`): `Promise`\<[`ActionResponse`](../interfaces/ActionResponse.md) \| `null`\>

doesn't belong here

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.context**: `string`

• **\_\_namedParameters.modelClass**: [`ModelClass`](../enumerations/ModelClass.md)

## Returns

`Promise`\<[`ActionResponse`](../interfaces/ActionResponse.md) \| `null`\>

## Defined in

[packages/core/src/generation.ts:2757](https://github.com/elizaOS/eliza/blob/main/packages/core/src/generation.ts#L2757)
