[@elizaos/core v0.25.8](../index.md) / getRelationships

# Function: getRelationships()

> **getRelationships**(`__namedParameters`): `Promise`\<[`Relationship`](../interfaces/Relationship.md)[]\>

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.userId**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

## Returns

`Promise`\<[`Relationship`](../interfaces/Relationship.md)[]\>

## Defined in

[packages/core/src/relationships.ts:33](https://github.com/elizaOS/eliza/blob/main/packages/core/src/relationships.ts#L33)
