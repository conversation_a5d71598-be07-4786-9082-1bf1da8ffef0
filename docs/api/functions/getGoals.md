[@elizaos/core v0.25.8](../index.md) / getGoals

# Function: getGoals()

> **getGoals**(`__namedParameters`): `Promise`\<[`Goal`](../interfaces/Goal.md)[]\>

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.roomId**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

• **\_\_namedParameters.userId?**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

• **\_\_namedParameters.onlyInProgress?**: `boolean` = `true`

• **\_\_namedParameters.count?**: `number` = `5`

## Returns

`Promise`\<[`Goal`](../interfaces/Goal.md)[]\>

## Defined in

[packages/core/src/goals.ts:8](https://github.com/elizaOS/eliza/blob/main/packages/core/src/goals.ts#L8)
