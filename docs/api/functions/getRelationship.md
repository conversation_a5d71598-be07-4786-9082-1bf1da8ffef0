[@elizaos/core v0.25.8](../index.md) / getRelationship

# Function: getRelationship()

> **getRelationship**(`__namedParameters`): `Promise`\<[`Relationship`](../interfaces/Relationship.md)\>

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.userA**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

• **\_\_namedParameters.userB**: \`$\{string\}-$\{string\}-$\{string\}-$\{string\}-$\{string\}\`

## Returns

`Promise`\<[`Relationship`](../interfaces/Relationship.md)\>

## Defined in

[packages/core/src/relationships.ts:18](https://github.com/elizaOS/eliza/blob/main/packages/core/src/relationships.ts#L18)
