[@elizaos/core v0.25.8](../index.md) / formatEvaluatorNames

# Function: formatEvaluatorNames()

> **formatEvaluatorNames**(`evaluators`): `string`

Formats the names of evaluators into a comma-separated list, each enclosed in single quotes.

## Parameters

• **evaluators**: [`Evaluator`](../interfaces/Evaluator.md)[]

An array of evaluator objects.

## Returns

`string`

A string that concatenates the names of all evaluators, each enclosed in single quotes and separated by commas.

## Defined in

[packages/core/src/evaluators.ts:30](https://github.com/elizaOS/eliza/blob/main/packages/core/src/evaluators.ts#L30)
