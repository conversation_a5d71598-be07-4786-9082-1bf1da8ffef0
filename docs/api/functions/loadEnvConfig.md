[@elizaos/core v0.25.8](../index.md) / loadEnvConfig

# Function: loadEnvConfig()

> **loadEnvConfig**(): `Settings`

Loads environment variables from the nearest .env file in Node.js
or returns configured settings in browser

## Returns

`Settings`

Environment variables object

## Throws

If no .env file is found in Node.js environment

## Defined in

[packages/core/src/settings.ts:83](https://github.com/elizaOS/eliza/blob/main/packages/core/src/settings.ts#L83)
