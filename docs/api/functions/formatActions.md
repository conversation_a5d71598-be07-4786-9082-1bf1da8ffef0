[@elizaos/core v0.25.8](../index.md) / formatActions

# Function: formatActions()

> **formatActions**(`actions`): `string`

Formats the provided actions into a detailed string listing each action's name and description, separated by commas and newlines.

## Parameters

• **actions**: [`Action`](../interfaces/Action.md)[]

An array of `Action` objects to format.

## Returns

`string`

A detailed string of actions, including names and descriptions.

## Defined in

[packages/core/src/actions.ts:73](https://github.com/elizaOS/eliza/blob/main/packages/core/src/actions.ts#L73)
