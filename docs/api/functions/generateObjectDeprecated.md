[@elizaos/core v0.25.8](../index.md) / generateObjectDeprecated

# Function: generateObjectDeprecated()

> **generateObjectDeprecated**(`__namedParameters`): `Promise`\<`any`\>

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.runtime**: [`IAgentRuntime`](../interfaces/IAgentRuntime.md)

• **\_\_namedParameters.context**: `string`

• **\_\_namedParameters.modelClass**: [`ModelClass`](../enumerations/ModelClass.md)

## Returns

`Promise`\<`any`\>

## Defined in

[packages/core/src/generation.ts:1577](https://github.com/elizaOS/eliza/blob/main/packages/core/src/generation.ts#L1577)
