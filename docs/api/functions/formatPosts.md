[@elizaos/core v0.25.8](../index.md) / formatPosts

# Function: formatPosts()

> **formatPosts**(`__namedParameters`): `string`

## Parameters

• **\_\_namedParameters**

• **\_\_namedParameters.messages**: [`Memory`](../interfaces/Memory.md)[]

• **\_\_namedParameters.actors**: [`Actor`](../interfaces/Actor.md)[]

• **\_\_namedParameters.conversationHeader?**: `boolean` = `true`

## Returns

`string`

## Defined in

[packages/core/src/posts.ts:4](https://github.com/elizaOS/eliza/blob/main/packages/core/src/posts.ts#L4)
