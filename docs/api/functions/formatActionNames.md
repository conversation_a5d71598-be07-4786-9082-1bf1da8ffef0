[@elizaos/core v0.25.8](../index.md) / formatActionNames

# Function: formatActionNames()

> **formatActionNames**(`actions`): `string`

Formats the names of the provided actions into a comma-separated string.

## Parameters

• **actions**: [`Action`](../interfaces/Action.md)[]

An array of `Action` objects from which to extract names.

## Returns

`string`

A comma-separated string of action names.

## Defined in

[packages/core/src/actions.ts:61](https://github.com/elizaOS/eliza/blob/main/packages/core/src/actions.ts#L61)
