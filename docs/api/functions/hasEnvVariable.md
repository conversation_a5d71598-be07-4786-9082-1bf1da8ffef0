[@elizaos/core v0.25.8](../index.md) / hasEnvVariable

# Function: hasEnvVariable()

> **hasEnvVariable**(`key`): `boolean`

Checks if a specific environment variable exists

## Parameters

• **key**: `string`

The environment variable key

## Returns

`boolean`

True if the environment variable exists

## Defined in

[packages/core/src/settings.ts:131](https://github.com/elizaOS/eliza/blob/main/packages/core/src/settings.ts#L131)
