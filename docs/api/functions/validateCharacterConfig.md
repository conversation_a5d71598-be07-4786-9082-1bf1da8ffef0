[@elizaos/core v0.25.8](../index.md) / validateCharacterConfig

# Function: validateCharacterConfig()

> **validateCharacterConfig**(`json`): [`CharacterConfig`](../type-aliases/CharacterConfig.md)

Validation function

## Parameters

• **json**: `unknown`

## Returns

[`CharacterConfig`](../type-aliases/CharacterConfig.md)

## Defined in

[packages/core/src/environment.ts:160](https://github.com/elizaOS/eliza/blob/main/packages/core/src/environment.ts#L160)
