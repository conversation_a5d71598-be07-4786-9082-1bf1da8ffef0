[@elizaos/core v0.25.8](../index.md) / formatEvaluators

# Function: formatEvaluators()

> **formatEvaluators**(`evaluators`): `string`

Formats evaluator details into a string, including both the name and description of each evaluator.

## Parameters

• **evaluators**: [`Evaluator`](../interfaces/Evaluator.md)[]

An array of evaluator objects.

## Returns

`string`

A string that concatenates the name and description of each evaluator, separated by a colon and a newline character.

## Defined in

[packages/core/src/evaluators.ts:41](https://github.com/elizaOS/eliza/blob/main/packages/core/src/evaluators.ts#L41)
