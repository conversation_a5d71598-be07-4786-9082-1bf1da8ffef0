[@elizaos/core v0.25.8](../index.md) / envSchema

# Variable: envSchema

> `const` **envSchema**: `ZodObject`\<`object`, `"strip"`, `ZodTypeAny`, `object`, `object`\>

TODO: TO COMPLETE

## Type declaration

### OPENAI\_API\_KEY

> **OPENAI\_API\_KEY**: `ZodString`

API Keys with specific formats

### REDPILL\_API\_KEY

> **REDPILL\_API\_KEY**: `ZodString`

### GROK\_API\_KEY

> **GROK\_API\_KEY**: `ZodString`

### GROQ\_API\_KEY

> **GROQ\_API\_KEY**: `ZodString`

### OPENROUTER\_API\_KEY

> **OPENROUTER\_API\_KEY**: `ZodString`

### GOOGLE\_GENERATIVE\_AI\_API\_KEY

> **GOOGLE\_GENERATIVE\_AI\_API\_KEY**: `ZodString`

### ELEVENLABS\_XI\_API\_KEY

> **ELEVENLABS\_XI\_API\_KEY**: `ZodString`

## Defined in

[packages/core/src/environment.ts:6](https://github.com/elizaOS/eliza/blob/main/packages/core/src/environment.ts#L6)
