[@elizaos/core v0.25.8](../index.md) / EmbeddingProvider

# Variable: EmbeddingProvider

> `const` **EmbeddingProvider**: `object`

## Type declaration

### OpenAI

> `readonly` **OpenAI**: `"OpenAI"` = `"OpenAI"`

### Ollama

> `readonly` **Ollama**: `"Ollama"` = `"Ollama"`

### GaiaNet

> `readonly` **GaiaNet**: `"GaiaNet"` = `"GaiaNet"`

### Heurist

> `readonly` **Heurist**: `"Heurist"` = `"Heurist"`

### BGE

> `readonly` **BGE**: `"BGE"` = `"BGE"`

## Defined in

[packages/core/src/embedding.ts:17](https://github.com/elizaOS/eliza/blob/main/packages/core/src/embedding.ts#L17)
