[@elizaos/core v0.25.8](../index.md) / messageCompletionFooter

# Variable: messageCompletionFooter

> `const` **messageCompletionFooter**: "\nResponse format should be formatted in a valid JSON block like this:\n\`\`\`json\n\{ \"user\": \"\{\{agentName\}\}\", \"text\": \"\<string\>\", \"action\": \"\<string\>\" \}\n\`\`\`\n\nThe “action” field should be one of the options in \[Available Actions\] and the \"text\" field should be the response you want to send.\n"

## Defined in

[packages/core/src/parsing.ts:4](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L4)
