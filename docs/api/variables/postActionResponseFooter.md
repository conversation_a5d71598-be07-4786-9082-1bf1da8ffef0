[@elizaos/core v0.25.8](../index.md) / postActionResponseFooter

# Variable: postActionResponseFooter

> `const` **postActionResponseFooter**: `"Choose any combination of [LIKE], [RETWEET], [QUOTE], and [REPLY] that are appropriate. Each action must be on its own line. Your response must only include the chosen actions."`

## Defined in

[packages/core/src/parsing.ts:277](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L277)
