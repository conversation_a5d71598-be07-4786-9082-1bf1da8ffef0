[@elizaos/core v0.25.8](../index.md) / shouldRespondFooter

# Variable: shouldRespondFooter

> `const` **shouldRespondFooter**: "The available options are \[RESPOND\], \[IGNORE\], or \[STOP\]. Choose the most appropriate option.\nIf \{\{agentName\}\} is talking too much, you can choose \[IGNORE\]\n\nYour response must include one of the options."

## Defined in

[packages/core/src/parsing.ts:12](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L12)
