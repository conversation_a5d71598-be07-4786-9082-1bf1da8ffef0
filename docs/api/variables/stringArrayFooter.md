[@elizaos/core v0.25.8](../index.md) / stringArrayFooter

# Variable: stringArrayFooter

> `const` **stringArrayFooter**: "Respond with a JSON array containing the values in a valid JSON block formatted in markdown with this structure:\n\`\`\`json\n\[\n  'value',\n  'value'\n\]\n\`\`\`\n\nYour response must include the valid JSON block."

## Defined in

[packages/core/src/parsing.ts:66](https://github.com/elizaOS/eliza/blob/main/packages/core/src/parsing.ts#L66)
