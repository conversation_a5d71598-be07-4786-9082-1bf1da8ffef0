#!/usr/bin/env node

/**
 * Test the enhanced aggressive ticker detection
 */

import { delegateToMisterAction } from './packages/plugin-mister/dist/actions/delegateToMister.js';

async function testAggressiveTickerDetection() {
    console.log('🧪 Testing Enhanced Aggressive Ticker Detection...\n');
    
    // Mock runtime
    const mockRuntime = {};
    
    // Test cases that should NOW trigger delegation
    const shouldTriggerCases = [
        "What's the price of $MISTER?",
        "$MISTER price",
        "Price of $MISTER",
        "$mister value",
        "$ADA looks good",
        "$BTC is pumping",
        "How much is $SNEK worth?",
        "$HOSKY trading volume",
        "Check $MIN price",
        "$cashcoldgame token info"
    ];
    
    // Test cases that should still NOT trigger
    const shouldNotTriggerCases = [
        "Hello there",
        "How are you?",
        "Good morning",
        "I spent $50 on groceries",
        "The cost was $100"
    ];
    
    console.log('🔍 Testing Cases That SHOULD Trigger Delegation...\n');
    
    for (const testCase of shouldTriggerCases) {
        console.log(`Testing: "${testCase}"`);
        
        const mockMessage = {
            content: { text: testCase },
            userId: "test-user",
            roomId: "test-room"
        };
        
        try {
            const shouldDelegate = await delegateToMisterAction.validate(mockRuntime, mockMessage);
            console.log(`   Result: ${shouldDelegate ? '✅ WILL delegate' : '❌ Will NOT delegate'}`);
            
            if (!shouldDelegate) {
                console.log('   ⚠️ FAILED - This should have triggered delegation!');
            }
        } catch (error) {
            console.log(`   ❌ Error: ${error.message}`);
        }
        console.log();
    }
    
    console.log('🔍 Testing Cases That Should NOT Trigger Delegation...\n');
    
    for (const testCase of shouldNotTriggerCases) {
        console.log(`Testing: "${testCase}"`);
        
        const mockMessage = {
            content: { text: testCase },
            userId: "test-user",
            roomId: "test-room"
        };
        
        try {
            const shouldDelegate = await delegateToMisterAction.validate(mockRuntime, mockMessage);
            console.log(`   Result: ${shouldDelegate ? '❌ WILL delegate' : '✅ Will NOT delegate'}`);
            
            if (shouldDelegate) {
                console.log('   ⚠️ FALSE POSITIVE - This should NOT have triggered delegation!');
            }
        } catch (error) {
            console.log(`   ❌ Error: ${error.message}`);
        }
        console.log();
    }
    
    console.log('=' .repeat(60));
    console.log('✨ Enhanced Ticker Detection Test Complete!\n');
    console.log('📊 Summary:');
    console.log('• Now detects ANY $ symbol followed by letters');
    console.log('• Aggressive price + ticker combinations');
    console.log('• Should catch "$MISTER price" and similar patterns');
    console.log('\n🚀 Ready to restart your agent with enhanced detection!');
}

testAggressiveTickerDetection().catch(console.error);
