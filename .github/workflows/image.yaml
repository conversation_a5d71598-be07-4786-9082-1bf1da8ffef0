#
name: Create and publish a Docker image

# Configures this workflow to run every time a change is pushed to the branch called `release`.
on:
    release:
        types: [created]
    workflow_dispatch:

# Defines two custom environment variables for the workflow. These are used for the Container registry domain, and a name for the Docker image that this workflow builds.
env:
    REGISTRY: ghcr.io
    IMAGE_NAME: ${{ github.repository }}

# There is a single job in this workflow. It's configured to run on the latest available version of Ubuntu.
jobs:
    build-and-push-image:
        runs-on: ubuntu-latest
        # Sets the permissions granted to the `GITHUB_TOKEN` for the actions in this job.
        permissions:
            contents: read
            packages: write
            attestations: write
            id-token: write
            #
        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
            # Uses the `docker/login-action` action to log in to the Container registry using the account and password that will publish the packages. Once published, the packages are scoped to the account defined here.
            - name: Log in to the Container registry
              uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
              with:
                  registry: ${{ env.REGISTRY }}
                  username: ${{ github.actor }}
                  password: ${{ secrets.GITHUB_TOKEN }}
            # This step uses [docker/metadata-action](https://github.com/docker/metadata-action#about) to extract tags and labels that will be applied to the specified image. The `id` "meta" allows the output of this step to be referenced in a subsequent step. The `images` value provides the base name for the tags and labels.
            - name: Extract metadata (tags, labels) for Docker
              id: meta
              uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
              with:
                  images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
            # This step uses the `docker/build-push-action` action to build the image, based on your repository's `Dockerfile`. If the build succeeds, it pushes the image to GitHub Packages.
            # It uses the `context` parameter to define the build's context as the set of files located in the specified path. For more information, see "[Usage](https://github.com/docker/build-push-action#usage)" in the README of the `docker/build-push-action` repository.
            # It uses the `tags` and `labels` parameters to tag and label the image with the output from the "meta" step.
            - name: Build and push Docker image
              id: push
              uses: docker/build-push-action@f2a1d5e99d037542a71f64918e516c093c6f3fc4
              with:
                  context: .
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}

            # This step generates an artifact attestation for the image, which is an unforgeable statement about where and how it was built. It increases supply chain security for people who consume the image. For more information, see "[AUTOTITLE](/actions/security-guides/using-artifact-attestations-to-establish-provenance-for-builds)."
            - name: Generate artifact attestation
              uses: actions/attest-build-provenance@v1
              with:
                  subject-name: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}
                  subject-digest: ${{ steps.push.outputs.digest }}
                  push-to-registry: true

            # This step makes the Docker image public, so users can pull it without authentication.
            - name: Make Docker image public
              run: |
                  curl \
                    -X PATCH \
                    -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
                    -H "Accept: application/vnd.github.v3+json" \
                    https://api.github.com/user/packages/container/${{ env.IMAGE_NAME }}/visibility \
                    -d '{"visibility":"public"}'
