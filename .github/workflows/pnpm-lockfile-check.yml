name: Pnpm Lockfile Check

on:
    pull_request:
        branches: [main]

jobs:
    check-lockfile:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - uses: actions/setup-node@v4
              with:
                  node-version: 23.3.0

            - uses: pnpm/action-setup@v3
              with:
                  version: 9.15.0

            - name: Check if lockfile is up-to-date
              id: lockfile-check
              run: |
                  # Try to install with frozen lockfile
                  if ! pnpm install --frozen-lockfile; then
                      echo "::error::Lockfile is out of date. Please run 'pnpm install --no-frozen-lockfile' and commit the updated pnpm-lock.yaml"
                      echo "failed=true" >> $GITHUB_OUTPUT
                      exit 1
                  fi

            - name: Comment on PR
              if: failure() && steps.lockfile-check.outputs.failed == 'true'
              uses: actions/github-script@v7
              with:
                  script: |
                      github.rest.issues.createComment({
                          issue_number: context.issue.number,
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          body: '❌ The pnpm-lockfile is out of date. Please run `pnpm install --no-frozen-lockfile` and commit the updated pnpm-lock.yaml file.'
                      })
