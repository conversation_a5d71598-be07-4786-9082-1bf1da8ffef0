name: JSDoc Automation

on:
    workflow_dispatch:
        inputs:
            jsdoc:
                description: "Generate JSDoc comments (T/F)"
                required: true
                default: "T"
                type: string
            readme:
                description: "Generate README documentation (T/F)"
                required: true
                default: "T"
                type: string
            pull_number:
                description: "Pull Request Number (if not provided, scans root_directory) - PR must be merged to develop branch. DONT provide if `README documentation` is T from above"
                required: false
                type: string
            root_directory:
                description: "Only scans files in this directory (relative to repository root, e.g., packages/core/src)"
                required: true
                default: "packages/plugin-bootstrap"
                type: string
            excluded_directories:
                description: "Directories to exclude from scanning (comma-separated, relative to root_directory)"
                required: true
                default: "node_modules,dist,test"
                type: string
            reviewers:
                description: "Pull Request Reviewers (Must be collaborator on the repository) comma-separated GitHub usernames"
                required: true
                default: ""
                type: string
            branch:
                description: "Target branch for PR (defaults to develop)"
                required: false
                default: "develop"
                type: string

jobs:
    generate-docs:
        runs-on: ubuntu-latest

        env:
            GITHUB_ACCESS_TOKEN: ${{ secrets.GH_PAT }}
            OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: "20"

            - name: Install pnpm
              uses: pnpm/action-setup@v2
              with:
                  version: 8
                  run_install: false

            - name: Update lockfile
              working-directory: scripts/jsdoc-automation
              run: |
                  echo "Updating lockfile..."
                  pnpm install --no-frozen-lockfile
                  git config --global user.email "github-actions[bot]@users.noreply.github.com"
                  git config --global user.name "github-actions[bot]"
                  git add pnpm-lock.yaml
                  git commit -m "chore: update pnpm lockfile" || echo "No changes to commit"
                  git push || echo "No changes to push"

            - name: Install root dependencies
              run: pnpm install --no-frozen-lockfile

            - name: Install package dependencies
              working-directory: scripts/jsdoc-automation
              run: pnpm install --no-frozen-lockfile

            - name: Build TypeScript
              working-directory: scripts/jsdoc-automation
              run: pnpm build

            - name: Run documentation generator
              working-directory: scripts/jsdoc-automation
              run: pnpm start
              env:
                  INPUT_ROOT_DIRECTORY: ${{ inputs.root_directory }}
                  INPUT_PULL_NUMBER: ${{ inputs.pull_number }}
                  INPUT_EXCLUDED_DIRECTORIES: ${{ inputs.excluded_directories }}
                  INPUT_REVIEWERS: ${{ inputs.reviewers }}
                  INPUT_BRANCH: ${{ inputs.branch }}
                  INPUT_JSDOC: ${{ inputs.jsdoc }}
                  INPUT_README: ${{ inputs.readme }}
