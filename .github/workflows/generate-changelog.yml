name: Generate Changelog
on:
    push:
        tags:
            - "*"
jobs:
    changelog:
        runs-on: ubuntu-latest
        permissions:
            contents: write
        steps:
            - uses: actions/checkout@v4
              with:
                  ref: main
                  token: ${{ secrets.CHANGELOG_GITHUB_TOKEN }}
            - name: Generate Changelog
              run: |
                  export PATH="$PATH:/home/<USER>/.local/share/gem/ruby/3.0.0/bin"
                  gem install --user-install github_changelog_generator
                  github_changelog_generator \
                    -u ${{ github.repository_owner }} \
                    -p ${{ github.event.repository.name }} \
                    --token ${{ secrets.CHANGELOG_GITHUB_TOKEN }}
            - name: Commit Changelog
              uses: stefanzweifel/git-auto-commit-action@v5
              with:
                  commit_message: "chore: update changelog"
                  branch: main
                  file_pattern: "CHANGELOG.md"
                  commit_author: "GitHub Action <<EMAIL>>"
