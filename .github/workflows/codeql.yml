name: "CodeQL Advanced"

on:
    push:
        branches: ["main"]
    pull_request:
        branches: ["main"]
    schedule:
        - cron: "29 8 * * 6"

jobs:
    analyze:
        name: Analyze (${{ matrix.language }})
        runs-on: ${{ (matrix.language == 'swift' && 'macos-latest') || 'ubuntu-latest' }}
        permissions:
            # required for all workflows
            security-events: write

            # required to fetch internal or private CodeQL packs
            packages: read

            # only required for workflows in private repositories
            actions: read
            contents: read

        strategy:
            fail-fast: false
            matrix:
                include:
                    - language: javascript-typescript
                      build-mode: none
        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Initialize CodeQL
              uses: github/codeql-action/init@v3
              with:
                  languages: ${{ matrix.language }}
                  build-mode: ${{ matrix.build-mode }}

            - if: matrix.build-mode == 'manual'
              shell: bash
              run: |
                  echo 'If you are using a "manual" build mode for one or more of the' \
                    'languages you are analyzing, replace this with the commands to build' \
                    'your code, for example:'
                  echo '  make bootstrap'
                  echo '  make release'
                  exit 1

            - name: Perform CodeQL Analysis
              uses: github/codeql-action/analyze@v3
              with:
                  category: "/language:${{matrix.language}}"
