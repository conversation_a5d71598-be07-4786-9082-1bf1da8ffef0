name: Greetings

on: [pull_request_target, issues]

jobs:
    greeting:
        runs-on: ubuntu-latest
        permissions:
            issues: write
            pull-requests: write
        steps:
            - uses: actions/first-interaction@v1
              with:
                  repo-token: ${{ secrets.GITHUB_TOKEN }}
                  issue-message: "Hello @${{ github.actor }}! Welcome to the elizaOS community. Thank you for opening your first issue; we appreciate your contribution. You are now an elizaOS contributor!"
                  pr-message: "Hi @${{ github.actor }}! Welcome to the elizaOS community. Thanks for submitting your first pull request; your efforts are helping us accelerate towards AGI. We'll review it shortly. You are now an elizaOS contributor!"
