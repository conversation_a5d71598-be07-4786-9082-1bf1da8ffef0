name: Mark stale issues and pull requests

on:
    schedule:
        - cron: "25 18 * * *"

jobs:
    stale:
        runs-on: ubuntu-latest
        permissions:
            issues: write
            pull-requests: write

        env:
            DAYS_BEFORE_STALE: 30 # Define the days-before-stale value
            DAYS_BEFORE_CLOSE: 7 # Define the days-before-close value

        steps:
            - uses: actions/stale@v5
              with:
                  repo-token: ${{ secrets.GITHUB_TOKEN }}
                  stale-issue-message: |
                      This issue has been automatically marked as stale due to ${{
                      env.DAYS_BEFORE_STALE }} days of inactivity.
                      If no further activity occurs within ${{ env.DAYS_BEFORE_CLOSE }} days, it will be closed automatically. Please take action if this issue is still relevant.
                  stale-pr-message: |
                      This pull request has been automatically marked as stale due to ${{
                      env.DAYS_BEFORE_STALE }} days of inactivity.
                      If no further activity occurs within ${{ env.DAYS_BEFORE_CLOSE }} days, it will be closed automatically. Please take action if this pull request is still relevant.
                  stale-issue-label: "no-issue-activity"
                  stale-pr-label: "no-pr-activity"
                  days-before-stale: ${{ env.DAYS_BEFORE_STALE }}
                  days-before-close: ${{ env.DAYS_BEFORE_CLOSE }}
