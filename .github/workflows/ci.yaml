name: ci
on:
    push:
        branches: [main]
    pull_request:
        branches: [main]
jobs:
    check:
        runs-on: ubuntu-latest
        env:
            OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
            TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
            TURBO_TEAM: ${{ vars.TURBO_TEAM }}
            TURBO_REMOTE_ONLY: true
        steps:
            - uses: actions/checkout@v4

            - uses: pnpm/action-setup@v3
              with:
                  version: 9.15.0

            - uses: actions/setup-node@v4
              with:
                  node-version: "23"
                  cache: "pnpm"

            - name: Install dependencies
              run: pnpm install -r --no-frozen-lockfile

            - name: Setup Biome CLI
              uses: biomejs/setup-biome@v2
              with:
                version: latest

            - name: Run Biome
              run: biome ci

            - name: Create test env file
              run: |
                  echo "TEST_DATABASE_CLIENT=sqlite" > packages/core/.env.test
                  echo "NODE_ENV=test" >> packages/core/.env.test

            - name: Run tests
              run: cd packages/core && pnpm test:coverage

            - name: Build packages
              run: pnpm run build

            - name: Upload coverage reports to Codecov
              uses: codecov/codecov-action@v5
              with:
                  token: ${{ secrets.CODECOV_TOKEN }}
