name: Integration Tests
on:
  push:
    branches:
      - "*"
  pull_request_target:
    branches:
      - "*"

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      TURBO_CACHE: remote:rw
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true  # Ensures submodules are checked out

      - uses: pnpm/action-setup@v3
        with:
          version: 9.15.0

      - uses: actions/setup-node@v4
        with:
          node-version: "23.3"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Build packages
        run: pnpm build

      - name: Check for API key
        run: |
          if [ -z "$OPENAI_API_KEY" ]; then
              echo "Error: OPENAI_API_KEY is not set."
              exit 1
          fi

      - name: Run integration tests
        run: pnpm run integrationTests
