name: smoke-test
on:
  push:
    branches:
      - "*"
  pull_request:
    branches:
      - "*"

jobs:
  smoke-tests:
    runs-on: ubuntu-latest
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      TURBO_CACHE: remote:rw
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true  # Ensures submodules are checked out
      - uses: pnpm/action-setup@v3
        with:
          version: 9.15.0

      - uses: actions/setup-node@v4
        with:
          node-version: "23.3"
          cache: "pnpm"

      - name: Run smoke tests
        run: pnpm run smokeTests
