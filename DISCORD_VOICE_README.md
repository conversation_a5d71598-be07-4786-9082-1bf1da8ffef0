# Discord Voice Integration

This README provides instructions for setting up the Discord voice integration for your ElizaOS agent.

## Prerequisites

1. A Discord bot token (get one from the [Discord Developer Portal](https://discord.com/developers/applications))
2. A Discord server where you have admin permissions
3. A voice channel ID from your Discord server
4. A Deepgram API key (get one from [Deepgram](https://deepgram.com/))
5. An ElevenLabs API key (get one from [ElevenLabs](https://elevenlabs.io/))

## Setup Instructions

1. Make sure your `.env` file has the following environment variables:

```
DEEPGRAM_API_KEY=your_deepgram_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
DISCORD_API_TOKEN=your_discord_bot_token
DISCORD_VOICE_CHANNEL_ID=your_discord_voice_channel_id
```

2. Make sure your character file has the following plugins:

```json
"plugins": [
  "@elizaos/plugin-node",
  "@elizaos-plugins/client-discord",
  "@elizaos-plugins/plugin-giphy"
]
```

3. Make sure your character file has the following voice settings:

```json
"settings": {
  "voice": {
    "transcription": "deepgram",
    "elevenlabs": {
      "voiceId": "your_elevenlabs_voice_id",
      "model": "eleven_multilingual_v2"
    }
  }
}
```

4. Restart your ElizaOS agent:

```
pnpm restart
```

## Troubleshooting

If you encounter any issues, check the following:

1. Make sure your Discord bot is added to your server with the correct permissions
2. Make sure your Discord bot has access to the voice channel
3. Make sure your API keys are correct
4. Check the logs for any error messages

For more information, see the [ElizaOS documentation](https://docs.elizaos.com).
