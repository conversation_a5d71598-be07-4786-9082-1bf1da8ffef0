// <PERSON><PERSON>t to test the Discord voice integration
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Check if the required environment variables are set
console.log('Checking environment variables...');
const requiredEnvVars = [
  'DEEPGRAM_API_KEY',
  'ELEVENLABS_API_KEY',
  'DISCORD_API_TOKEN',
  'DISCORD_VOICE_CHANNEL_ID'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  console.error('Missing environment variables:', missingEnvVars.join(', '));
  console.error('Please set these environment variables in your .env file and try again.');
  process.exit(1);
}

console.log('All required environment variables are set!');

// Check if the character file has the correct plugin configuration
const characterFilePath = path.join(__dirname, 'characters', 'MxSTER.character.json');
const characterFile = JSON.parse(fs.readFileSync(characterFilePath, 'utf8'));

console.log('\nChecking character file...');
console.log('Character name:', characterFile.name);

// Check if the character file has the plugin-node plugin
const hasPluginNode = characterFile.plugins.some(plugin => plugin === '@elizaos/plugin-node');
console.log('Has @elizaos/plugin-node plugin:', hasPluginNode);

// Check if the character file has the plugin-giphy plugin
const hasPluginGiphy = characterFile.plugins.some(plugin => plugin === '@elizaos-plugins/plugin-giphy');
console.log('Has @elizaos-plugins/plugin-giphy plugin:', hasPluginGiphy);

// Check if the character file has the client-discord plugin
const hasClientDiscord = characterFile.plugins.some(plugin => plugin === '@elizaos-plugins/client-discord');
console.log('Has @elizaos-plugins/client-discord plugin:', hasClientDiscord);

// Check if the character file has the voice settings
console.log('Voice settings:', characterFile.settings?.voice);

if (!hasPluginNode || !hasClientDiscord) {
  console.error('Character file is missing required plugins!');
  console.error('Please add the missing plugins to your character file and try again.');
  process.exit(1);
}

if (!characterFile.settings?.voice) {
  console.error('Character file is missing voice settings!');
  console.error('Please add voice settings to your character file and try again.');
  process.exit(1);
}

console.log('\nAll checks passed! Your Discord voice integration should work correctly.');
console.log('Please restart your ElizaOS agent with the following command:');
console.log('\npnpm restart');
console.log('\nThen check the logs for any error messages related to the Discord voice integration.');
