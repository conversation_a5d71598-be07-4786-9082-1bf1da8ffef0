#!/usr/bin/env node

/**
 * Test script to verify MISTER delegation is working
 * This will test the delegation action directly
 */

import { delegateToMisterAction } from './packages/plugin-mister/dist/actions/delegateToMister.js';

async function testDelegation() {
    console.log('🧪 Testing MISTER Delegation Action...\n');

    // Mock runtime and message objects
    const mockRuntime = {};

    const testCases = [
        // Basic crypto queries
        { text: "What's the price of ADA?", expected: true },
        { text: "What's the ticker?", expected: true },
        { text: "Bitcoin price analysis", expected: true },
        { text: "Market update please", expected: true },
        { text: "Cardano analysis", expected: true },

        // Specific analysis requests (your examples)
        { text: "I need deep analysis on $SNEK", expected: true },
        { text: "What's the recent news on HOSKY?", expected: true },
        { text: "Deep analysis on MIN token", expected: true },
        { text: "Recent news on ADA", expected: true },

        // Ticker patterns
        { text: "Check $HOSKY volume", expected: true },
        { text: "$BTC is pumping", expected: true },
        { text: "What about $LENFI?", expected: true },
        { text: "How is $SNEK doing?", expected: true },

        // CAPS token patterns
        { text: "HOSKY is trending", expected: true },
        { text: "ADA looks bullish", expected: true },
        { text: "SNEK token analysis", expected: true },
        { text: "MIN project update", expected: true },

        // Analysis patterns
        { text: "Cabal check on MIN", expected: true },
        { text: "Risk analysis for SNEK", expected: true },
        { text: "Tell me about LENFI token", expected: true },
        { text: "Research on HOSKY", expected: true },
        { text: "DD on ADA", expected: true },

        // Price patterns
        { text: "Price of Bitcoin", expected: true },
        { text: "How much is ADA worth?", expected: true },
        { text: "SNEK trading at what price?", expected: true },
        { text: "What's the market cap of HOSKY?", expected: true },

        // Major crypto mentions
        { text: "Ethereum is down", expected: true },
        { text: "Solana news", expected: true },
        { text: "BNB analysis", expected: true },
        { text: "XRP lawsuit update", expected: true },

        // Non-crypto (should NOT delegate)
        { text: "How's the weather today?", expected: false },
        { text: "Tell me a joke", expected: false },
        { text: "What's for dinner?", expected: false },
        { text: "I love cats", expected: false },
        { text: "Good morning!", expected: false },

        // Edge cases that might trigger but shouldn't
        { text: "I CAN help you", expected: false }, // CAPS but common word
        { text: "THE BEST day ever", expected: false }, // CAPS but common words
    ];

    console.log('🔍 Testing Action Validation...\n');

    for (const testCase of testCases) {
        const mockMessage = {
            content: { text: testCase.text }
        };

        try {
            const shouldDelegate = await delegateToMisterAction.validate(mockRuntime, mockMessage);
            const result = shouldDelegate === testCase.expected ? '✅' : '❌';
            console.log(`   ${result} "${testCase.text}" -> ${shouldDelegate} (expected: ${testCase.expected})`);
        } catch (error) {
            console.log(`   ❌ "${testCase.text}" -> Error: ${error.message}`);
        }
    }

    console.log('\n🎯 Testing Handler (with crypto query)...\n');

    // Test the handler with a crypto query
    const cryptoMessage = {
        content: { text: "What's the price of ADA?" },
        userId: "test-user",
        roomId: "test-room"
    };

    let handlerResponse = null;
    const mockCallback = async (response) => {
        handlerResponse = response;
        console.log('   📤 Handler Response:', response.text.substring(0, 100) + '...');
    };

    try {
        const handlerResult = await delegateToMisterAction.handler(
            mockRuntime,
            cryptoMessage,
            null,
            null,
            mockCallback
        );

        console.log(`   📊 Handler Result: ${handlerResult ? '✅ Success' : '❌ Failed'}`);

        if (handlerResponse) {
            console.log(`   🎯 Response Source: ${handlerResponse.content?.source || 'Unknown'}`);
            console.log(`   🔧 Response Action: ${handlerResponse.content?.action || 'None'}`);
        }

    } catch (error) {
        console.log(`   ❌ Handler Error: ${error.message}`);
    }

    console.log('\n' + '='.repeat(50));
    console.log('✨ MISTER Delegation Test Complete!\n');
    console.log('🚀 Your MISTER plugin is ready to use!');
    console.log('💡 Crypto queries will now be delegated to MISTER\'s API');
    console.log('🔄 All other queries will use your normal Eliza provider');
}

testDelegation().catch(console.error);
