// <PERSON>ript to fix the transcription service issue
const fs = require('fs');
const path = require('path');

// Path to the package.json file
const packageJsonPath = path.join(__dirname, 'package.json');

// Read the package.json file
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Remove the conflicting plugin-node dependency
if (packageJson.dependencies['@elizaos-plugins/plugin-node']) {
  delete packageJson.dependencies['@elizaos-plugins/plugin-node'];
  console.log('Removed @elizaos-plugins/plugin-node dependency');
}

// Make sure we have the correct plugin-node dependency
if (!packageJson.dependencies['@elizaos/plugin-node']) {
  packageJson.dependencies['@elizaos/plugin-node'] = '0.25.6-alpha.1';
  console.log('Added @elizaos/plugin-node dependency');
}

// Write the updated package.json file
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('Updated package.json file');

console.log('Please run the following commands to apply the changes:');
console.log('pnpm install');
console.log('pnpm restart');
