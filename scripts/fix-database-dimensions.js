#!/usr/bin/env node

/**
 * Database Dimension Fix Script
 *
 * This script fixes the embedding dimension mismatch by:
 * 1. Identifying all entries with wrong dimensions (6144 instead of 1536)
 * 2. Replacing them with correct zero vectors (1536 dimensions)
 * 3. Ensuring database consistency for future operations
 */

const fs = require('fs');
const path = require('path');

// Try to require better-sqlite3 from the packages directory
let Database;
try {
    Database = require('../packages/adapter-sqlite/node_modules/better-sqlite3');
} catch (e) {
    try {
        Database = require('../node_modules/better-sqlite3');
    } catch (e2) {
        console.error('❌ better-sqlite3 not found. Please run: pnpm install');
        process.exit(1);
    }
}

console.log('🔧 Database Dimension Fix Script');
console.log('=================================');

const DB_PATH = './agent/data/db.sqlite';
const EXPECTED_DIMENSIONS = 1536;
const WRONG_DIMENSIONS = 6144;

if (!fs.existsSync(DB_PATH)) {
    console.error(`❌ Database not found at: ${DB_PATH}`);
    process.exit(1);
}

console.log(`📂 Database path: ${DB_PATH}`);
console.log(`✅ Expected dimensions: ${EXPECTED_DIMENSIONS}`);
console.log(`❌ Wrong dimensions: ${WRONG_DIMENSIONS}`);

// Create backup
const backupPath = `${DB_PATH}.backup.${Date.now()}`;
fs.copyFileSync(DB_PATH, backupPath);
console.log(`💾 Backup created: ${backupPath}`);

const db = new Database(DB_PATH);

function createZeroVector(dimensions) {
    return new Float32Array(dimensions);
}

function checkTable(tableName) {
    console.log(`\n🔍 Checking table: ${tableName}`);

    try {
        // Check if table exists and has embedding column
        const tableInfo = db.prepare(`PRAGMA table_info(${tableName})`).all();
        const hasEmbedding = tableInfo.some(col => col.name === 'embedding');

        if (!hasEmbedding) {
            console.log(`   ⏭️ Table ${tableName} has no embedding column, skipping`);
            return { total: 0, fixed: 0 };
        }

        // Count total entries
        const totalCount = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get().count;
        console.log(`   📊 Total entries: ${totalCount}`);

        if (totalCount === 0) {
            console.log(`   ⏭️ Table ${tableName} is empty, skipping`);
            return { total: 0, fixed: 0 };
        }

        // Find entries with wrong dimensions
        const wrongDimensionEntries = db.prepare(`
            SELECT id, LENGTH(embedding) as embedding_size
            FROM ${tableName}
            WHERE LENGTH(embedding) = ?
        `).all(WRONG_DIMENSIONS * 4); // 4 bytes per float

        console.log(`   ❌ Entries with wrong dimensions (${WRONG_DIMENSIONS}): ${wrongDimensionEntries.length}`);

        if (wrongDimensionEntries.length === 0) {
            console.log(`   ✅ All entries in ${tableName} have correct dimensions`);
            return { total: totalCount, fixed: 0 };
        }

        // Fix entries with wrong dimensions
        const correctZeroVector = createZeroVector(EXPECTED_DIMENSIONS);
        const updateStmt = db.prepare(`UPDATE ${tableName} SET embedding = ? WHERE id = ?`);

        let fixedCount = 0;
        for (const entry of wrongDimensionEntries) {
            try {
                updateStmt.run(correctZeroVector, entry.id);
                fixedCount++;
            } catch (error) {
                console.log(`   ⚠️ Failed to fix entry ${entry.id}: ${error.message}`);
            }
        }

        console.log(`   ✅ Fixed ${fixedCount} entries in ${tableName}`);
        return { total: totalCount, fixed: fixedCount };

    } catch (error) {
        console.log(`   ❌ Error checking table ${tableName}: ${error.message}`);
        return { total: 0, fixed: 0 };
    }
}

function verifyFix() {
    console.log('\n🔍 Verifying fix...');

    const tables = ['memories', 'knowledge'];
    let totalEntries = 0;
    let totalCorrect = 0;
    let totalWrong = 0;

    for (const tableName of tables) {
        try {
            const tableInfo = db.prepare(`PRAGMA table_info(${tableName})`).all();
            const hasEmbedding = tableInfo.some(col => col.name === 'embedding');

            if (!hasEmbedding) continue;

            const entries = db.prepare(`
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN LENGTH(embedding) = ? THEN 1 ELSE 0 END) as correct,
                    SUM(CASE WHEN LENGTH(embedding) = ? THEN 1 ELSE 0 END) as wrong
                FROM ${tableName}
            `).get(EXPECTED_DIMENSIONS * 4, WRONG_DIMENSIONS * 4);

            totalEntries += entries.total;
            totalCorrect += entries.correct;
            totalWrong += entries.wrong;

            const status = entries.wrong === 0 ? '✅' : '❌';
            console.log(`   ${status} ${tableName}: ${entries.correct} correct, ${entries.wrong} wrong`);

        } catch (error) {
            console.log(`   ⚠️ Error verifying ${tableName}: ${error.message}`);
        }
    }

    console.log(`\n📊 Final Summary:`);
    console.log(`   Total entries: ${totalEntries}`);
    console.log(`   Correct dimensions (${EXPECTED_DIMENSIONS}): ${totalCorrect}`);
    console.log(`   Wrong dimensions (${WRONG_DIMENSIONS}): ${totalWrong}`);

    if (totalWrong === 0) {
        console.log(`   🎉 All embeddings now have correct dimensions!`);
        return true;
    } else {
        console.log(`   ⚠️ ${totalWrong} entries still have wrong dimensions`);
        return false;
    }
}

async function main() {
    try {
        console.log('\n🚀 Starting dimension fix...');

        // Check and fix each table
        const tables = ['memories', 'knowledge'];
        let totalFixed = 0;

        for (const tableName of tables) {
            const result = checkTable(tableName);
            totalFixed += result.fixed;
        }

        console.log(`\n✅ Fixed ${totalFixed} total entries`);

        // Verify the fix
        const success = verifyFix();

        if (success) {
            console.log('\n🎉 Database dimension fix completed successfully!');
            console.log('🚀 Your agent should now work without embedding dimension errors');
        } else {
            console.log('\n⚠️ Some issues remain. Check the logs above for details.');
        }

    } catch (error) {
        console.error('\n💥 Fix failed:', error);
        console.error('Stack trace:', error.stack);
    } finally {
        db.close();
    }
}

// Run the fix
main().then(() => {
    console.log('\n🏁 Dimension fix script completed');
}).catch(error => {
    console.error('\n💥 Script crashed:', error);
    process.exit(1);
});
