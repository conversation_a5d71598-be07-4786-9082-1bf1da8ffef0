// Script to generate price narrative posts for MxSTER
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const TOKENS = ['ADA', 'BTC']; // Tokens to generate narratives for
const TEMPLATE_PATH = path.join(__dirname, '../characters/templates/price-narrative.txt');
const SERVER_URL = process.env.SERVER_URL || 'http://localhost';
const SERVER_PORT = process.env.SERVER_PORT || 3000;

// Read the template
const template = fs.readFileSync(TEMPLATE_PATH, 'utf8');

// Function to get price data from CoinMarketCap plugin
async function getPriceData(token) {
  try {
    const response = await axios.post(`${SERVER_URL}:${SERVER_PORT}/api/actions/execute`, {
      action: 'GET_PRICE',
      parameters: {
        symbol: token,
        currency: 'USD'
      },
      agentId: 'MISTER',
      userId: 'system',
      roomId: 'system'
    });
    
    return response.data;
  } catch (error) {
    console.error(`Error fetching price data for ${token}:`, error.message);
    return null;
  }
}

// Function to generate a narrative post
async function generateNarrativePost(token) {
  try {
    // Get price data
    const priceData = await getPriceData(token);
    if (!priceData) {
      console.error(`Failed to get price data for ${token}`);
      return;
    }
    
    // Create prompt with token
    const prompt = template.replace(/{{token}}/g, token);
    
    // Generate the post using the agent's model
    const response = await axios.post(`${SERVER_URL}:${SERVER_PORT}/api/agents/MISTER/generate`, {
      prompt,
      context: {
        priceData: JSON.stringify(priceData),
        token
      }
    });
    
    if (response.data && response.data.text) {
      // Post the generated content to Twitter
      await postToTwitter(response.data.text);
    } else {
      console.error('Failed to generate narrative post');
    }
  } catch (error) {
    console.error('Error generating narrative post:', error.message);
  }
}

// Function to post to Twitter
async function postToTwitter(content) {
  try {
    const response = await axios.post(`${SERVER_URL}:${SERVER_PORT}/api/agents/MISTER/twitter/post`, {
      content
    });
    
    console.log('Posted to Twitter:', content);
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error posting to Twitter:', error.message);
  }
}

// Main function
async function main() {
  console.log('Generating price narrative posts...');
  
  // Generate posts for each token
  for (const token of TOKENS) {
    console.log(`Generating post for ${token}...`);
    await generateNarrativePost(token);
    
    // Wait a bit between posts
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  console.log('Done generating price narrative posts');
}

// Run the script
main().catch(console.error);
