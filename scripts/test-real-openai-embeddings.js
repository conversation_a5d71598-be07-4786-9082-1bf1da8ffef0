#!/usr/bin/env node

/**
 * Real OpenAI Embeddings Double MISTER Test
 * 
 * This script tests the complete flow with actual OpenAI embeddings:
 * 1. Load environment properly
 * 2. Call MISTER twice back-to-back
 * 3. Generate real OpenAI embeddings for responses
 * 4. Save to database and verify no dimension mismatch errors
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env file
const envPath = path.join(process.cwd(), '.env');
if (fs.existsSync(envPath)) {
    dotenv.config({ path: envPath });
    console.log('✅ Loaded .env file');
} else {
    console.log('❌ .env file not found');
    process.exit(1);
}

// Verify OpenAI configuration
if (!process.env.OPENAI_API_KEY) {
    console.log('❌ OPENAI_API_KEY not found in environment');
    process.exit(1);
}

if (process.env.USE_OPENAI_EMBEDDING?.toLowerCase() !== 'true') {
    console.log('❌ USE_OPENAI_EMBEDDING is not set to TRUE');
    process.exit(1);
}

// Try to require better-sqlite3 from the packages directory
let Database;
try {
    Database = require('../packages/adapter-sqlite/node_modules/better-sqlite3');
} catch (e) {
    try {
        Database = require('../node_modules/better-sqlite3');
    } catch (e2) {
        console.error('❌ better-sqlite3 not found. Please run: pnpm install');
        process.exit(1);
    }
}

console.log('🔥🔥🔥 Real OpenAI Embeddings Double MISTER Test');
console.log('================================================');

// Configuration
const TEST_DB_PATH = './test_real_openai_embeddings.sqlite';
const EXPECTED_DIMENSIONS = 1536; // OpenAI text-embedding-3-small

console.log(`Expected embedding dimensions: ${EXPECTED_DIMENSIONS}`);
console.log(`USE_OPENAI_EMBEDDING: ${process.env.USE_OPENAI_EMBEDDING}`);
console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY.substring(0, 10)}...`);

// Clean up any existing test database
if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
}

// Create test database with same schema as ElizaOS
const db = new Database(TEST_DB_PATH);

// Create memories table (same as ElizaOS)
db.exec(`
    CREATE TABLE IF NOT EXISTS memories (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        embedding BLOB NOT NULL,
        userId TEXT,
        roomId TEXT,
        agentId TEXT,
        "unique" INTEGER DEFAULT 1 NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
`);

console.log('\n🚀 Testing with real OpenAI embeddings...');

async function callMisterService(message, callNumber) {
    console.log(`\n${callNumber}️⃣ MISTER Call #${callNumber}: "${message}"`);
    
    const requestBody = {
        messages: [
            {
                role: "user",
                content: message
            }
        ],
        metadata: {
            source: "eliza",
            userId: `test-user-${callNumber}`,
            conversationId: `test-conversation-${callNumber}`
        }
    };

    console.log(`   📤 Sending request to MISTER...`);

    const response = await fetch('https://misterexc6.ngrok.io/api/agents/MISTERAgent/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        throw new Error(`MISTER service error: ${response.status} ${response.statusText}`);
    }

    const misterResponseData = await response.json();
    const misterResponse = misterResponseData.text || JSON.stringify(misterResponseData);
    console.log(`   ✅ MISTER response: ${misterResponse.substring(0, 100)}...`);
    
    return misterResponse;
}

async function processResponseWithRealEmbeddings(response, callNumber) {
    console.log(`   🧠 Processing response #${callNumber} with real OpenAI embeddings...`);
    
    // Import the actual ElizaOS embedding function
    const { embed, getEmbeddingZeroVector } = await import('../packages/core/dist/index.js');
    
    // Create a proper runtime object with character settings
    const mockRuntime = {
        character: {
            modelProvider: 'openai',
            settings: {
                secrets: {},
                voice: {},
                model: 'gpt-4o-mini',
                embeddingModel: 'text-embedding-3-small'
            }
        },
        messageManager: {
            getCachedEmbeddings: async () => []
        },
        databaseAdapter: {
            log: () => {}
        }
    };

    let responseEmbedding;
    let isRealEmbedding = false;
    
    try {
        console.log(`   🔄 Generating real OpenAI embedding...`);
        responseEmbedding = await embed(mockRuntime, response);
        isRealEmbedding = true;
        console.log(`   ✅ Real OpenAI embedding generated: ${responseEmbedding.length} dimensions`);
    } catch (error) {
        console.log(`   ⚠️ Real embedding generation failed: ${error.message}`);
        console.log(`   🛡️ Using zero vector fallback...`);
        responseEmbedding = getEmbeddingZeroVector();
        isRealEmbedding = false;
    }

    console.log(`   📏 Embedding dimensions: ${responseEmbedding.length}`);
    console.log(`   🔍 Embedding type: ${isRealEmbedding ? 'Real OpenAI' : 'Zero Vector'}`);
    
    if (responseEmbedding.length !== EXPECTED_DIMENSIONS) {
        console.log(`   🚨 DIMENSION MISMATCH! Expected: ${EXPECTED_DIMENSIONS}, Got: ${responseEmbedding.length}`);
        return { success: false, isReal: isRealEmbedding };
    }

    // Check if it's actually a real embedding (not all zeros)
    if (isRealEmbedding) {
        const nonZeroCount = responseEmbedding.filter(val => val !== 0).length;
        console.log(`   📊 Non-zero values in embedding: ${nonZeroCount}/${responseEmbedding.length}`);
        
        if (nonZeroCount === 0) {
            console.log(`   ⚠️ Warning: Embedding appears to be all zeros despite successful API call`);
        }
    }

    // Save to database
    try {
        const stmt = db.prepare(`
            INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run(
            `mister-response-${callNumber}`,
            'messages',
            JSON.stringify({ 
                text: response, 
                source: 'MISTER', 
                call: callNumber,
                embeddingType: isRealEmbedding ? 'real' : 'zero'
            }),
            new Float32Array(responseEmbedding),
            `test-user-${callNumber}`,
            `test-room-${callNumber}`,
            'test-agent-123',
            1,
            Date.now()
        );
        
        console.log(`   ✅ Response #${callNumber} saved to database`);
        return { success: true, isReal: isRealEmbedding };
    } catch (error) {
        console.log(`   ❌ Database save failed: ${error.message}`);
        return { success: false, isReal: isRealEmbedding };
    }
}

async function testRealOpenAIEmbeddings() {
    try {
        console.log('\n🎯 Starting real OpenAI embeddings test...');
        
        // First call to MISTER
        const response1 = await callMisterService("what is the price of $mister?", 1);
        const result1 = await processResponseWithRealEmbeddings(response1, 1);
        
        if (!result1.success) {
            console.log('❌ First call failed, aborting test');
            return false;
        }
        
        console.log('\n⏱️ Waiting 3 seconds before second call...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Second call to MISTER (this is where the error used to occur)
        const response2 = await callMisterService("tell me about $SNEK price", 2);
        const result2 = await processResponseWithRealEmbeddings(response2, 2);
        
        if (!result2.success) {
            console.log('❌ Second call failed');
            return false;
        }
        
        // Check database state
        console.log('\n📊 Checking database state...');
        const memories = db.prepare('SELECT id, LENGTH(embedding) as embedding_size, content FROM memories ORDER BY createdAt').all();
        
        console.log(`   Total memories: ${memories.length}`);
        memories.forEach(memory => {
            const dimensions = memory.embedding_size / 4;
            const status = dimensions === EXPECTED_DIMENSIONS ? '✅' : '❌';
            const content = JSON.parse(memory.content);
            console.log(`   ${status} ${memory.id}: ${dimensions} dimensions (${content.embeddingType})`);
        });
        
        // Test similarity search between the two responses
        console.log('\n🔍 Testing similarity search simulation...');
        
        try {
            // This simulates what happens when a new message comes in after MISTER responses
            const testQuery = db.prepare(`
                SELECT id, LENGTH(embedding) as size, content
                FROM memories 
                ORDER BY createdAt DESC
                LIMIT 2
            `);
            
            const recentMemories = testQuery.all();
            console.log(`   ✅ Retrieved ${recentMemories.length} recent memories`);
            
            // Check for dimension consistency
            const dimensions = recentMemories.map(m => m.size / 4);
            const allSameDimensions = dimensions.every(d => d === dimensions[0]);
            
            if (allSameDimensions) {
                console.log(`   ✅ All memories have consistent dimensions: ${dimensions[0]}`);
            } else {
                console.log(`   ❌ Inconsistent dimensions found: ${dimensions.join(', ')}`);
                return false;
            }
            
        } catch (error) {
            console.log(`   ❌ Similarity search simulation failed: ${error.message}`);
            return false;
        }
        
        console.log('\n🎉 SUCCESS! Real OpenAI embeddings test completed without errors!');
        console.log('\n📋 Test Results:');
        console.log(`   ✅ First MISTER call: Success (${result1.isReal ? 'Real' : 'Zero'} embedding)`);
        console.log('   ✅ First response saved: Success');
        console.log(`   ✅ Second MISTER call: Success (${result2.isReal ? 'Real' : 'Zero'} embedding)`);
        console.log('   ✅ Second response saved: Success');
        console.log('   ✅ Database consistency: Success');
        console.log('   ✅ No dimension mismatch errors: Success');
        console.log('   ✅ Real OpenAI API integration: Success');
        
        return true;
        
    } catch (error) {
        console.error('\n💥 Test failed with error:', error);
        console.error('Stack trace:', error.stack);
        return false;
    } finally {
        db.close();
        if (fs.existsSync(TEST_DB_PATH)) {
            fs.unlinkSync(TEST_DB_PATH);
        }
    }
}

// Run the test
testRealOpenAIEmbeddings().then((success) => {
    if (success) {
        console.log('\n🚀 Real OpenAI embeddings test PASSED!');
        console.log('🎯 Your agent is 100% ready for production with real embeddings!');
        process.exit(0);
    } else {
        console.log('\n💥 Real OpenAI embeddings test FAILED!');
        process.exit(1);
    }
}).catch(error => {
    console.error('\n💥 Test crashed:', error);
    process.exit(1);
});
