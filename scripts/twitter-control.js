#!/usr/bin/env node

/**
 * Twitter Client Control Script
 * 
 * This script provides manual control over the Twitter client's monitoring processes.
 * It allows you to start and stop the Twitter client's monitoring loops as needed.
 * 
 * Usage:
 *   node twitter-control.js start - Start Twitter monitoring
 *   node twitter-control.js stop - Stop Twitter monitoring
 *   node twitter-control.js status - Check Twitter monitoring status
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Path to the Twitter client control file
const controlFilePath = path.join(__dirname, '../data/twitter-control.json');

// Default control settings
const defaultControlSettings = {
  monitoring: false,
  lastStartTime: null,
  lastStopTime: null,
  runDuration: 10 * 60 * 1000, // 10 minutes in milliseconds
  processId: null
};

// Ensure the control file exists
function ensureControlFile() {
  if (!fs.existsSync(path.dirname(controlFilePath))) {
    fs.mkdirSync(path.dirname(controlFilePath), { recursive: true });
  }
  
  if (!fs.existsSync(controlFilePath)) {
    fs.writeFileSync(controlFilePath, JSON.stringify(defaultControlSettings, null, 2));
  }
}

// Read the current control settings
function readControlSettings() {
  ensureControlFile();
  const data = fs.readFileSync(controlFilePath, 'utf8');
  return JSON.parse(data);
}

// Write updated control settings
function writeControlSettings(settings) {
  fs.writeFileSync(controlFilePath, JSON.stringify(settings, null, 2));
}

// Start Twitter monitoring
function startMonitoring() {
  const settings = readControlSettings();
  
  if (settings.monitoring) {
    console.log('Twitter monitoring is already running.');
    return;
  }
  
  // Update settings
  settings.monitoring = true;
  settings.lastStartTime = Date.now();
  
  // Set a timer to automatically stop after the run duration
  setTimeout(() => {
    stopMonitoring();
    console.log(`Twitter monitoring automatically stopped after ${settings.runDuration / 60000} minutes.`);
  }, settings.runDuration);
  
  writeControlSettings(settings);
  console.log(`Twitter monitoring started. Will run for ${settings.runDuration / 60000} minutes.`);
  
  // Start the agent with Twitter enabled
  exec('npm run start', (error, stdout, stderr) => {
    if (error) {
      console.error(`Error starting agent: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`Agent stderr: ${stderr}`);
      return;
    }
    console.log(`Agent stdout: ${stdout}`);
  });
}

// Stop Twitter monitoring
function stopMonitoring() {
  const settings = readControlSettings();
  
  if (!settings.monitoring) {
    console.log('Twitter monitoring is not running.');
    return;
  }
  
  // Update settings
  settings.monitoring = false;
  settings.lastStopTime = Date.now();
  
  writeControlSettings(settings);
  console.log('Twitter monitoring stopped.');
  
  // Kill the agent process if it's running
  if (settings.processId) {
    try {
      process.kill(settings.processId);
      console.log(`Killed agent process (PID: ${settings.processId}).`);
    } catch (error) {
      console.error(`Error killing agent process: ${error.message}`);
    }
  }
}

// Check Twitter monitoring status
function checkStatus() {
  const settings = readControlSettings();
  
  console.log('Twitter Monitoring Status:');
  console.log(`  Monitoring: ${settings.monitoring ? 'Running' : 'Stopped'}`);
  
  if (settings.lastStartTime) {
    console.log(`  Last Start: ${new Date(settings.lastStartTime).toLocaleString()}`);
  }
  
  if (settings.lastStopTime) {
    console.log(`  Last Stop: ${new Date(settings.lastStopTime).toLocaleString()}`);
  }
  
  console.log(`  Run Duration: ${settings.runDuration / 60000} minutes`);
  
  if (settings.processId) {
    console.log(`  Process ID: ${settings.processId}`);
  }
}

// Set the run duration
function setRunDuration(minutes) {
  const settings = readControlSettings();
  
  settings.runDuration = minutes * 60 * 1000;
  writeControlSettings(settings);
  
  console.log(`Run duration set to ${minutes} minutes.`);
}

// Main function
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'start':
      startMonitoring();
      break;
    case 'stop':
      stopMonitoring();
      break;
    case 'status':
      checkStatus();
      break;
    case 'duration':
      const minutes = parseInt(process.argv[3]);
      if (isNaN(minutes) || minutes <= 0) {
        console.error('Please provide a valid duration in minutes.');
        return;
      }
      setRunDuration(minutes);
      break;
    default:
      console.log('Usage:');
      console.log('  node twitter-control.js start - Start Twitter monitoring');
      console.log('  node twitter-control.js stop - Stop Twitter monitoring');
      console.log('  node twitter-control.js status - Check Twitter monitoring status');
      console.log('  node twitter-control.js duration <minutes> - Set run duration in minutes');
      break;
  }
}

// Run the script
main();
