#!/usr/bin/env node

/**
 * Real MISTER Delegation Test
 *
 * This script makes an actual call to the MISTER service and tests
 * the complete flow including saving the response to the database.
 */

const fs = require('fs');
const path = require('path');

// Try to require better-sqlite3 from the packages directory
let Database;
try {
    Database = require('../packages/adapter-sqlite/node_modules/better-sqlite3');
} catch (e) {
    try {
        Database = require('../node_modules/better-sqlite3');
    } catch (e2) {
        console.error('❌ better-sqlite3 not found. Please run: pnpm install');
        process.exit(1);
    }
}

console.log('🔥 Real MISTER Delegation Test');
console.log('==============================');

// Configuration
const TEST_DB_PATH = './test_real_mister.sqlite';
const EXPECTED_DIMENSIONS = process.env.USE_OPENAI_EMBEDDING?.toLowerCase() === 'true' ? 1536 : 384;

console.log(`Expected embedding dimensions: ${EXPECTED_DIMENSIONS}`);
console.log(`USE_OPENAI_EMBEDDING: ${process.env.USE_OPENAI_EMBEDDING}`);

// Clean up any existing test database
if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
}

// Create test database with same schema as ElizaOS
const db = new Database(TEST_DB_PATH);

// Create memories table (same as ElizaOS)
db.exec(`
    CREATE TABLE IF NOT EXISTS memories (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        embedding BLOB NOT NULL,
        userId TEXT,
        roomId TEXT,
        agentId TEXT,
        "unique" INTEGER DEFAULT 1 NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
`);

console.log('\n🚀 Making real call to MISTER service...');

async function testRealMisterDelegation() {
    try {
        // Step 1: Make actual call to MISTER service
        console.log('\n1️⃣ Sending request to MISTER service...');

        const requestBody = {
            messages: [
                {
                    role: "user",
                    content: "what is the price of $mister?"
                }
            ],
            metadata: {
                source: "eliza",
                userId: "test-user-123",
                conversationId: "test-conversation-123"
            }
        };

        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await fetch('https://misterexc6.ngrok.io/api/agents/MISTERAgent/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`MISTER service error: ${response.status} ${response.statusText}`);
        }

        const misterResponseData = await response.json();
        const misterResponse = misterResponseData.text || JSON.stringify(misterResponseData);
        console.log('✅ MISTER response received:', misterResponse);

        // Step 2: Test embedding generation for the response
        console.log('\n2️⃣ Testing embedding generation for MISTER response...');

        // Import the actual ElizaOS embedding function
        const { embed, getEmbeddingZeroVector } = await import('../packages/core/dist/index.js');

        // Create a mock runtime object
        const mockRuntime = {
            character: {
                modelProvider: 'openai',
                settings: {}
            },
            messageManager: {
                getCachedEmbeddings: async () => []
            }
        };

        let responseEmbedding;
        try {
            responseEmbedding = await embed(mockRuntime, misterResponse);
            console.log(`✅ Embedding generated successfully: ${responseEmbedding.length} dimensions`);
        } catch (error) {
            console.log('❌ Embedding generation failed:', error.message);
            console.log('🛡️ Using zero vector fallback...');
            responseEmbedding = getEmbeddingZeroVector();
        }

        console.log(`📏 Response embedding dimensions: ${responseEmbedding.length}`);
        console.log(`📏 Expected dimensions: ${EXPECTED_DIMENSIONS}`);

        if (responseEmbedding.length !== EXPECTED_DIMENSIONS) {
            console.log('🚨 DIMENSION MISMATCH DETECTED!');
            console.log(`   Expected: ${EXPECTED_DIMENSIONS}`);
            console.log(`   Actual: ${responseEmbedding.length}`);
        } else {
            console.log('✅ Embedding dimensions match expected values');
        }

        // Step 3: Test saving to database
        console.log('\n3️⃣ Testing database save operation...');

        try {
            const stmt = db.prepare(`
                INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                'mister-response-real',
                'messages',
                JSON.stringify({ text: misterResponse, source: 'MISTER' }),
                new Float32Array(responseEmbedding),
                'test-user-123',
                'test-room-123',
                'test-agent-123',
                1,
                Date.now()
            );

            console.log('✅ MISTER response saved to database successfully');
        } catch (error) {
            console.log('❌ Database save failed:', error.message);
        }

        // Step 4: Test similarity search (this is where the error usually occurs)
        console.log('\n4️⃣ Testing similarity search with new message...');

        const newMessage = "tell me more about mister";
        let newMessageEmbedding;

        try {
            newMessageEmbedding = await embed(mockRuntime, newMessage);
            console.log(`✅ New message embedding generated: ${newMessageEmbedding.length} dimensions`);
        } catch (error) {
            console.log('❌ New message embedding failed:', error.message);
            newMessageEmbedding = getEmbeddingZeroVector();
        }

        // Try to save the new message (this should trigger similarity search)
        try {
            const stmt = db.prepare(`
                INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                'new-message-real',
                'messages',
                JSON.stringify({ text: newMessage }),
                new Float32Array(newMessageEmbedding),
                'test-user-123',
                'test-room-123',
                'test-agent-123',
                1,
                Date.now()
            );

            console.log('✅ New message saved to database successfully');
        } catch (error) {
            console.log('❌ New message save failed:', error.message);
        }

        // Step 5: Check database contents
        console.log('\n5️⃣ Checking database contents...');

        const memories = db.prepare('SELECT id, LENGTH(embedding) as embedding_size FROM memories').all();
        console.log('\n📊 Database contents:');
        memories.forEach(memory => {
            const dimensions = memory.embedding_size / 4;
            const status = dimensions === EXPECTED_DIMENSIONS ? '✅' : '❌';
            console.log(`   ${status} ${memory.id}: ${dimensions} dimensions`);
        });

        // Step 6: Test vector comparison (if sqlite-vec is available)
        console.log('\n6️⃣ Testing vector comparison...');

        try {
            // This would normally trigger the dimension mismatch error
            const testQuery = db.prepare(`
                SELECT id, LENGTH(embedding) as size
                FROM memories
                WHERE LENGTH(embedding) = ?
            `);

            const results = testQuery.all(EXPECTED_DIMENSIONS * 4); // 4 bytes per float
            console.log(`✅ Found ${results.length} memories with correct dimensions`);

            const wrongResults = db.prepare(`
                SELECT id, LENGTH(embedding) as size
                FROM memories
                WHERE LENGTH(embedding) != ?
            `).all(EXPECTED_DIMENSIONS * 4);

            if (wrongResults.length > 0) {
                console.log(`⚠️ Found ${wrongResults.length} memories with wrong dimensions`);
                wrongResults.forEach(result => {
                    const dims = result.size / 4;
                    console.log(`   - ${result.id}: ${dims} dimensions`);
                });
            }

        } catch (error) {
            console.log('❌ Vector comparison test failed:', error.message);
        }

    } catch (error) {
        console.error('\n❌ Test failed:', error);
        console.error('Stack trace:', error.stack);
    } finally {
        db.close();
        fs.unlinkSync(TEST_DB_PATH);
    }
}

// Run the test
testRealMisterDelegation().then(() => {
    console.log('\n🎉 Real MISTER delegation test completed!');
}).catch(error => {
    console.error('\n💥 Test crashed:', error);
    process.exit(1);
});
