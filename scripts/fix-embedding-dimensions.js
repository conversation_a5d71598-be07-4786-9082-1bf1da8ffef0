#!/usr/bin/env node

/**
 * Database Embedding Dimension Fix Script
 *
 * This script fixes the embedding dimension mismatch error by:
 * 1. Backing up the current database
 * 2. Clearing all embeddings with incorrect dimensions
 * 3. Preserving non-embedding data
 *
 * Usage: node scripts/fix-embedding-dimensions.js
 */

const fs = require('fs');
const path = require('path');

// Try to require better-sqlite3 from the packages directory
let Database;
try {
    Database = require('../packages/adapter-sqlite/node_modules/better-sqlite3');
} catch (e) {
    try {
        Database = require('../node_modules/better-sqlite3');
    } catch (e2) {
        console.error('❌ better-sqlite3 not found. Please run: pnpm install');
        process.exit(1);
    }
}

// Configuration
const DB_PATH = './agent/data/db.sqlite';
const BACKUP_PATH = `./agent/data/db_backup_${Date.now()}.sqlite`;
const EXPECTED_DIMENSIONS = process.env.USE_OPENAI_EMBEDDING?.toLowerCase() === 'true' ? 1536 : 384;

console.log('🔧 ElizaOS Embedding Dimension Fix Script');
console.log('==========================================');
console.log(`Expected embedding dimensions: ${EXPECTED_DIMENSIONS}`);
console.log(`Database path: ${DB_PATH}`);

// Check if database exists
if (!fs.existsSync(DB_PATH)) {
    console.log('❌ Database file not found. Nothing to fix.');
    process.exit(0);
}

// Create backup
console.log('\n📦 Creating database backup...');
try {
    fs.copyFileSync(DB_PATH, BACKUP_PATH);
    console.log(`✅ Backup created: ${BACKUP_PATH}`);
} catch (error) {
    console.error('❌ Failed to create backup:', error.message);
    process.exit(1);
}

// Open database
console.log('\n🔍 Analyzing database...');
const db = new Database(DB_PATH);

try {
    // Get memory count before cleanup
    const totalMemories = db.prepare('SELECT COUNT(*) as count FROM memories').get().count;
    console.log(`Total memories in database: ${totalMemories}`);

    // Check for embeddings with different dimensions
    const memoriesWithEmbeddings = db.prepare('SELECT id, LENGTH(embedding) as embedding_size FROM memories WHERE embedding IS NOT NULL').all();

    console.log(`Memories with embeddings: ${memoriesWithEmbeddings.length}`);

    // Group by embedding size
    const dimensionGroups = {};
    memoriesWithEmbeddings.forEach(memory => {
        const size = memory.embedding_size;
        if (!dimensionGroups[size]) {
            dimensionGroups[size] = 0;
        }
        dimensionGroups[size]++;
    });

    console.log('\n📊 Embedding dimension distribution:');
    Object.entries(dimensionGroups).forEach(([size, count]) => {
        const dimensions = size / 4; // Float32Array uses 4 bytes per float
        const status = dimensions === EXPECTED_DIMENSIONS ? '✅' : '❌';
        console.log(`  ${status} ${dimensions} dimensions: ${count} memories`);
    });

    // Check if all embeddings have correct dimensions
    const incorrectDimensions = Object.keys(dimensionGroups)
        .map(size => size / 4)
        .filter(dims => dims !== EXPECTED_DIMENSIONS);

    if (incorrectDimensions.length === 0) {
        console.log('\n✅ All embeddings already have correct dimensions! No cleanup needed.');
        console.log('🎉 Database is already in good state.');
        db.close();

        // Remove the backup since no changes were made
        fs.unlinkSync(BACKUP_PATH);
        console.log('🗑️ Removed unnecessary backup file.');
        process.exit(0);
    }

    // Option 1: Delete memories with incorrect embeddings (since embedding is NOT NULL)
    console.log('\n🧹 Removing memories with incorrect embedding dimensions...');
    let totalRemoved = 0;
    incorrectDimensions.forEach(dims => {
        const bytesSize = dims * 4;
        const result = db.prepare('DELETE FROM memories WHERE LENGTH(embedding) = ?').run(bytesSize);
        console.log(`  Removed ${result.changes} memories with ${dims} dimensions`);
        totalRemoved += result.changes;
    });

    console.log(`✅ Total memories removed: ${totalRemoved}`);

    // Verify cleanup
    const remainingMemories = db.prepare('SELECT COUNT(*) as count FROM memories').get().count;
    const remainingEmbeddings = db.prepare('SELECT COUNT(*) as count FROM memories WHERE embedding IS NOT NULL').get().count;
    console.log(`\n✅ Cleanup complete. Remaining memories: ${remainingMemories}`);
    console.log(`✅ Remaining embeddings: ${remainingEmbeddings}`);

    console.log('\n🎉 Database fix completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Restart your ElizaOS agent');
    console.log('2. New embeddings will be generated with correct dimensions');
    console.log('3. Monitor logs for any remaining dimension mismatch errors');
    console.log(`4. If needed, restore from backup: ${BACKUP_PATH}`);

} catch (error) {
    console.error('\n❌ Error during database fix:', error.message);
    console.log(`\n🔄 Restoring from backup: ${BACKUP_PATH}`);

    try {
        db.close();
        fs.copyFileSync(BACKUP_PATH, DB_PATH);
        console.log('✅ Database restored from backup');
    } catch (restoreError) {
        console.error('❌ Failed to restore backup:', restoreError.message);
    }

    process.exit(1);
} finally {
    db.close();
}

console.log('\n🔧 Fix script completed.');
