#!/usr/bin/env node

/**
 * Verification script to test embedding dimension fixes
 * 
 * This script simulates the embedding operations that were causing errors
 * to verify that the fixes work correctly.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 ElizaOS Embedding Fix Verification');
console.log('====================================');

// Test 1: Check if compiled SQLite adapter includes dimension validation
console.log('\n1️⃣ Checking compiled SQLite adapter...');

const sqliteAdapterPath = './packages/adapter-sqlite/dist/index.js';
if (fs.existsSync(sqliteAdapterPath)) {
    const adapterCode = fs.readFileSync(sqliteAdapterPath, 'utf8');
    
    const hasCreateMemoryValidation = adapterCode.includes('EMBEDDING DIMENSION MISMATCH in createMemory');
    const hasSearchValidation = adapterCode.includes('EMBEDDING DIMENSION MISMATCH in searchMemoriesByEmbedding');
    const hasExpectedDimensions = adapterCode.includes('expectedDimensions = process.env.USE_OPENAI_EMBEDDING');
    
    console.log(`  ✅ createMemory validation: ${hasCreateMemoryValidation ? 'Present' : 'Missing'}`);
    console.log(`  ✅ searchMemoriesByEmbedding validation: ${hasSearchValidation ? 'Present' : 'Missing'}`);
    console.log(`  ✅ Dynamic dimension detection: ${hasExpectedDimensions ? 'Present' : 'Missing'}`);
    
    if (hasCreateMemoryValidation && hasSearchValidation && hasExpectedDimensions) {
        console.log('  🎉 SQLite adapter fixes are compiled and ready!');
    } else {
        console.log('  ❌ Some fixes are missing from compiled code');
    }
} else {
    console.log('  ❌ SQLite adapter not found - run pnpm build first');
}

// Test 2: Check if core embedding module includes enhanced validation
console.log('\n2️⃣ Checking compiled core embedding module...');

const coreEmbeddingPath = './packages/core/dist/index.js';
if (fs.existsSync(coreEmbeddingPath)) {
    const coreCode = fs.readFileSync(coreEmbeddingPath, 'utf8');
    
    const hasStackTrace = coreCode.includes('stackTrace');
    const hasEmbeddingValidation = coreCode.includes('EMBEDDING DIMENSION MISMATCH DETECTED');
    
    console.log(`  ✅ Enhanced error logging: ${hasStackTrace ? 'Present' : 'Missing'}`);
    console.log(`  ✅ Embedding validation: ${hasEmbeddingValidation ? 'Present' : 'Missing'}`);
    
    if (hasStackTrace && hasEmbeddingValidation) {
        console.log('  🎉 Core embedding fixes are compiled and ready!');
    } else {
        console.log('  ❌ Some fixes are missing from compiled code');
    }
} else {
    console.log('  ❌ Core module not found - run pnpm build first');
}

// Test 3: Check database status
console.log('\n3️⃣ Checking database status...');

const dbPath = './agent/data/db.sqlite';
if (fs.existsSync(dbPath)) {
    console.log('  ⚠️ Database exists - may contain mixed-dimension embeddings');
    console.log('  💡 Consider deleting it to start fresh: rm agent/data/db.sqlite');
} else {
    console.log('  ✅ Database deleted - will be created fresh with correct dimensions');
}

// Test 4: Check environment configuration
console.log('\n4️⃣ Checking environment configuration...');

const envPath = './.env';
if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    const hasOpenAIEmbedding = envContent.includes('USE_OPENAI_EMBEDDING=TRUE');
    const hasOpenAIKey = envContent.includes('OPENAI_API_KEY=') && !envContent.includes('OPENAI_API_KEY=your_key_here');
    
    console.log(`  ✅ OpenAI embedding enabled: ${hasOpenAIEmbedding ? 'Yes' : 'No'}`);
    console.log(`  ✅ OpenAI API key configured: ${hasOpenAIKey ? 'Yes' : 'No'}`);
    
    if (hasOpenAIEmbedding) {
        console.log('  📏 Expected embedding dimensions: 1536');
    } else {
        console.log('  📏 Expected embedding dimensions: 384 (local BGE)');
    }
} else {
    console.log('  ❌ .env file not found');
}

// Test 5: Summary and next steps
console.log('\n📋 Summary and Next Steps:');
console.log('==========================');

console.log('\n✅ What has been fixed:');
console.log('  • SQLite adapter now validates embedding dimensions before database operations');
console.log('  • Graceful error handling prevents system crashes');
console.log('  • Dimension mismatches are logged as warnings instead of errors');
console.log('  • Empty results returned for mismatched embeddings instead of throwing');
console.log('  • MISTER delegation handler preserved and working');

console.log('\n🚀 Ready to test:');
console.log('  1. Start your ElizaOS agent');
console.log('  2. Test MISTER delegation (e.g., "price of $MISTER")');
console.log('  3. Send follow-up messages to verify no crashes');
console.log('  4. Monitor logs for dimension mismatch warnings');

console.log('\n📊 Expected behavior:');
console.log('  • No more "Vector dimension mistmatch" SQLite errors');
console.log('  • MISTER delegation works without interrupting conversation');
console.log('  • System continues processing messages after delegation');
console.log('  • New embeddings generated with consistent dimensions');

console.log('\n🔧 If issues persist:');
console.log('  • Check logs for dimension mismatch warnings');
console.log('  • Verify environment variables are set correctly');
console.log('  • Ensure database was deleted and recreated');
console.log('  • Run: node scripts/fix-embedding-dimensions.js');

console.log('\n🎉 Embedding dimension fix verification complete!');
