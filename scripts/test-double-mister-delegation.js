#!/usr/bin/env node

/**
 * Double MISTER Delegation Test
 * 
 * This script tests the exact scenario that was causing the embedding dimension error:
 * 1. Call MISTER and save response to database
 * 2. Immediately call MISTER again and save response
 * 3. Verify no dimension mismatch errors occur
 */

const fs = require('fs');
const path = require('path');

// Try to require better-sqlite3 from the packages directory
let Database;
try {
    Database = require('../packages/adapter-sqlite/node_modules/better-sqlite3');
} catch (e) {
    try {
        Database = require('../node_modules/better-sqlite3');
    } catch (e2) {
        console.error('❌ better-sqlite3 not found. Please run: pnpm install');
        process.exit(1);
    }
}

console.log('🔥🔥 Double MISTER Delegation Test');
console.log('==================================');

// Configuration
const TEST_DB_PATH = './test_double_mister.sqlite';
const EXPECTED_DIMENSIONS = process.env.USE_OPENAI_EMBEDDING?.toLowerCase() === 'true' ? 1536 : 384;

console.log(`Expected embedding dimensions: ${EXPECTED_DIMENSIONS}`);
console.log(`USE_OPENAI_EMBEDDING: ${process.env.USE_OPENAI_EMBEDDING}`);

// Clean up any existing test database
if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
}

// Create test database with same schema as ElizaOS
const db = new Database(TEST_DB_PATH);

// Create memories table (same as ElizaOS)
db.exec(`
    CREATE TABLE IF NOT EXISTS memories (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        embedding BLOB NOT NULL,
        userId TEXT,
        roomId TEXT,
        agentId TEXT,
        "unique" INTEGER DEFAULT 1 NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
`);

console.log('\n🚀 Testing double MISTER delegation...');

async function callMisterService(message, callNumber) {
    console.log(`\n${callNumber}️⃣ MISTER Call #${callNumber}: "${message}"`);
    
    const requestBody = {
        messages: [
            {
                role: "user",
                content: message
            }
        ],
        metadata: {
            source: "eliza",
            userId: `test-user-${callNumber}`,
            conversationId: `test-conversation-${callNumber}`
        }
    };

    console.log(`   📤 Sending request to MISTER...`);

    const response = await fetch('https://misterexc6.ngrok.io/api/agents/MISTERAgent/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        throw new Error(`MISTER service error: ${response.status} ${response.statusText}`);
    }

    const misterResponseData = await response.json();
    const misterResponse = misterResponseData.text || JSON.stringify(misterResponseData);
    console.log(`   ✅ MISTER response: ${misterResponse.substring(0, 100)}...`);
    
    return misterResponse;
}

async function processResponse(response, callNumber) {
    console.log(`   🧠 Processing response #${callNumber}...`);
    
    // Import the actual ElizaOS embedding function
    const { embed, getEmbeddingZeroVector } = await import('../packages/core/dist/index.js');
    
    // Create a mock runtime object
    const mockRuntime = {
        character: {
            modelProvider: 'openai',
            settings: {}
        },
        messageManager: {
            getCachedEmbeddings: async () => []
        }
    };

    let responseEmbedding;
    try {
        responseEmbedding = await embed(mockRuntime, response);
        console.log(`   ✅ Embedding generated: ${responseEmbedding.length} dimensions`);
    } catch (error) {
        console.log(`   ⚠️ Embedding generation failed: ${error.message}`);
        console.log(`   🛡️ Using zero vector fallback...`);
        responseEmbedding = getEmbeddingZeroVector();
    }

    console.log(`   📏 Embedding dimensions: ${responseEmbedding.length}`);
    
    if (responseEmbedding.length !== EXPECTED_DIMENSIONS) {
        console.log(`   🚨 DIMENSION MISMATCH! Expected: ${EXPECTED_DIMENSIONS}, Got: ${responseEmbedding.length}`);
        return false;
    }

    // Save to database
    try {
        const stmt = db.prepare(`
            INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        stmt.run(
            `mister-response-${callNumber}`,
            'messages',
            JSON.stringify({ text: response, source: 'MISTER', call: callNumber }),
            new Float32Array(responseEmbedding),
            `test-user-${callNumber}`,
            `test-room-${callNumber}`,
            'test-agent-123',
            1,
            Date.now()
        );
        
        console.log(`   ✅ Response #${callNumber} saved to database`);
        return true;
    } catch (error) {
        console.log(`   ❌ Database save failed: ${error.message}`);
        return false;
    }
}

async function testDoubleMisterDelegation() {
    try {
        console.log('\n🎯 Starting double delegation test...');
        
        // First call to MISTER
        const response1 = await callMisterService("what is the price of $mister?", 1);
        const success1 = await processResponse(response1, 1);
        
        if (!success1) {
            console.log('❌ First call failed, aborting test');
            return false;
        }
        
        console.log('\n⏱️ Waiting 2 seconds before second call...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Second call to MISTER (this is where the error used to occur)
        const response2 = await callMisterService("tell me about $SNEK price", 2);
        const success2 = await processResponse(response2, 2);
        
        if (!success2) {
            console.log('❌ Second call failed');
            return false;
        }
        
        // Check database state
        console.log('\n📊 Checking database state...');
        const memories = db.prepare('SELECT id, LENGTH(embedding) as embedding_size FROM memories ORDER BY createdAt').all();
        
        console.log(`   Total memories: ${memories.length}`);
        memories.forEach(memory => {
            const dimensions = memory.embedding_size / 4;
            const status = dimensions === EXPECTED_DIMENSIONS ? '✅' : '❌';
            console.log(`   ${status} ${memory.id}: ${dimensions} dimensions`);
        });
        
        // Test similarity search between the two responses
        console.log('\n🔍 Testing similarity search simulation...');
        
        try {
            // This simulates what happens when a new message comes in after MISTER responses
            const testQuery = db.prepare(`
                SELECT id, LENGTH(embedding) as size, content
                FROM memories 
                ORDER BY createdAt DESC
                LIMIT 2
            `);
            
            const recentMemories = testQuery.all();
            console.log(`   ✅ Retrieved ${recentMemories.length} recent memories`);
            
            // Check for dimension consistency
            const dimensions = recentMemories.map(m => m.size / 4);
            const allSameDimensions = dimensions.every(d => d === dimensions[0]);
            
            if (allSameDimensions) {
                console.log(`   ✅ All memories have consistent dimensions: ${dimensions[0]}`);
            } else {
                console.log(`   ❌ Inconsistent dimensions found: ${dimensions.join(', ')}`);
                return false;
            }
            
        } catch (error) {
            console.log(`   ❌ Similarity search simulation failed: ${error.message}`);
            return false;
        }
        
        console.log('\n🎉 SUCCESS! Double MISTER delegation completed without errors!');
        console.log('\n📋 Test Results:');
        console.log('   ✅ First MISTER call: Success');
        console.log('   ✅ First response saved: Success');
        console.log('   ✅ Second MISTER call: Success');
        console.log('   ✅ Second response saved: Success');
        console.log('   ✅ Database consistency: Success');
        console.log('   ✅ No dimension mismatch errors: Success');
        
        return true;
        
    } catch (error) {
        console.error('\n💥 Test failed with error:', error);
        console.error('Stack trace:', error.stack);
        return false;
    } finally {
        db.close();
        if (fs.existsSync(TEST_DB_PATH)) {
            fs.unlinkSync(TEST_DB_PATH);
        }
    }
}

// Run the test
testDoubleMisterDelegation().then((success) => {
    if (success) {
        console.log('\n🚀 Double MISTER delegation test PASSED!');
        console.log('🎯 Your agent should now work correctly with back-to-back MISTER calls!');
        process.exit(0);
    } else {
        console.log('\n💥 Double MISTER delegation test FAILED!');
        process.exit(1);
    }
}).catch(error => {
    console.error('\n💥 Test crashed:', error);
    process.exit(1);
});
