#!/usr/bin/env node

/**
 * Test MISTER delegation flow to identify where dimension mismatch occurs
 * 
 * This script simulates the exact flow that causes the embedding dimension error
 */

const fs = require('fs');
const path = require('path');

// Try to require better-sqlite3 from the packages directory
let Database;
try {
    Database = require('../packages/adapter-sqlite/node_modules/better-sqlite3');
} catch (e) {
    try {
        Database = require('../node_modules/better-sqlite3');
    } catch (e2) {
        console.error('❌ better-sqlite3 not found. Please run: pnpm install');
        process.exit(1);
    }
}

console.log('🧪 MISTER Delegation Flow Test');
console.log('==============================');

// Configuration
const DB_PATH = './agent/data/test_delegation.sqlite';
const EXPECTED_DIMENSIONS = process.env.USE_OPENAI_EMBEDDING?.toLowerCase() === 'true' ? 1536 : 384;

console.log(`Expected embedding dimensions: ${EXPECTED_DIMENSIONS}`);

// Clean up any existing test database
if (fs.existsSync(DB_PATH)) {
    fs.unlinkSync(DB_PATH);
}

// Create test database with same schema as ElizaOS
const db = new Database(DB_PATH);

// Create memories table (simplified version)
db.exec(`
    CREATE TABLE IF NOT EXISTS memories (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        embedding BLOB NOT NULL,
        userId TEXT,
        roomId TEXT,
        agentId TEXT,
        "unique" INTEGER DEFAULT 1 NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
`);

console.log('\n🔬 Testing delegation flow...');

// Simulate the delegation flow that causes the error
try {
    console.log('\n1️⃣ Simulating user message: "what is the price of $mister?"');
    
    // Step 1: User message gets processed and saved (this works fine)
    const userEmbedding = new Float32Array(EXPECTED_DIMENSIONS).fill(0.1);
    const userMemoryStmt = db.prepare(`
        INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    userMemoryStmt.run(
        'user-msg-1',
        'messages',
        JSON.stringify({ text: 'what is the price of $mister?' }),
        userEmbedding,
        'user123',
        'room123',
        'agent123',
        1,
        Date.now()
    );
    console.log('✅ User message saved successfully');

    console.log('\n2️⃣ Simulating MISTER delegation response...');
    
    // Step 2: MISTER responds with price data
    const misterResponse = "The current price of MISTER is approximately 0.00017027 ADA. No recent trading volume recorded in the last hour.";
    
    // This is where the issue might be - when the MISTER response gets saved
    // Let's test different embedding scenarios
    
    console.log('\n3️⃣ Testing different embedding scenarios for MISTER response...');
    
    // Scenario A: Correct dimensions (1536 for OpenAI)
    console.log('\n   A) Testing with correct dimensions (1536)...');
    try {
        const correctEmbedding = new Float32Array(1536).fill(0.2);
        const correctStmt = db.prepare(`
            INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        correctStmt.run(
            'mister-response-correct',
            'messages',
            JSON.stringify({ text: misterResponse, source: 'MISTER' }),
            correctEmbedding,
            'agent123',
            'room123',
            'agent123',
            1,
            Date.now()
        );
        console.log('      ✅ Correct dimensions: Success');
    } catch (error) {
        console.log('      ❌ Correct dimensions: Failed -', error.message);
    }

    // Scenario B: Wrong dimensions (6144) - this might be what MISTER is sending
    console.log('\n   B) Testing with wrong dimensions (6144)...');
    try {
        const wrongEmbedding6144 = new Float32Array(6144).fill(0.3);
        const wrongStmt = db.prepare(`
            INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        wrongStmt.run(
            'mister-response-wrong-6144',
            'messages',
            JSON.stringify({ text: misterResponse, source: 'MISTER' }),
            wrongEmbedding6144,
            'agent123',
            'room123',
            'agent123',
            1,
            Date.now()
        );
        console.log('      ✅ Wrong dimensions (6144): Success (stored but will cause comparison errors)');
    } catch (error) {
        console.log('      ❌ Wrong dimensions (6144): Failed -', error.message);
    }

    // Scenario C: Wrong dimensions (384) - legacy BGE
    console.log('\n   C) Testing with wrong dimensions (384)...');
    try {
        const wrongEmbedding384 = new Float32Array(384).fill(0.4);
        const wrongStmt384 = db.prepare(`
            INSERT INTO memories (id, type, content, embedding, userId, roomId, agentId, "unique", createdAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        wrongStmt384.run(
            'mister-response-wrong-384',
            'messages',
            JSON.stringify({ text: misterResponse, source: 'MISTER' }),
            wrongEmbedding384,
            'agent123',
            'room123',
            'agent123',
            1,
            Date.now()
        );
        console.log('      ✅ Wrong dimensions (384): Success (stored but will cause comparison errors)');
    } catch (error) {
        console.log('      ❌ Wrong dimensions (384): Failed -', error.message);
    }

    console.log('\n4️⃣ Testing vector comparison (this is where the error occurs)...');
    
    // This simulates what happens when the next message comes in and tries to do similarity search
    console.log('\n   Testing similarity search with mixed dimensions...');
    
    // Load sqlite-vec extension (this would normally be done by ElizaOS)
    try {
        // Note: In real ElizaOS, this would be loaded by the SQLite adapter
        console.log('   ⚠️ Note: sqlite-vec extension not available in test environment');
        console.log('   In production, this would trigger the dimension mismatch error');
    } catch (error) {
        console.log('   ⚠️ sqlite-vec extension not available in test');
    }

    // Check what we have in the database
    const memories = db.prepare('SELECT id, LENGTH(embedding) as embedding_size FROM memories').all();
    console.log('\n📊 Database contents:');
    memories.forEach(memory => {
        const dimensions = memory.embedding_size / 4;
        const status = dimensions === EXPECTED_DIMENSIONS ? '✅' : '❌';
        console.log(`   ${status} ${memory.id}: ${dimensions} dimensions`);
    });

    console.log('\n🔍 Analysis:');
    console.log('============');
    
    const dimensionGroups = {};
    memories.forEach(memory => {
        const dimensions = memory.embedding_size / 4;
        if (!dimensionGroups[dimensions]) {
            dimensionGroups[dimensions] = 0;
        }
        dimensionGroups[dimensions]++;
    });

    console.log('\nDimension distribution:');
    Object.entries(dimensionGroups).forEach(([dims, count]) => {
        const status = parseInt(dims) === EXPECTED_DIMENSIONS ? '✅' : '❌';
        console.log(`  ${status} ${dims} dimensions: ${count} memories`);
    });

    if (Object.keys(dimensionGroups).length > 1) {
        console.log('\n🚨 PROBLEM IDENTIFIED:');
        console.log('   Mixed embedding dimensions detected!');
        console.log('   This will cause "Vector dimension mistmatch" errors when:');
        console.log('   1. Next message comes in');
        console.log('   2. System tries to do similarity search');
        console.log('   3. SQLite vec_distance_L2 compares different dimension vectors');
        
        console.log('\n💡 SOLUTION:');
        console.log('   The MISTER delegation system is creating embeddings with wrong dimensions');
        console.log('   Need to ensure MISTER responses use the same embedding model as main system');
    } else {
        console.log('\n✅ No dimension mismatch detected in this test');
    }

} catch (error) {
    console.error('\n❌ Error during delegation flow test:', error);
} finally {
    db.close();
    fs.unlinkSync(DB_PATH);
}

console.log('\n🎯 Key Findings:');
console.log('================');
console.log('1. The error occurs when MISTER delegation responses are saved with wrong embedding dimensions');
console.log('2. The SQLite adapter fixes should prevent the crash, but we need to fix the root cause');
console.log('3. MISTER responses should NOT include embeddings - let ElizaOS generate them');
console.log('4. Check if MISTER service is somehow sending embeddings with responses');

console.log('\n📝 Next Steps:');
console.log('==============');
console.log('1. Verify MISTER delegation handlers don\'t create embeddings');
console.log('2. Check if callback responses include embedding data');
console.log('3. Ensure all Memory objects from MISTER have NO embedding property');
console.log('4. Test with your actual agent to see the exact flow');

console.log('\n🔧 Test completed!');
