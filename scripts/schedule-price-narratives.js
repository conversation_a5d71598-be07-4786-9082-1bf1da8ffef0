// Script to schedule price narrative posts
const cron = require('node-cron');
const { spawn } = require('child_process');
const path = require('path');

// Configuration
const SCRIPT_PATH = path.join(__dirname, 'generate-price-narratives.js');

// Schedule for ADA posts - every 6 hours
cron.schedule('0 */6 * * *', () => {
  console.log('Running scheduled ADA price narrative post...');
  runScript();
});

// Function to run the script
function runScript() {
  const process = spawn('node', [SCRIPT_PATH]);
  
  process.stdout.on('data', (data) => {
    console.log(`stdout: ${data}`);
  });
  
  process.stderr.on('data', (data) => {
    console.error(`stderr: ${data}`);
  });
  
  process.on('close', (code) => {
    console.log(`Child process exited with code ${code}`);
  });
}

console.log('Price narrative scheduler started');
console.log('Next posts will be generated according to the schedule');

// Run once at startup
setTimeout(() => {
  console.log('Running initial price narrative posts...');
  runScript();
}, 60000); // Wait 1 minute after startup
