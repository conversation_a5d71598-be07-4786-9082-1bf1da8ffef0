#!/usr/bin/env node

/**
 * Direct test of MISTER API to debug the connection issue
 */

async function testMisterAPI() {
    console.log('🔍 Testing MISTER API directly...\n');
    
    const baseURL = "https://misterexc6.ngrok.io";
    const endpoint = "/api/agents/MISTERAgent/generate";
    
    const requestBody = {
        messages: [
            {
                role: "user",
                content: "What's the price of ADA?"
            }
        ],
        metadata: {
            source: "eliza",
            userId: "test-user",
            conversationId: "test-room"
        }
    };
    
    console.log('📤 Request URL:', `${baseURL}${endpoint}`);
    console.log('📤 Request Body:', JSON.stringify(requestBody, null, 2));
    console.log('\n⏳ Sending request...\n');
    
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);
        
        const response = await fetch(`${baseURL}${endpoint}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: JSON.stringify(requestBody),
            signal: controller.signal,
        });
        
        clearTimeout(timeoutId);
        
        console.log('📥 Response Status:', response.status, response.statusText);
        console.log('📥 Response Headers:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
            const errorText = await response.text();
            console.log('❌ Error Response Body:', errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('✅ Success Response:', JSON.stringify(data, null, 2));
        
        if (data.success) {
            console.log('\n🎉 MISTER API is working correctly!');
            console.log('💬 Response:', data.response);
        } else {
            console.log('\n⚠️ MISTER API returned error:', data.error);
        }
        
    } catch (error) {
        console.log('\n❌ Connection Error:', error.message);
        
        if (error.name === "AbortError") {
            console.log('⏰ Request timed out after 30 seconds');
        } else if (error.message.includes("fetch")) {
            console.log('🌐 Network connectivity issue');
        }
        
        // Test if the base URL is reachable
        console.log('\n🔍 Testing base URL connectivity...');
        try {
            const healthResponse = await fetch(baseURL, { method: "GET" });
            console.log('🏥 Base URL Status:', healthResponse.status);
        } catch (healthError) {
            console.log('🏥 Base URL Error:', healthError.message);
        }
    }
}

testMisterAPI().catch(console.error);
