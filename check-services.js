// Simple script to check if the transcription service is properly registered
const { createNodePlugin } = require('./packages/plugin-node/dist/index.js');
const { ServiceType } = require('./packages/core/dist/index.js');

// Create the node plugin
const nodePlugin = createNodePlugin();

// Check if the transcription service is registered
const transcriptionService = nodePlugin.services.find(service => service.type === ServiceType.TRANSCRIPTION);

console.log('Node Plugin Services:');
nodePlugin.services.forEach(service => {
  console.log(`- ${service.type}: ${service.constructor.name}`);
});

console.log('\nTranscription Service Found:', !!transcriptionService);
if (transcriptionService) {
  console.log('Transcription Service Type:', transcriptionService.type);
  console.log('Transcription Service Class:', transcriptionService.constructor.name);
}
