// <PERSON><PERSON>t to fix the Discord client implementation
const fs = require('fs');
const path = require('path');

// Path to the Discord client file
const discordClientPath = path.join(__dirname, 'packages', 'client-discord', 'src', 'client.ts');

// Read the Discord client file
let discordClientContent = fs.readFileSync(discordClientPath, 'utf8');

// Add import for ServiceType
if (!discordClientContent.includes('ServiceType')) {
  discordClientContent = discordClientContent.replace(
    'import {',
    'import {\n    ServiceType,'
  );
  console.log('Added ServiceType import');
}

// Write the updated Discord client file
fs.writeFileSync(discordClientPath, discordClientContent);
console.log('Updated Discord client file');

// Path to the voice.ts file
const voicePath = path.join(__dirname, 'packages', 'client-discord', 'src', 'voice.ts');

// Read the voice.ts file
let voiceContent = fs.readFileSync(voicePath, 'utf8');

// Update the processTranscription method to handle errors better
const oldProcessTranscription = `            // Get the transcription service
            const transcriptionService = this.runtime.getService<ITranscriptionService>(ServiceType.TRANSCRIPTION);

            if (!transcriptionService) {
                console.error("[2025-05-12 01:45:01] ERROR: Service transcription not found");
                throw new TypeError("Cannot read properties of null (reading 'transcribe')");
            }`;

const newProcessTranscription = `            // Get the transcription service
            const transcriptionService = this.runtime.getService<ITranscriptionService>(ServiceType.TRANSCRIPTION);

            if (!transcriptionService) {
                elizaLogger.error("Service transcription not found");
                elizaLogger.error("Available services:", Array.from(this.runtime.services.keys()).join(", "));
                elizaLogger.error("Make sure @elizaos/plugin-node is properly registered in the character file");
                return;
            }`;

voiceContent = voiceContent.replace(oldProcessTranscription, newProcessTranscription);
console.log('Updated voice.ts file');

// Write the updated voice.ts file
fs.writeFileSync(voicePath, voiceContent);

console.log('Please run the following commands to apply the changes:');
console.log('pnpm build');
console.log('pnpm restart');
