lockfileVersion: "9.0"

settings:
    autoInstallPeers: true
    excludeLinksFromLockfile: false

importers:
    .:
        dependencies:
            "@assistant-ui/react":
                specifier: ^0.7.33
                version: 0.7.33(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(tailwindcss@3.4.17)
            "@radix-ui/react-avatar":
                specifier: ^1.1.2
                version: 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-collapsible":
                specifier: ^1.1.2
                version: 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-dialog":
                specifier: ^1.1.4
                version: 1.1.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-label":
                specifier: ^2.1.1
                version: 2.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-separator":
                specifier: ^1.1.1
                version: 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-slot":
                specifier: ^1.1.1
                version: 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-tabs":
                specifier: ^1.1.2
                version: 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-toast":
                specifier: ^1.2.4
                version: 1.2.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-tooltip":
                specifier: ^1.1.6
                version: 1.1.6(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@remix-run/node":
                specifier: ^2.15.2
                version: 2.15.2(typescript@5.6.3)
            "@remix-run/react":
                specifier: ^2.15.2
                version: 2.15.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.6.3)
            "@remix-run/serve":
                specifier: ^2.15.2
                version: 2.15.2(typescript@5.6.3)
            "@tanstack/react-query":
                specifier: ^5.62.15
                version: 5.62.16(react@18.3.1)
            class-variance-authority:
                specifier: ^0.7.1
                version: 0.7.1
            clsx:
                specifier: 2.1.1
                version: 2.1.1
            dayjs:
                specifier: ^1.11.13
                version: 1.11.13
            framer-motion:
                specifier: ^11.16.0
                version: 11.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            isbot:
                specifier: ^4.1.0
                version: 4.4.0
            lucide-react:
                specifier: ^0.469.0
                version: 0.469.0(react@18.3.1)
            react:
                specifier: ^18.3.1
                version: 18.3.1
            react-aiwriter:
                specifier: ^1.0.0
                version: 1.0.0
            react-dom:
                specifier: ^18.3.1
                version: 18.3.1(react@18.3.1)
            tailwind-merge:
                specifier: ^2.6.0
                version: 2.6.0
            tailwindcss-animate:
                specifier: ^1.0.7
                version: 1.0.7(tailwindcss@3.4.17)
            use-sound:
                specifier: ^4.0.3
                version: 4.0.3(react@18.3.1)
        devDependencies:
            "@eslint/js":
                specifier: ^9.17.0
                version: 9.17.0
            "@remix-run/dev":
                specifier: ^2.15.2
                version: 2.15.2(@remix-run/react@2.15.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.6.3))(@remix-run/serve@2.15.2(typescript@5.6.3))(@types/node@22.10.5)(typescript@5.6.3)(vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0))
            "@types/react":
                specifier: ^18.3.18
                version: 18.3.18
            "@types/react-dom":
                specifier: ^18.3.5
                version: 18.3.5(@types/react@18.3.18)
            "@typescript-eslint/eslint-plugin":
                specifier: ^6.7.4
                version: 6.21.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/parser":
                specifier: ^6.7.4
                version: 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@vitejs/plugin-react-swc":
                specifier: ^3.5.0
                version: 3.7.2(vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0))
            autoprefixer:
                specifier: ^10.4.19
                version: 10.4.20(postcss@8.4.49)
            eslint:
                specifier: ^9.17.0
                version: 9.17.0(jiti@1.21.7)
            eslint-import-resolver-typescript:
                specifier: ^3.6.1
                version: 3.7.0(eslint-plugin-import@2.31.0)(eslint@9.17.0(jiti@1.21.7))
            eslint-plugin-import:
                specifier: ^2.28.1
                version: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.7))
            eslint-plugin-jsx-a11y:
                specifier: ^6.7.1
                version: 6.10.2(eslint@9.17.0(jiti@1.21.7))
            eslint-plugin-react:
                specifier: ^7.33.2
                version: 7.37.3(eslint@9.17.0(jiti@1.21.7))
            eslint-plugin-react-hooks:
                specifier: ^5.0.0
                version: 5.1.0(eslint@9.17.0(jiti@1.21.7))
            eslint-plugin-react-refresh:
                specifier: ^0.4.16
                version: 0.4.16(eslint@9.17.0(jiti@1.21.7))
            globals:
                specifier: ^15.14.0
                version: 15.14.0
            postcss:
                specifier: ^8.4.38
                version: 8.4.49
            tailwindcss:
                specifier: ^3.4.4
                version: 3.4.17
            typescript:
                specifier: ~5.6.2
                version: 5.6.3
            typescript-eslint:
                specifier: ^8.18.2
                version: 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            vite:
                specifier: ^6.0.5
                version: 6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0)
            vite-tsconfig-paths:
                specifier: ^4.2.1
                version: 4.3.2(typescript@5.6.3)(vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0))

packages:
    "@ai-sdk/provider@1.0.6":
        resolution:
            {
                integrity: sha512-lJi5zwDosvvZER3e/pB8lj1MN3o3S7zJliQq56BRr4e9V3fcRyFtwP0JRxaRS5vHYX3OJ154VezVoQNrk0eaKw==,
            }
        engines: { node: ">=18" }

    "@alloc/quick-lru@5.2.0":
        resolution:
            {
                integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==,
            }
        engines: { node: ">=10" }

    "@ampproject/remapping@2.3.0":
        resolution:
            {
                integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==,
            }
        engines: { node: ">=6.0.0" }

    "@assistant-ui/react@0.7.33":
        resolution:
            {
                integrity: sha512-vFE8dU8lM/BQ7oeQ00eb0JsA/AC1Xl3WpSm38zRSBRKRlcnoZQUhpT/9WurCFx6BnzKk90fxVZmPs6J+3EOQRg==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^18 || ^19 || ^19.0.0-rc
            react-dom: ^18 || ^19 || ^19.0.0-rc
            tailwindcss: ^3.4.4
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true
            tailwindcss:
                optional: true

    "@babel/code-frame@7.26.2":
        resolution:
            {
                integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/compat-data@7.26.3":
        resolution:
            {
                integrity: sha512-nHIxvKPniQXpmQLb0vhY3VaFb3S0YrTAwpOWJZh1wn3oJPjJk9Asva204PsBdmAE8vpzfHudT8DB0scYvy9q0g==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/core@7.26.0":
        resolution:
            {
                integrity: sha512-i1SLeK+DzNnQ3LL/CswPCa/E5u4lh1k6IAEphON8F+cXt0t9euTshDru0q7/IqMa1PMPz5RnHuHscF8/ZJsStg==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/generator@7.26.3":
        resolution:
            {
                integrity: sha512-6FF/urZvD0sTeO7k6/B15pMLC4CHUv1426lzr3N01aHJTl046uCAh9LXW/fzeXXjPNCJ6iABW5XaWOsIZB93aQ==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-annotate-as-pure@7.25.9":
        resolution:
            {
                integrity: sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-compilation-targets@7.25.9":
        resolution:
            {
                integrity: sha512-j9Db8Suy6yV/VHa4qzrj9yZfZxhLWQdVnRlXxmKLYlhWUVB1sB2G5sxuWYXk/whHD9iW76PmNzxZ4UCnTQTVEQ==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-create-class-features-plugin@7.25.9":
        resolution:
            {
                integrity: sha512-UTZQMvt0d/rSz6KI+qdu7GQze5TIajwTS++GUozlw8VBJDEOAqSXwm1WvmYEZwqdqSGQshRocPDqrt4HBZB3fQ==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0

    "@babel/helper-member-expression-to-functions@7.25.9":
        resolution:
            {
                integrity: sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-module-imports@7.25.9":
        resolution:
            {
                integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-module-transforms@7.26.0":
        resolution:
            {
                integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0

    "@babel/helper-optimise-call-expression@7.25.9":
        resolution:
            {
                integrity: sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-plugin-utils@7.25.9":
        resolution:
            {
                integrity: sha512-kSMlyUVdWe25rEsRGviIgOWnoT/nfABVWlqt9N19/dIPWViAOW2s9wznP5tURbs/IDuNk4gPy3YdYRgH3uxhBw==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-replace-supers@7.25.9":
        resolution:
            {
                integrity: sha512-IiDqTOTBQy0sWyeXyGSC5TBJpGFXBkRynjBeXsvbhQFKj2viwJC76Epz35YLU1fpe/Am6Vppb7W7zM4fPQzLsQ==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0

    "@babel/helper-skip-transparent-expression-wrappers@7.25.9":
        resolution:
            {
                integrity: sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-string-parser@7.25.9":
        resolution:
            {
                integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-validator-identifier@7.25.9":
        resolution:
            {
                integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helper-validator-option@7.25.9":
        resolution:
            {
                integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/helpers@7.26.0":
        resolution:
            {
                integrity: sha512-tbhNuIxNcVb21pInl3ZSjksLCvgdZy9KwJ8brv993QtIVKJBBkYXz4q4ZbAv31GdnC+R90np23L5FbEBlthAEw==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/parser@7.26.3":
        resolution:
            {
                integrity: sha512-WJ/CvmY8Mea8iDXo6a7RK2wbmJITT5fN3BEkRuFlxVyNx8jOKIIhmC4fSkTcPcf8JyavbBwIe6OpiCOBXt/IcA==,
            }
        engines: { node: ">=6.0.0" }
        hasBin: true

    "@babel/plugin-syntax-decorators@7.25.9":
        resolution:
            {
                integrity: sha512-ryzI0McXUPJnRCvMo4lumIKZUzhYUO/ScI+Mz4YVaTLt04DHNSjEUjKVvbzQjZFLuod/cYEc07mJWhzl6v4DPg==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0-0

    "@babel/plugin-syntax-jsx@7.25.9":
        resolution:
            {
                integrity: sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0-0

    "@babel/plugin-syntax-typescript@7.25.9":
        resolution:
            {
                integrity: sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0-0

    "@babel/plugin-transform-modules-commonjs@7.26.3":
        resolution:
            {
                integrity: sha512-MgR55l4q9KddUDITEzEFYn5ZsGDXMSsU9E+kh7fjRXTIC3RHqfCo8RPRbyReYJh44HQ/yomFkqbOFohXvDCiIQ==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0-0

    "@babel/plugin-transform-typescript@7.26.3":
        resolution:
            {
                integrity: sha512-6+5hpdr6mETwSKjmJUdYw0EIkATiQhnELWlE3kJFBwSg/BGIVwVaVbX+gOXBCdc7Ln1RXZxyWGecIXhUfnl7oA==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0-0

    "@babel/preset-typescript@7.26.0":
        resolution:
            {
                integrity: sha512-NMk1IGZ5I/oHhoXEElcm+xUnL/szL6xflkFZmoEU9xj1qSJXpiS7rsspYo92B4DRCDvZn2erT5LdsCeXAKNCkg==,
            }
        engines: { node: ">=6.9.0" }
        peerDependencies:
            "@babel/core": ^7.0.0-0

    "@babel/runtime@7.26.0":
        resolution:
            {
                integrity: sha512-FDSOghenHTiToteC/QRlv2q3DhPZ/oOXTBoirfWNx1Cx3TMVcGWQtMMmQcSvb/JjpNeGzx8Pq/b4fKEJuWm1sw==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/template@7.25.9":
        resolution:
            {
                integrity: sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/traverse@7.26.4":
        resolution:
            {
                integrity: sha512-fH+b7Y4p3yqvApJALCPJcwb0/XaOSgtK4pzV6WVjPR5GLFQBRI7pfoX2V2iM48NXvX07NUxxm1Vw98YjqTcU5w==,
            }
        engines: { node: ">=6.9.0" }

    "@babel/types@7.26.3":
        resolution:
            {
                integrity: sha512-vN5p+1kl59GVKMvTHt55NzzmYVxprfJD+ql7U9NFIfKCBkYE55LYtS+WtPlaYOyzydrKI8Nezd+aZextrd+FMA==,
            }
        engines: { node: ">=6.9.0" }

    "@emotion/hash@0.9.2":
        resolution:
            {
                integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==,
            }

    "@esbuild/aix-ppc64@0.21.5":
        resolution:
            {
                integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==,
            }
        engines: { node: ">=12" }
        cpu: [ppc64]
        os: [aix]

    "@esbuild/aix-ppc64@0.24.2":
        resolution:
            {
                integrity: sha512-thpVCb/rhxE/BnMLQ7GReQLLN8q9qbHmI55F4489/ByVg2aQaQ6kbcLb6FHkocZzQhxc4gx0sCk0tJkKBFzDhA==,
            }
        engines: { node: ">=18" }
        cpu: [ppc64]
        os: [aix]

    "@esbuild/android-arm64@0.17.6":
        resolution:
            {
                integrity: sha512-YnYSCceN/dUzUr5kdtUzB+wZprCafuD89Hs0Aqv9QSdwhYQybhXTaSTcrl6X/aWThn1a/j0eEpUBGOE7269REg==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [android]

    "@esbuild/android-arm64@0.21.5":
        resolution:
            {
                integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [android]

    "@esbuild/android-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-cNLgeqCqV8WxfcTIOeL4OAtSmL8JjcN6m09XIgro1Wi7cF4t/THaWEa7eL5CMoMBdjoHOTh/vwTO/o2TRXIyzg==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [android]

    "@esbuild/android-arm@0.17.6":
        resolution:
            {
                integrity: sha512-bSC9YVUjADDy1gae8RrioINU6e1lCkg3VGVwm0QQ2E1CWcC4gnMce9+B6RpxuSsrsXsk1yojn7sp1fnG8erE2g==,
            }
        engines: { node: ">=12" }
        cpu: [arm]
        os: [android]

    "@esbuild/android-arm@0.21.5":
        resolution:
            {
                integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==,
            }
        engines: { node: ">=12" }
        cpu: [arm]
        os: [android]

    "@esbuild/android-arm@0.24.2":
        resolution:
            {
                integrity: sha512-tmwl4hJkCfNHwFB3nBa8z1Uy3ypZpxqxfTQOcHX+xRByyYgunVbZ9MzUUfb0RxaHIMnbHagwAxuTL+tnNM+1/Q==,
            }
        engines: { node: ">=18" }
        cpu: [arm]
        os: [android]

    "@esbuild/android-x64@0.17.6":
        resolution:
            {
                integrity: sha512-MVcYcgSO7pfu/x34uX9u2QIZHmXAB7dEiLQC5bBl5Ryqtpj9lT2sg3gNDEsrPEmimSJW2FXIaxqSQ501YLDsZQ==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [android]

    "@esbuild/android-x64@0.21.5":
        resolution:
            {
                integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [android]

    "@esbuild/android-x64@0.24.2":
        resolution:
            {
                integrity: sha512-B6Q0YQDqMx9D7rvIcsXfmJfvUYLoP722bgfBlO5cGvNVb5V/+Y7nhBE3mHV9OpxBf4eAS2S68KZztiPaWq4XYw==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [android]

    "@esbuild/darwin-arm64@0.17.6":
        resolution:
            {
                integrity: sha512-bsDRvlbKMQMt6Wl08nHtFz++yoZHsyTOxnjfB2Q95gato+Yi4WnRl13oC2/PJJA9yLCoRv9gqT/EYX0/zDsyMA==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [darwin]

    "@esbuild/darwin-arm64@0.21.5":
        resolution:
            {
                integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [darwin]

    "@esbuild/darwin-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-kj3AnYWc+CekmZnS5IPu9D+HWtUI49hbnyqk0FLEJDbzCIQt7hg7ucF1SQAilhtYpIujfaHr6O0UHlzzSPdOeA==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [darwin]

    "@esbuild/darwin-x64@0.17.6":
        resolution:
            {
                integrity: sha512-xh2A5oPrYRfMFz74QXIQTQo8uA+hYzGWJFoeTE8EvoZGHb+idyV4ATaukaUvnnxJiauhs/fPx3vYhU4wiGfosg==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [darwin]

    "@esbuild/darwin-x64@0.21.5":
        resolution:
            {
                integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [darwin]

    "@esbuild/darwin-x64@0.24.2":
        resolution:
            {
                integrity: sha512-WeSrmwwHaPkNR5H3yYfowhZcbriGqooyu3zI/3GGpF8AyUdsrrP0X6KumITGA9WOyiJavnGZUwPGvxvwfWPHIA==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [darwin]

    "@esbuild/freebsd-arm64@0.17.6":
        resolution:
            {
                integrity: sha512-EnUwjRc1inT4ccZh4pB3v1cIhohE2S4YXlt1OvI7sw/+pD+dIE4smwekZlEPIwY6PhU6oDWwITrQQm5S2/iZgg==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [freebsd]

    "@esbuild/freebsd-arm64@0.21.5":
        resolution:
            {
                integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [freebsd]

    "@esbuild/freebsd-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-UN8HXjtJ0k/Mj6a9+5u6+2eZ2ERD7Edt1Q9IZiB5UZAIdPnVKDoG7mdTVGhHJIeEml60JteamR3qhsr1r8gXvg==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [freebsd]

    "@esbuild/freebsd-x64@0.17.6":
        resolution:
            {
                integrity: sha512-Uh3HLWGzH6FwpviUcLMKPCbZUAFzv67Wj5MTwK6jn89b576SR2IbEp+tqUHTr8DIl0iDmBAf51MVaP7pw6PY5Q==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [freebsd]

    "@esbuild/freebsd-x64@0.21.5":
        resolution:
            {
                integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [freebsd]

    "@esbuild/freebsd-x64@0.24.2":
        resolution:
            {
                integrity: sha512-TvW7wE/89PYW+IevEJXZ5sF6gJRDY/14hyIGFXdIucxCsbRmLUcjseQu1SyTko+2idmCw94TgyaEZi9HUSOe3Q==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [freebsd]

    "@esbuild/linux-arm64@0.17.6":
        resolution:
            {
                integrity: sha512-bUR58IFOMJX523aDVozswnlp5yry7+0cRLCXDsxnUeQYJik1DukMY+apBsLOZJblpH+K7ox7YrKrHmJoWqVR9w==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [linux]

    "@esbuild/linux-arm64@0.21.5":
        resolution:
            {
                integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [linux]

    "@esbuild/linux-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-7HnAD6074BW43YvvUmE/35Id9/NB7BeX5EoNkK9obndmZBUk8xmJJeU7DwmUeN7tkysslb2eSl6CTrYz6oEMQg==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [linux]

    "@esbuild/linux-arm@0.17.6":
        resolution:
            {
                integrity: sha512-7YdGiurNt7lqO0Bf/U9/arrPWPqdPqcV6JCZda4LZgEn+PTQ5SMEI4MGR52Bfn3+d6bNEGcWFzlIxiQdS48YUw==,
            }
        engines: { node: ">=12" }
        cpu: [arm]
        os: [linux]

    "@esbuild/linux-arm@0.21.5":
        resolution:
            {
                integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==,
            }
        engines: { node: ">=12" }
        cpu: [arm]
        os: [linux]

    "@esbuild/linux-arm@0.24.2":
        resolution:
            {
                integrity: sha512-n0WRM/gWIdU29J57hJyUdIsk0WarGd6To0s+Y+LwvlC55wt+GT/OgkwoXCXvIue1i1sSNWblHEig00GBWiJgfA==,
            }
        engines: { node: ">=18" }
        cpu: [arm]
        os: [linux]

    "@esbuild/linux-ia32@0.17.6":
        resolution:
            {
                integrity: sha512-ujp8uoQCM9FRcbDfkqECoARsLnLfCUhKARTP56TFPog8ie9JG83D5GVKjQ6yVrEVdMie1djH86fm98eY3quQkQ==,
            }
        engines: { node: ">=12" }
        cpu: [ia32]
        os: [linux]

    "@esbuild/linux-ia32@0.21.5":
        resolution:
            {
                integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==,
            }
        engines: { node: ">=12" }
        cpu: [ia32]
        os: [linux]

    "@esbuild/linux-ia32@0.24.2":
        resolution:
            {
                integrity: sha512-sfv0tGPQhcZOgTKO3oBE9xpHuUqguHvSo4jl+wjnKwFpapx+vUDcawbwPNuBIAYdRAvIDBfZVvXprIj3HA+Ugw==,
            }
        engines: { node: ">=18" }
        cpu: [ia32]
        os: [linux]

    "@esbuild/linux-loong64@0.17.6":
        resolution:
            {
                integrity: sha512-y2NX1+X/Nt+izj9bLoiaYB9YXT/LoaQFYvCkVD77G/4F+/yuVXYCWz4SE9yr5CBMbOxOfBcy/xFL4LlOeNlzYQ==,
            }
        engines: { node: ">=12" }
        cpu: [loong64]
        os: [linux]

    "@esbuild/linux-loong64@0.21.5":
        resolution:
            {
                integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==,
            }
        engines: { node: ">=12" }
        cpu: [loong64]
        os: [linux]

    "@esbuild/linux-loong64@0.24.2":
        resolution:
            {
                integrity: sha512-CN9AZr8kEndGooS35ntToZLTQLHEjtVB5n7dl8ZcTZMonJ7CCfStrYhrzF97eAecqVbVJ7APOEe18RPI4KLhwQ==,
            }
        engines: { node: ">=18" }
        cpu: [loong64]
        os: [linux]

    "@esbuild/linux-mips64el@0.17.6":
        resolution:
            {
                integrity: sha512-09AXKB1HDOzXD+j3FdXCiL/MWmZP0Ex9eR8DLMBVcHorrWJxWmY8Nms2Nm41iRM64WVx7bA/JVHMv081iP2kUA==,
            }
        engines: { node: ">=12" }
        cpu: [mips64el]
        os: [linux]

    "@esbuild/linux-mips64el@0.21.5":
        resolution:
            {
                integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==,
            }
        engines: { node: ">=12" }
        cpu: [mips64el]
        os: [linux]

    "@esbuild/linux-mips64el@0.24.2":
        resolution:
            {
                integrity: sha512-iMkk7qr/wl3exJATwkISxI7kTcmHKE+BlymIAbHO8xanq/TjHaaVThFF6ipWzPHryoFsesNQJPE/3wFJw4+huw==,
            }
        engines: { node: ">=18" }
        cpu: [mips64el]
        os: [linux]

    "@esbuild/linux-ppc64@0.17.6":
        resolution:
            {
                integrity: sha512-AmLhMzkM8JuqTIOhxnX4ubh0XWJIznEynRnZAVdA2mMKE6FAfwT2TWKTwdqMG+qEaeyDPtfNoZRpJbD4ZBv0Tg==,
            }
        engines: { node: ">=12" }
        cpu: [ppc64]
        os: [linux]

    "@esbuild/linux-ppc64@0.21.5":
        resolution:
            {
                integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==,
            }
        engines: { node: ">=12" }
        cpu: [ppc64]
        os: [linux]

    "@esbuild/linux-ppc64@0.24.2":
        resolution:
            {
                integrity: sha512-shsVrgCZ57Vr2L8mm39kO5PPIb+843FStGt7sGGoqiiWYconSxwTiuswC1VJZLCjNiMLAMh34jg4VSEQb+iEbw==,
            }
        engines: { node: ">=18" }
        cpu: [ppc64]
        os: [linux]

    "@esbuild/linux-riscv64@0.17.6":
        resolution:
            {
                integrity: sha512-Y4Ri62PfavhLQhFbqucysHOmRamlTVK10zPWlqjNbj2XMea+BOs4w6ASKwQwAiqf9ZqcY9Ab7NOU4wIgpxwoSQ==,
            }
        engines: { node: ">=12" }
        cpu: [riscv64]
        os: [linux]

    "@esbuild/linux-riscv64@0.21.5":
        resolution:
            {
                integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==,
            }
        engines: { node: ">=12" }
        cpu: [riscv64]
        os: [linux]

    "@esbuild/linux-riscv64@0.24.2":
        resolution:
            {
                integrity: sha512-4eSFWnU9Hhd68fW16GD0TINewo1L6dRrB+oLNNbYyMUAeOD2yCK5KXGK1GH4qD/kT+bTEXjsyTCiJGHPZ3eM9Q==,
            }
        engines: { node: ">=18" }
        cpu: [riscv64]
        os: [linux]

    "@esbuild/linux-s390x@0.17.6":
        resolution:
            {
                integrity: sha512-SPUiz4fDbnNEm3JSdUW8pBJ/vkop3M1YwZAVwvdwlFLoJwKEZ9L98l3tzeyMzq27CyepDQ3Qgoba44StgbiN5Q==,
            }
        engines: { node: ">=12" }
        cpu: [s390x]
        os: [linux]

    "@esbuild/linux-s390x@0.21.5":
        resolution:
            {
                integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==,
            }
        engines: { node: ">=12" }
        cpu: [s390x]
        os: [linux]

    "@esbuild/linux-s390x@0.24.2":
        resolution:
            {
                integrity: sha512-S0Bh0A53b0YHL2XEXC20bHLuGMOhFDO6GN4b3YjRLK//Ep3ql3erpNcPlEFed93hsQAjAQDNsvcK+hV90FubSw==,
            }
        engines: { node: ">=18" }
        cpu: [s390x]
        os: [linux]

    "@esbuild/linux-x64@0.17.6":
        resolution:
            {
                integrity: sha512-a3yHLmOodHrzuNgdpB7peFGPx1iJ2x6m+uDvhP2CKdr2CwOaqEFMeSqYAHU7hG+RjCq8r2NFujcd/YsEsFgTGw==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [linux]

    "@esbuild/linux-x64@0.21.5":
        resolution:
            {
                integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [linux]

    "@esbuild/linux-x64@0.24.2":
        resolution:
            {
                integrity: sha512-8Qi4nQcCTbLnK9WoMjdC9NiTG6/E38RNICU6sUNqK0QFxCYgoARqVqxdFmWkdonVsvGqWhmm7MO0jyTqLqwj0Q==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [linux]

    "@esbuild/netbsd-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-wuLK/VztRRpMt9zyHSazyCVdCXlpHkKm34WUyinD2lzK07FAHTq0KQvZZlXikNWkDGoT6x3TD51jKQ7gMVpopw==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [netbsd]

    "@esbuild/netbsd-x64@0.17.6":
        resolution:
            {
                integrity: sha512-EanJqcU/4uZIBreTrnbnre2DXgXSa+Gjap7ifRfllpmyAU7YMvaXmljdArptTHmjrkkKm9BK6GH5D5Yo+p6y5A==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [netbsd]

    "@esbuild/netbsd-x64@0.21.5":
        resolution:
            {
                integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [netbsd]

    "@esbuild/netbsd-x64@0.24.2":
        resolution:
            {
                integrity: sha512-VefFaQUc4FMmJuAxmIHgUmfNiLXY438XrL4GDNV1Y1H/RW3qow68xTwjZKfj/+Plp9NANmzbH5R40Meudu8mmw==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [netbsd]

    "@esbuild/openbsd-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-YQbi46SBct6iKnszhSvdluqDmxCJA+Pu280Av9WICNwQmMxV7nLRHZfjQzwbPs3jeWnuAhE9Jy0NrnJ12Oz+0A==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [openbsd]

    "@esbuild/openbsd-x64@0.17.6":
        resolution:
            {
                integrity: sha512-xaxeSunhQRsTNGFanoOkkLtnmMn5QbA0qBhNet/XLVsc+OVkpIWPHcr3zTW2gxVU5YOHFbIHR9ODuaUdNza2Vw==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [openbsd]

    "@esbuild/openbsd-x64@0.21.5":
        resolution:
            {
                integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [openbsd]

    "@esbuild/openbsd-x64@0.24.2":
        resolution:
            {
                integrity: sha512-+iDS6zpNM6EnJyWv0bMGLWSWeXGN/HTaF/LXHXHwejGsVi+ooqDfMCCTerNFxEkM3wYVcExkeGXNqshc9iMaOA==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [openbsd]

    "@esbuild/sunos-x64@0.17.6":
        resolution:
            {
                integrity: sha512-gnMnMPg5pfMkZvhHee21KbKdc6W3GR8/JuE0Da1kjwpK6oiFU3nqfHuVPgUX2rsOx9N2SadSQTIYV1CIjYG+xw==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [sunos]

    "@esbuild/sunos-x64@0.21.5":
        resolution:
            {
                integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [sunos]

    "@esbuild/sunos-x64@0.24.2":
        resolution:
            {
                integrity: sha512-hTdsW27jcktEvpwNHJU4ZwWFGkz2zRJUz8pvddmXPtXDzVKTTINmlmga3ZzwcuMpUvLw7JkLy9QLKyGpD2Yxig==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [sunos]

    "@esbuild/win32-arm64@0.17.6":
        resolution:
            {
                integrity: sha512-G95n7vP1UnGJPsVdKXllAJPtqjMvFYbN20e8RK8LVLhlTiSOH1sd7+Gt7rm70xiG+I5tM58nYgwWrLs6I1jHqg==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [win32]

    "@esbuild/win32-arm64@0.21.5":
        resolution:
            {
                integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [win32]

    "@esbuild/win32-arm64@0.24.2":
        resolution:
            {
                integrity: sha512-LihEQ2BBKVFLOC9ZItT9iFprsE9tqjDjnbulhHoFxYQtQfai7qfluVODIYxt1PgdoyQkz23+01rzwNwYfutxUQ==,
            }
        engines: { node: ">=18" }
        cpu: [arm64]
        os: [win32]

    "@esbuild/win32-ia32@0.17.6":
        resolution:
            {
                integrity: sha512-96yEFzLhq5bv9jJo5JhTs1gI+1cKQ83cUpyxHuGqXVwQtY5Eq54ZEsKs8veKtiKwlrNimtckHEkj4mRh4pPjsg==,
            }
        engines: { node: ">=12" }
        cpu: [ia32]
        os: [win32]

    "@esbuild/win32-ia32@0.21.5":
        resolution:
            {
                integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==,
            }
        engines: { node: ">=12" }
        cpu: [ia32]
        os: [win32]

    "@esbuild/win32-ia32@0.24.2":
        resolution:
            {
                integrity: sha512-q+iGUwfs8tncmFC9pcnD5IvRHAzmbwQ3GPS5/ceCyHdjXubwQWI12MKWSNSMYLJMq23/IUCvJMS76PDqXe1fxA==,
            }
        engines: { node: ">=18" }
        cpu: [ia32]
        os: [win32]

    "@esbuild/win32-x64@0.17.6":
        resolution:
            {
                integrity: sha512-n6d8MOyUrNp6G4VSpRcgjs5xj4A91svJSaiwLIDWVWEsZtpN5FA9NlBbZHDmAJc2e8e6SF4tkBD3HAvPF+7igA==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [win32]

    "@esbuild/win32-x64@0.21.5":
        resolution:
            {
                integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [win32]

    "@esbuild/win32-x64@0.24.2":
        resolution:
            {
                integrity: sha512-7VTgWzgMGvup6aSqDPLiW5zHaxYJGTO4OokMjIlrCtf+VpEL+cXKtCvg723iguPYI5oaUNdS+/V7OU2gvXVWEg==,
            }
        engines: { node: ">=18" }
        cpu: [x64]
        os: [win32]

    "@eslint-community/eslint-utils@4.4.1":
        resolution:
            {
                integrity: sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==,
            }
        engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
        peerDependencies:
            eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

    "@eslint-community/regexpp@4.12.1":
        resolution:
            {
                integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==,
            }
        engines: { node: ^12.0.0 || ^14.0.0 || >=16.0.0 }

    "@eslint/config-array@0.19.1":
        resolution:
            {
                integrity: sha512-fo6Mtm5mWyKjA/Chy1BYTdn5mGJoDNjC7C64ug20ADsRDGrA85bN3uK3MaKbeRkRuuIEAR5N33Jr1pbm411/PA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@eslint/core@0.9.1":
        resolution:
            {
                integrity: sha512-GuUdqkyyzQI5RMIWkHhvTWLCyLo1jNK3vzkSyaExH5kHPDHcuL2VOpHjmMY+y3+NC69qAKToBqldTBgYeLSr9Q==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@eslint/eslintrc@3.2.0":
        resolution:
            {
                integrity: sha512-grOjVNN8P3hjJn/eIETF1wwd12DdnwFDoyceUJLYYdkpbwq3nLi+4fqrTAONx7XDALqlL220wC/RHSC/QTI/0w==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@eslint/js@9.17.0":
        resolution:
            {
                integrity: sha512-Sxc4hqcs1kTu0iID3kcZDW3JHq2a77HO9P8CP6YEA/FpH3Ll8UXE2r/86Rz9YJLKme39S9vU5OWNjC6Xl0Cr3w==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@eslint/object-schema@2.1.5":
        resolution:
            {
                integrity: sha512-o0bhxnL89h5Bae5T318nFoFzGy+YE5i/gGkoPAgkmTVdRKTiv3p8JHevPiPaMwoloKfEiiaHlawCqaZMqRm+XQ==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@eslint/plugin-kit@0.2.4":
        resolution:
            {
                integrity: sha512-zSkKow6H5Kdm0ZUQUB2kV5JIXqoG0+uH5YADhaEHswm664N9Db8dXSi0nMJpacpMf+MyyglF1vnZohpEg5yUtg==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@floating-ui/core@1.6.9":
        resolution:
            {
                integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==,
            }

    "@floating-ui/dom@1.6.13":
        resolution:
            {
                integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==,
            }

    "@floating-ui/react-dom@2.1.2":
        resolution:
            {
                integrity: sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==,
            }
        peerDependencies:
            react: ">=16.8.0"
            react-dom: ">=16.8.0"

    "@floating-ui/utils@0.2.9":
        resolution:
            {
                integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==,
            }

    "@humanfs/core@0.19.1":
        resolution:
            {
                integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==,
            }
        engines: { node: ">=18.18.0" }

    "@humanfs/node@0.16.6":
        resolution:
            {
                integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==,
            }
        engines: { node: ">=18.18.0" }

    "@humanwhocodes/module-importer@1.0.1":
        resolution:
            {
                integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==,
            }
        engines: { node: ">=12.22" }

    "@humanwhocodes/retry@0.3.1":
        resolution:
            {
                integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==,
            }
        engines: { node: ">=18.18" }

    "@humanwhocodes/retry@0.4.1":
        resolution:
            {
                integrity: sha512-c7hNEllBlenFTHBky65mhq8WD2kbN9Q6gk0bTk8lSBvc554jpXSkST1iePudpt7+A/AQvuHs9EMqjHDXMY1lrA==,
            }
        engines: { node: ">=18.18" }

    "@isaacs/cliui@8.0.2":
        resolution:
            {
                integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
            }
        engines: { node: ">=12" }

    "@jridgewell/gen-mapping@0.3.8":
        resolution:
            {
                integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==,
            }
        engines: { node: ">=6.0.0" }

    "@jridgewell/resolve-uri@3.1.2":
        resolution:
            {
                integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
            }
        engines: { node: ">=6.0.0" }

    "@jridgewell/set-array@1.2.1":
        resolution:
            {
                integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
            }
        engines: { node: ">=6.0.0" }

    "@jridgewell/sourcemap-codec@1.5.0":
        resolution:
            {
                integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==,
            }

    "@jridgewell/trace-mapping@0.3.25":
        resolution:
            {
                integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
            }

    "@jspm/core@2.0.1":
        resolution:
            {
                integrity: sha512-Lg3PnLp0QXpxwLIAuuJboLeRaIhrgJjeuh797QADg3xz8wGLugQOS5DpsE8A6i6Adgzf+bacllkKZG3J0tGfDw==,
            }

    "@mdx-js/mdx@2.3.0":
        resolution:
            {
                integrity: sha512-jLuwRlz8DQfQNiUCJR50Y09CGPq3fLtmtUQfVrj79E0JWu3dvsVcxVIcfhR5h0iXu+/z++zDrYeiJqifRynJkA==,
            }

    "@nodelib/fs.scandir@2.1.5":
        resolution:
            {
                integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
            }
        engines: { node: ">= 8" }

    "@nodelib/fs.stat@2.0.5":
        resolution:
            {
                integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
            }
        engines: { node: ">= 8" }

    "@nodelib/fs.walk@1.2.8":
        resolution:
            {
                integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
            }
        engines: { node: ">= 8" }

    "@nolyfill/is-core-module@1.0.39":
        resolution:
            {
                integrity: sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==,
            }
        engines: { node: ">=12.4.0" }

    "@npmcli/fs@3.1.1":
        resolution:
            {
                integrity: sha512-q9CRWjpHCMIh5sVyefoD1cA7PkvILqCZsnSOEUUivORLjxCO/Irmue2DprETiNgEqktDBZaM1Bi+jrarx1XdCg==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    "@npmcli/git@4.1.0":
        resolution:
            {
                integrity: sha512-9hwoB3gStVfa0N31ymBmrX+GuDGdVA/QWShZVqE0HK2Af+7QGGrCTbZia/SW0ImUTjTne7SP91qxDmtXvDHRPQ==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    "@npmcli/package-json@4.0.1":
        resolution:
            {
                integrity: sha512-lRCEGdHZomFsURroh522YvA/2cVb9oPIJrjHanCJZkiasz1BzcnLr3tBJhlV7S86MBJBuAQ33is2D60YitZL2Q==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    "@npmcli/promise-spawn@6.0.2":
        resolution:
            {
                integrity: sha512-gGq0NJkIGSwdbUt4yhdF8ZrmkGKVz9vAdVzpOfnom+V8PLSmSOVhZwbNvZZS1EYcJN5hzzKBxmmVVAInM6HQLg==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    "@pkgjs/parseargs@0.11.0":
        resolution:
            {
                integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
            }
        engines: { node: ">=14" }

    "@radix-ui/primitive@1.1.1":
        resolution:
            {
                integrity: sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==,
            }

    "@radix-ui/react-arrow@1.1.1":
        resolution:
            {
                integrity: sha512-NaVpZfmv8SKeZbn4ijN2V3jlHA9ngBG16VnIIm22nUR0Yk8KUALyBxT3KYEUnNuch9sTE8UTsS3whzBgKOL30w==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-avatar@1.1.2":
        resolution:
            {
                integrity: sha512-GaC7bXQZ5VgZvVvsJ5mu/AEbjYLnhhkoidOboC50Z6FFlLA03wG2ianUoH+zgDQ31/9gCF59bE4+2bBgTyMiig==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-collapsible@1.1.2":
        resolution:
            {
                integrity: sha512-PliMB63vxz7vggcyq0IxNYk8vGDrLXVWw4+W4B8YnwI1s18x7YZYqlG9PLX7XxAJUi0g2DxP4XKJMFHh/iVh9A==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-collection@1.1.1":
        resolution:
            {
                integrity: sha512-LwT3pSho9Dljg+wY2KN2mrrh6y3qELfftINERIzBUO9e0N+t0oMTyn3k9iv+ZqgrwGkRnLpNJrsMv9BZlt2yuA==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-compose-refs@1.1.1":
        resolution:
            {
                integrity: sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-context@1.1.1":
        resolution:
            {
                integrity: sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-dialog@1.1.4":
        resolution:
            {
                integrity: sha512-Ur7EV1IwQGCyaAuyDRiOLA5JIUZxELJljF+MbM/2NC0BYwfuRrbpS30BiQBJrVruscgUkieKkqXYDOoByaxIoA==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-direction@1.1.0":
        resolution:
            {
                integrity: sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-dismissable-layer@1.1.3":
        resolution:
            {
                integrity: sha512-onrWn/72lQoEucDmJnr8uczSNTujT0vJnA/X5+3AkChVPowr8n1yvIKIabhWyMQeMvvmdpsvcyDqx3X1LEXCPg==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-focus-guards@1.1.1":
        resolution:
            {
                integrity: sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-focus-scope@1.1.1":
        resolution:
            {
                integrity: sha512-01omzJAYRxXdG2/he/+xy+c8a8gCydoQ1yOxnWNcRhrrBW5W+RQJ22EK1SaO8tb3WoUsuEw7mJjBozPzihDFjA==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-id@1.1.0":
        resolution:
            {
                integrity: sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-label@2.1.1":
        resolution:
            {
                integrity: sha512-UUw5E4e/2+4kFMH7+YxORXGWggtY6sM8WIwh5RZchhLuUg2H1hc98Py+pr8HMz6rdaYrK2t296ZEjYLOCO5uUw==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-popover@1.1.4":
        resolution:
            {
                integrity: sha512-aUACAkXx8LaFymDma+HQVji7WhvEhpFJ7+qPz17Nf4lLZqtreGOFRiNQWQmhzp7kEWg9cOyyQJpdIMUMPc/CPw==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-popper@1.2.1":
        resolution:
            {
                integrity: sha512-3kn5Me69L+jv82EKRuQCXdYyf1DqHwD2U/sxoNgBGCB7K9TRc3bQamQ+5EPM9EvyPdli0W41sROd+ZU1dTCztw==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-portal@1.1.3":
        resolution:
            {
                integrity: sha512-NciRqhXnGojhT93RPyDaMPfLH3ZSl4jjIFbZQ1b/vxvZEdHsBZ49wP9w8L3HzUQwep01LcWtkUvm0OVB5JAHTw==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-presence@1.1.2":
        resolution:
            {
                integrity: sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-primitive@2.0.1":
        resolution:
            {
                integrity: sha512-sHCWTtxwNn3L3fH8qAfnF3WbUZycW93SM1j3NFDzXBiz8D6F5UTTy8G1+WFEaiCdvCVRJWj6N2R4Xq6HdiHmDg==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-roving-focus@1.1.1":
        resolution:
            {
                integrity: sha512-QE1RoxPGJ/Nm8Qmk0PxP8ojmoaS67i0s7hVssS7KuI2FQoc/uzVlZsqKfQvxPE6D8hICCPHJ4D88zNhT3OOmkw==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-separator@1.1.1":
        resolution:
            {
                integrity: sha512-RRiNRSrD8iUiXriq/Y5n4/3iE8HzqgLHsusUSg5jVpU2+3tqcUFPJXHDymwEypunc2sWxDUS3UC+rkZRlHedsw==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-slot@1.1.1":
        resolution:
            {
                integrity: sha512-RApLLOcINYJA+dMVbOju7MYv1Mb2EBp2nH4HdDzXTSyaR5optlm6Otrz1euW3HbdOR8UmmFK06TD+A9frYWv+g==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-tabs@1.1.2":
        resolution:
            {
                integrity: sha512-9u/tQJMcC2aGq7KXpGivMm1mgq7oRJKXphDwdypPd/j21j/2znamPU8WkXgnhUaTrSFNIt8XhOyCAupg8/GbwQ==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-toast@1.2.4":
        resolution:
            {
                integrity: sha512-Sch9idFJHJTMH9YNpxxESqABcAFweJG4tKv+0zo0m5XBvUSL8FM5xKcJLFLXononpePs8IclyX1KieL5SDUNgA==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-tooltip@1.1.6":
        resolution:
            {
                integrity: sha512-TLB5D8QLExS1uDn7+wH/bjEmRurNMTzNrtq7IjaS4kjion9NtzsTGkvR5+i7yc9q01Pi2KMM2cN3f8UG4IvvXA==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/react-use-callback-ref@1.1.0":
        resolution:
            {
                integrity: sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-use-controllable-state@1.1.0":
        resolution:
            {
                integrity: sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-use-escape-keydown@1.1.0":
        resolution:
            {
                integrity: sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-use-layout-effect@1.1.0":
        resolution:
            {
                integrity: sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-use-rect@1.1.0":
        resolution:
            {
                integrity: sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-use-size@1.1.0":
        resolution:
            {
                integrity: sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    "@radix-ui/react-visually-hidden@1.1.1":
        resolution:
            {
                integrity: sha512-vVfA2IZ9q/J+gEamvj761Oq1FpWgCDaNOOIfbPVp2MVPLEomUr5+Vf7kJGwQ24YxZSlQVar7Bes8kyTo5Dshpg==,
            }
        peerDependencies:
            "@types/react": "*"
            "@types/react-dom": "*"
            react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
            react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true
            "@types/react-dom":
                optional: true

    "@radix-ui/rect@1.1.0":
        resolution:
            {
                integrity: sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==,
            }

    "@remix-run/dev@2.15.2":
        resolution:
            {
                integrity: sha512-o8lix8t4GBhtXjo/G1IzwtHVW5GRMs7amtFtBHiR1bhSyK7VyX5qGtTDmJyny5QDv83pxaLOCiE0dUng2BCoyQ==,
            }
        engines: { node: ">=18.0.0" }
        hasBin: true
        peerDependencies:
            "@remix-run/react": ^2.15.2
            "@remix-run/serve": ^2.15.2
            typescript: ^5.1.0
            vite: ^5.1.0
            wrangler: ^3.28.2
        peerDependenciesMeta:
            "@remix-run/serve":
                optional: true
            typescript:
                optional: true
            vite:
                optional: true
            wrangler:
                optional: true

    "@remix-run/express@2.15.2":
        resolution:
            {
                integrity: sha512-54FKQ6/Zj2DCxc4/9tWKUJLPkFakCUf1m7j7a5zp4JGDr436lkZEpS9btfoBZAVq14SIMp5Uc4yt5rUJ1PMORw==,
            }
        engines: { node: ">=18.0.0" }
        peerDependencies:
            express: ^4.20.0
            typescript: ^5.1.0
        peerDependenciesMeta:
            typescript:
                optional: true

    "@remix-run/node@2.15.2":
        resolution:
            {
                integrity: sha512-NS/h5uxje7DYCNgcKqKAiUhf0r2HVnoYUBWLyIIMmCUP1ddWurBP6xTPcWzGhEvV/EvguniYi1wJZ5+X8sonWw==,
            }
        engines: { node: ">=18.0.0" }
        peerDependencies:
            typescript: ^5.1.0
        peerDependenciesMeta:
            typescript:
                optional: true

    "@remix-run/react@2.15.2":
        resolution:
            {
                integrity: sha512-NAAMsSgoC/sdOgovUewwRCE/RUm3F+MBxxZKfwu3POCNeHaplY5qGkH/y8PUXvdN1EBG7Z0Ko43dyzCfcEy5PA==,
            }
        engines: { node: ">=18.0.0" }
        peerDependencies:
            react: ^18.0.0
            react-dom: ^18.0.0
            typescript: ^5.1.0
        peerDependenciesMeta:
            typescript:
                optional: true

    "@remix-run/router@1.21.0":
        resolution:
            {
                integrity: sha512-xfSkCAchbdG5PnbrKqFWwia4Bi61nH+wm8wLEqfHDyp7Y3dZzgqS2itV8i4gAq9pC2HsTpwyBC6Ds8VHZ96JlA==,
            }
        engines: { node: ">=14.0.0" }

    "@remix-run/serve@2.15.2":
        resolution:
            {
                integrity: sha512-m/nZtAUzzGcixNgNc3RNjA1ocFlWAuZFALpZ5fJdPXmITwqRwfjo/1gI+jx7AL7haoo+4j/sAljuAQw2CiswXA==,
            }
        engines: { node: ">=18.0.0" }
        hasBin: true

    "@remix-run/server-runtime@2.15.2":
        resolution:
            {
                integrity: sha512-OqiPcvEnnU88B8b1LIWHHkQ3Tz2GDAmQ1RihFNQsbrFKpDsQLkw0lJlnfgKA/uHd0CEEacpfV7C9qqJT3V6Z2g==,
            }
        engines: { node: ">=18.0.0" }
        peerDependencies:
            typescript: ^5.1.0
        peerDependenciesMeta:
            typescript:
                optional: true

    "@remix-run/web-blob@3.1.0":
        resolution:
            {
                integrity: sha512-owGzFLbqPH9PlKb8KvpNJ0NO74HWE2euAn61eEiyCXX/oteoVzTVSN8mpLgDjaxBf2btj5/nUllSUgpyd6IH6g==,
            }

    "@remix-run/web-fetch@4.4.2":
        resolution:
            {
                integrity: sha512-jgKfzA713/4kAW/oZ4bC3MoLWyjModOVDjFPNseVqcJKSafgIscrYL9G50SurEYLswPuoU3HzSbO0jQCMYWHhA==,
            }
        engines: { node: ^10.17 || >=12.3 }

    "@remix-run/web-file@3.1.0":
        resolution:
            {
                integrity: sha512-dW2MNGwoiEYhlspOAXFBasmLeYshyAyhIdrlXBi06Duex5tDr3ut2LFKVj7tyHLmn8nnNwFf1BjNbkQpygC2aQ==,
            }

    "@remix-run/web-form-data@3.1.0":
        resolution:
            {
                integrity: sha512-NdeohLMdrb+pHxMQ/Geuzdp0eqPbea+Ieo8M8Jx2lGC6TBHsgHzYcBvr0LyPdPVycNRDEpWpiDdCOdCryo3f9A==,
            }

    "@remix-run/web-stream@1.1.0":
        resolution:
            {
                integrity: sha512-KRJtwrjRV5Bb+pM7zxcTJkhIqWWSy+MYsIxHK+0m5atcznsf15YwUBWHWulZerV2+vvHH1Lp1DD7pw6qKW8SgA==,
            }

    "@rollup/rollup-android-arm-eabi@4.30.1":
        resolution:
            {
                integrity: sha512-pSWY+EVt3rJ9fQ3IqlrEUtXh3cGqGtPDH1FQlNZehO2yYxCHEX1SPsz1M//NXwYfbTlcKr9WObLnJX9FsS9K1Q==,
            }
        cpu: [arm]
        os: [android]

    "@rollup/rollup-android-arm64@4.30.1":
        resolution:
            {
                integrity: sha512-/NA2qXxE3D/BRjOJM8wQblmArQq1YoBVJjrjoTSBS09jgUisq7bqxNHJ8kjCHeV21W/9WDGwJEWSN0KQ2mtD/w==,
            }
        cpu: [arm64]
        os: [android]

    "@rollup/rollup-darwin-arm64@4.30.1":
        resolution:
            {
                integrity: sha512-r7FQIXD7gB0WJ5mokTUgUWPl0eYIH0wnxqeSAhuIwvnnpjdVB8cRRClyKLQr7lgzjctkbp5KmswWszlwYln03Q==,
            }
        cpu: [arm64]
        os: [darwin]

    "@rollup/rollup-darwin-x64@4.30.1":
        resolution:
            {
                integrity: sha512-x78BavIwSH6sqfP2xeI1hd1GpHL8J4W2BXcVM/5KYKoAD3nNsfitQhvWSw+TFtQTLZ9OmlF+FEInEHyubut2OA==,
            }
        cpu: [x64]
        os: [darwin]

    "@rollup/rollup-freebsd-arm64@4.30.1":
        resolution:
            {
                integrity: sha512-HYTlUAjbO1z8ywxsDFWADfTRfTIIy/oUlfIDmlHYmjUP2QRDTzBuWXc9O4CXM+bo9qfiCclmHk1x4ogBjOUpUQ==,
            }
        cpu: [arm64]
        os: [freebsd]

    "@rollup/rollup-freebsd-x64@4.30.1":
        resolution:
            {
                integrity: sha512-1MEdGqogQLccphhX5myCJqeGNYTNcmTyaic9S7CG3JhwuIByJ7J05vGbZxsizQthP1xpVx7kd3o31eOogfEirw==,
            }
        cpu: [x64]
        os: [freebsd]

    "@rollup/rollup-linux-arm-gnueabihf@4.30.1":
        resolution:
            {
                integrity: sha512-PaMRNBSqCx7K3Wc9QZkFx5+CX27WFpAMxJNiYGAXfmMIKC7jstlr32UhTgK6T07OtqR+wYlWm9IxzennjnvdJg==,
            }
        cpu: [arm]
        os: [linux]

    "@rollup/rollup-linux-arm-musleabihf@4.30.1":
        resolution:
            {
                integrity: sha512-B8Rcyj9AV7ZlEFqvB5BubG5iO6ANDsRKlhIxySXcF1axXYUyqwBok+XZPgIYGBgs7LDXfWfifxhw0Ik57T0Yug==,
            }
        cpu: [arm]
        os: [linux]

    "@rollup/rollup-linux-arm64-gnu@4.30.1":
        resolution:
            {
                integrity: sha512-hqVyueGxAj3cBKrAI4aFHLV+h0Lv5VgWZs9CUGqr1z0fZtlADVV1YPOij6AhcK5An33EXaxnDLmJdQikcn5NEw==,
            }
        cpu: [arm64]
        os: [linux]

    "@rollup/rollup-linux-arm64-musl@4.30.1":
        resolution:
            {
                integrity: sha512-i4Ab2vnvS1AE1PyOIGp2kXni69gU2DAUVt6FSXeIqUCPIR3ZlheMW3oP2JkukDfu3PsexYRbOiJrY+yVNSk9oA==,
            }
        cpu: [arm64]
        os: [linux]

    "@rollup/rollup-linux-loongarch64-gnu@4.30.1":
        resolution:
            {
                integrity: sha512-fARcF5g296snX0oLGkVxPmysetwUk2zmHcca+e9ObOovBR++9ZPOhqFUM61UUZ2EYpXVPN1redgqVoBB34nTpQ==,
            }
        cpu: [loong64]
        os: [linux]

    "@rollup/rollup-linux-powerpc64le-gnu@4.30.1":
        resolution:
            {
                integrity: sha512-GLrZraoO3wVT4uFXh67ElpwQY0DIygxdv0BNW9Hkm3X34wu+BkqrDrkcsIapAY+N2ATEbvak0XQ9gxZtCIA5Rw==,
            }
        cpu: [ppc64]
        os: [linux]

    "@rollup/rollup-linux-riscv64-gnu@4.30.1":
        resolution:
            {
                integrity: sha512-0WKLaAUUHKBtll0wvOmh6yh3S0wSU9+yas923JIChfxOaaBarmb/lBKPF0w/+jTVozFnOXJeRGZ8NvOxvk/jcw==,
            }
        cpu: [riscv64]
        os: [linux]

    "@rollup/rollup-linux-s390x-gnu@4.30.1":
        resolution:
            {
                integrity: sha512-GWFs97Ruxo5Bt+cvVTQkOJ6TIx0xJDD/bMAOXWJg8TCSTEK8RnFeOeiFTxKniTc4vMIaWvCplMAFBt9miGxgkA==,
            }
        cpu: [s390x]
        os: [linux]

    "@rollup/rollup-linux-x64-gnu@4.30.1":
        resolution:
            {
                integrity: sha512-UtgGb7QGgXDIO+tqqJ5oZRGHsDLO8SlpE4MhqpY9Llpzi5rJMvrK6ZGhsRCST2abZdBqIBeXW6WPD5fGK5SDwg==,
            }
        cpu: [x64]
        os: [linux]

    "@rollup/rollup-linux-x64-musl@4.30.1":
        resolution:
            {
                integrity: sha512-V9U8Ey2UqmQsBT+xTOeMzPzwDzyXmnAoO4edZhL7INkwQcaW1Ckv3WJX3qrrp/VHaDkEWIBWhRwP47r8cdrOow==,
            }
        cpu: [x64]
        os: [linux]

    "@rollup/rollup-win32-arm64-msvc@4.30.1":
        resolution:
            {
                integrity: sha512-WabtHWiPaFF47W3PkHnjbmWawnX/aE57K47ZDT1BXTS5GgrBUEpvOzq0FI0V/UYzQJgdb8XlhVNH8/fwV8xDjw==,
            }
        cpu: [arm64]
        os: [win32]

    "@rollup/rollup-win32-ia32-msvc@4.30.1":
        resolution:
            {
                integrity: sha512-pxHAU+Zv39hLUTdQQHUVHf4P+0C47y/ZloorHpzs2SXMRqeAWmGghzAhfOlzFHHwjvgokdFAhC4V+6kC1lRRfw==,
            }
        cpu: [ia32]
        os: [win32]

    "@rollup/rollup-win32-x64-msvc@4.30.1":
        resolution:
            {
                integrity: sha512-D6qjsXGcvhTjv0kI4fU8tUuBDF/Ueee4SVX79VfNDXZa64TfCW1Slkb6Z7O1p7vflqZjcmOVdZlqf8gvJxc6og==,
            }
        cpu: [x64]
        os: [win32]

    "@rtsao/scc@1.1.0":
        resolution:
            {
                integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==,
            }

    "@swc/core-darwin-arm64@1.10.6":
        resolution:
            {
                integrity: sha512-USbMvT8Rw5PvIfF6HyTm+yW84J9c45emzmHBDIWY76vZHkFsS5MepNi+JLQyBzBBgE7ScwBRBNhRx6VNhkSoww==,
            }
        engines: { node: ">=10" }
        cpu: [arm64]
        os: [darwin]

    "@swc/core-darwin-x64@1.10.6":
        resolution:
            {
                integrity: sha512-7t2IozcZN4r1p27ei+Kb8IjN4aLoBDn107fPi+aPLcVp2uFgJEUzhCDuZXBNW2057Mx1OHcjzrkaleRpECz3Xg==,
            }
        engines: { node: ">=10" }
        cpu: [x64]
        os: [darwin]

    "@swc/core-linux-arm-gnueabihf@1.10.6":
        resolution:
            {
                integrity: sha512-CPgWT+D0bDp/qhXsLkIJ54LmKU1/zvyGaf/yz8A4iR+YoF6R5CSXENXhNJY8cIrb6+uNWJZzHJ+gefB5V51bpA==,
            }
        engines: { node: ">=10" }
        cpu: [arm]
        os: [linux]

    "@swc/core-linux-arm64-gnu@1.10.6":
        resolution:
            {
                integrity: sha512-5qZ6hVnqO/ShETXdGSzvdGUVx372qydlj1YWSYiaxQzTAepEBc8TC1NVUgYtOHOKVRkky1d7p6GQ9lymsd4bHw==,
            }
        engines: { node: ">=10" }
        cpu: [arm64]
        os: [linux]

    "@swc/core-linux-arm64-musl@1.10.6":
        resolution:
            {
                integrity: sha512-hB2xZFmXCKf2iJF5y2z01PSuLqEoUP3jIX/XlIHN+/AIP7PkSKsValE63LnjlnWPnSEI0IxUyRE3T3FzWE/fQQ==,
            }
        engines: { node: ">=10" }
        cpu: [arm64]
        os: [linux]

    "@swc/core-linux-x64-gnu@1.10.6":
        resolution:
            {
                integrity: sha512-PRGPp0I22+oJ8RMGg8M4hXYxEffH3ayu0WoSDPOjfol1F51Wj1tfTWN4wVa2RibzJjkBwMOT0KGLGb/hSEDDXQ==,
            }
        engines: { node: ">=10" }
        cpu: [x64]
        os: [linux]

    "@swc/core-linux-x64-musl@1.10.6":
        resolution:
            {
                integrity: sha512-SoNBxlA86lnoV9vIz/TCyakLkdRhFSHx6tFMKNH8wAhz1kKYbZfDmpYoIzeQqdTh0tpx8e/Zu1zdK4smovsZqQ==,
            }
        engines: { node: ">=10" }
        cpu: [x64]
        os: [linux]

    "@swc/core-win32-arm64-msvc@1.10.6":
        resolution:
            {
                integrity: sha512-6L5Y2E+FVvM+BtoA+mJFjf/SjpFr73w2kHBxINxwH8/PkjAjkePDr5m0ibQhPXV61bTwX49+1otzTY85EsUW9Q==,
            }
        engines: { node: ">=10" }
        cpu: [arm64]
        os: [win32]

    "@swc/core-win32-ia32-msvc@1.10.6":
        resolution:
            {
                integrity: sha512-kxK3tW8DJwEkAkwy0vhwoBAShRebH1QTe0mvH9tlBQ21rToVZQn+GCV/I44dind80hYPw0Tw2JKFVfoEJyBszg==,
            }
        engines: { node: ">=10" }
        cpu: [ia32]
        os: [win32]

    "@swc/core-win32-x64-msvc@1.10.6":
        resolution:
            {
                integrity: sha512-4pJka/+t8XcHee12G/R5VWcilkp5poT2EJhrybpuREkpQ7iC/4WOlOVrohbWQ4AhDQmojYQI/iS+gdF2JFLzTQ==,
            }
        engines: { node: ">=10" }
        cpu: [x64]
        os: [win32]

    "@swc/core@1.10.6":
        resolution:
            {
                integrity: sha512-zgXXsI6SAVwr6XsXyMnqlyLoa1lT+r09bAWI1xT3679ejWqI1Vnl14eJG0GjWYXCEMKHCNytfMq3OOQ62C39QQ==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            "@swc/helpers": "*"
        peerDependenciesMeta:
            "@swc/helpers":
                optional: true

    "@swc/counter@0.1.3":
        resolution:
            {
                integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==,
            }

    "@swc/types@0.1.17":
        resolution:
            {
                integrity: sha512-V5gRru+aD8YVyCOMAjMpWR1Ui577DD5KSJsHP8RAxopAH22jFz6GZd/qxqjO6MJHQhcsjvjOFXyDhyLQUnMveQ==,
            }

    "@tanstack/query-core@5.62.16":
        resolution:
            {
                integrity: sha512-9Sgft7Qavcd+sN0V25xVyo0nfmcZXBuODy3FVG7BMWTg1HMLm8wwG5tNlLlmSic1u7l1v786oavn+STiFaPH2g==,
            }

    "@tanstack/react-query@5.62.16":
        resolution:
            {
                integrity: sha512-XJIZNj65d2IdvU8VBESmrPakfIm6FSdHDzrS1dPrAwmq3ZX+9riMh/ZfbNQHAWnhrgmq7KoXpgZSRyXnqMYT9A==,
            }
        peerDependencies:
            react: ^18 || ^19

    "@types/acorn@4.0.6":
        resolution:
            {
                integrity: sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==,
            }

    "@types/cookie@0.6.0":
        resolution:
            {
                integrity: sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==,
            }

    "@types/debug@4.1.12":
        resolution:
            {
                integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==,
            }

    "@types/estree-jsx@1.0.5":
        resolution:
            {
                integrity: sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==,
            }

    "@types/estree@1.0.6":
        resolution:
            {
                integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==,
            }

    "@types/hast@2.3.10":
        resolution:
            {
                integrity: sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==,
            }

    "@types/json-schema@7.0.15":
        resolution:
            {
                integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==,
            }

    "@types/json5@0.0.29":
        resolution:
            {
                integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==,
            }

    "@types/mdast@3.0.15":
        resolution:
            {
                integrity: sha512-LnwD+mUEfxWMa1QpDraczIn6k0Ee3SMicuYSSzS6ZYl2gKS09EClnJYGd8Du6rfc5r/GZEk5o1mRb8TaTj03sQ==,
            }

    "@types/mdx@2.0.13":
        resolution:
            {
                integrity: sha512-+OWZQfAYyio6YkJb3HLxDrvnx6SWWDbC0zVPfBRzUk0/nqoDyf6dNxQi3eArPe8rJ473nobTMQ/8Zk+LxJ+Yuw==,
            }

    "@types/ms@0.7.34":
        resolution:
            {
                integrity: sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g==,
            }

    "@types/node@22.10.5":
        resolution:
            {
                integrity: sha512-F8Q+SeGimwOo86fiovQh8qiXfFEh2/ocYv7tU5pJ3EXMSSxk1Joj5wefpFK2fHTf/N6HKGSxIDBT9f3gCxXPkQ==,
            }

    "@types/prop-types@15.7.14":
        resolution:
            {
                integrity: sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==,
            }

    "@types/react-dom@18.3.5":
        resolution:
            {
                integrity: sha512-P4t6saawp+b/dFrUr2cvkVsfvPguwsxtH6dNIYRllMsefqFzkZk5UIjzyDOv5g1dXIPdG4Sp1yCR4Z6RCUsG/Q==,
            }
        peerDependencies:
            "@types/react": ^18.0.0

    "@types/react@18.3.18":
        resolution:
            {
                integrity: sha512-t4yC+vtgnkYjNSKlFx1jkAhH8LgTo2N/7Qvi83kdEaUtMDiwpbLAktKDaAMlRcJ5eSxZkH74eEGt1ky31d7kfQ==,
            }

    "@types/semver@7.5.8":
        resolution:
            {
                integrity: sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==,
            }

    "@types/unist@2.0.11":
        resolution:
            {
                integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==,
            }

    "@typescript-eslint/eslint-plugin@6.21.0":
        resolution:
            {
                integrity: sha512-oy9+hTPCUFpngkEZUSzbf9MxI65wbKFoQYsgPdILTfbUldp5ovUuphZVe4i30emU9M/kP+T64Di0mxl7dSw3MA==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }
        peerDependencies:
            "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
            eslint: ^7.0.0 || ^8.0.0
            typescript: "*"
        peerDependenciesMeta:
            typescript:
                optional: true

    "@typescript-eslint/eslint-plugin@8.19.1":
        resolution:
            {
                integrity: sha512-tJzcVyvvb9h/PB96g30MpxACd9IrunT7GF9wfA9/0TJ1LxGOJx1TdPzSbBBnNED7K9Ka8ybJsnEpiXPktolTLg==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
            eslint: ^8.57.0 || ^9.0.0
            typescript: ">=4.8.4 <5.8.0"

    "@typescript-eslint/parser@6.21.0":
        resolution:
            {
                integrity: sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }
        peerDependencies:
            eslint: ^7.0.0 || ^8.0.0
            typescript: "*"
        peerDependenciesMeta:
            typescript:
                optional: true

    "@typescript-eslint/parser@8.19.1":
        resolution:
            {
                integrity: sha512-67gbfv8rAwawjYx3fYArwldTQKoYfezNUT4D5ioWetr/xCrxXxvleo3uuiFuKfejipvq+og7mjz3b0G2bVyUCw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: ">=4.8.4 <5.8.0"

    "@typescript-eslint/scope-manager@6.21.0":
        resolution:
            {
                integrity: sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }

    "@typescript-eslint/scope-manager@8.19.1":
        resolution:
            {
                integrity: sha512-60L9KIuN/xgmsINzonOcMDSB8p82h95hoBfSBtXuO4jlR1R9L1xSkmVZKgCPVfavDlXihh4ARNjXhh1gGnLC7Q==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@typescript-eslint/type-utils@6.21.0":
        resolution:
            {
                integrity: sha512-rZQI7wHfao8qMX3Rd3xqeYSMCL3SoiSQLBATSiVKARdFGCYSRvmViieZjqc58jKgs8Y8i9YvVVhRbHSTA4VBag==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }
        peerDependencies:
            eslint: ^7.0.0 || ^8.0.0
            typescript: "*"
        peerDependenciesMeta:
            typescript:
                optional: true

    "@typescript-eslint/type-utils@8.19.1":
        resolution:
            {
                integrity: sha512-Rp7k9lhDKBMRJB/nM9Ksp1zs4796wVNyihG9/TU9R6KCJDNkQbc2EOKjrBtLYh3396ZdpXLtr/MkaSEmNMtykw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: ">=4.8.4 <5.8.0"

    "@typescript-eslint/types@6.21.0":
        resolution:
            {
                integrity: sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }

    "@typescript-eslint/types@8.19.1":
        resolution:
            {
                integrity: sha512-JBVHMLj7B1K1v1051ZaMMgLW4Q/jre5qGK0Ew6UgXz1Rqh+/xPzV1aW581OM00X6iOfyr1be+QyW8LOUf19BbA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@typescript-eslint/typescript-estree@6.21.0":
        resolution:
            {
                integrity: sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }
        peerDependencies:
            typescript: "*"
        peerDependenciesMeta:
            typescript:
                optional: true

    "@typescript-eslint/typescript-estree@8.19.1":
        resolution:
            {
                integrity: sha512-jk/TZwSMJlxlNnqhy0Eod1PNEvCkpY6MXOXE/WLlblZ6ibb32i2We4uByoKPv1d0OD2xebDv4hbs3fm11SMw8Q==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            typescript: ">=4.8.4 <5.8.0"

    "@typescript-eslint/utils@6.21.0":
        resolution:
            {
                integrity: sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }
        peerDependencies:
            eslint: ^7.0.0 || ^8.0.0

    "@typescript-eslint/utils@8.19.1":
        resolution:
            {
                integrity: sha512-IxG5gLO0Ne+KaUc8iW1A+XuKLd63o4wlbI1Zp692n1xojCl/THvgIKXJXBZixTh5dd5+yTJ/VXH7GJaaw21qXA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: ">=4.8.4 <5.8.0"

    "@typescript-eslint/visitor-keys@6.21.0":
        resolution:
            {
                integrity: sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==,
            }
        engines: { node: ^16.0.0 || >=18.0.0 }

    "@typescript-eslint/visitor-keys@8.19.1":
        resolution:
            {
                integrity: sha512-fzmjU8CHK853V/avYZAvuVut3ZTfwN5YtMaoi+X9Y9MA9keaWNHC3zEQ9zvyX/7Hj+5JkNyK1l7TOR2hevHB6Q==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    "@vanilla-extract/babel-plugin-debug-ids@1.2.0":
        resolution:
            {
                integrity: sha512-z5nx2QBnOhvmlmBKeRX5sPVLz437wV30u+GJL+Hzj1rGiJYVNvgIIlzUpRNjVQ0MgAgiQIqIUbqPnmMc6HmDlQ==,
            }

    "@vanilla-extract/css@1.17.0":
        resolution:
            {
                integrity: sha512-W6FqVFDD+C71ZlKsuj0MxOXSvHb1tvQ9h/+79aYfi097wLsALrnnBzd0by8C///iurrpQ3S+SH74lXd7Lr9MvA==,
            }

    "@vanilla-extract/integration@6.5.0":
        resolution:
            {
                integrity: sha512-E2YcfO8vA+vs+ua+gpvy1HRqvgWbI+MTlUpxA8FvatOvybuNcWAY0CKwQ/Gpj7rswYKtC6C7+xw33emM6/ImdQ==,
            }

    "@vanilla-extract/private@1.0.6":
        resolution:
            {
                integrity: sha512-ytsG/JLweEjw7DBuZ/0JCN4WAQgM9erfSTdS1NQY778hFQSZ6cfCDEZZ0sgVm4k54uNz6ImKB33AYvSR//fjxw==,
            }

    "@vitejs/plugin-react-swc@3.7.2":
        resolution:
            {
                integrity: sha512-y0byko2b2tSVVf5Gpng1eEhX1OvPC7x8yns1Fx8jDzlJp4LS6CMkCPfLw47cjyoMrshQDoQw4qcgjsU9VvlCew==,
            }
        peerDependencies:
            vite: ^4 || ^5 || ^6

    "@web3-storage/multipart-parser@1.0.0":
        resolution:
            {
                integrity: sha512-BEO6al7BYqcnfX15W2cnGR+Q566ACXAT9UQykORCWW80lmkpWsnEob6zJS1ZVBKsSJC8+7vJkHwlp+lXG1UCdw==,
            }

    "@zxing/text-encoding@0.9.0":
        resolution:
            {
                integrity: sha512-U/4aVJ2mxI0aDNI8Uq0wEhMgY+u4CNtEb0om3+y3+niDAsoTCOB33UF0sxpzqzdqXLqmvc+vZyAt4O8pPdfkwA==,
            }

    abort-controller@3.0.0:
        resolution:
            {
                integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==,
            }
        engines: { node: ">=6.5" }

    accepts@1.3.8:
        resolution:
            {
                integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==,
            }
        engines: { node: ">= 0.6" }

    acorn-jsx@5.3.2:
        resolution:
            {
                integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==,
            }
        peerDependencies:
            acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

    acorn@8.14.0:
        resolution:
            {
                integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==,
            }
        engines: { node: ">=0.4.0" }
        hasBin: true

    aggregate-error@3.1.0:
        resolution:
            {
                integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==,
            }
        engines: { node: ">=8" }

    ajv@6.12.6:
        resolution:
            {
                integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==,
            }

    ansi-regex@5.0.1:
        resolution:
            {
                integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
            }
        engines: { node: ">=8" }

    ansi-regex@6.1.0:
        resolution:
            {
                integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==,
            }
        engines: { node: ">=12" }

    ansi-styles@4.3.0:
        resolution:
            {
                integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
            }
        engines: { node: ">=8" }

    ansi-styles@6.2.1:
        resolution:
            {
                integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
            }
        engines: { node: ">=12" }

    any-promise@1.3.0:
        resolution:
            {
                integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
            }

    anymatch@3.1.3:
        resolution:
            {
                integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
            }
        engines: { node: ">= 8" }

    arg@5.0.2:
        resolution:
            {
                integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==,
            }

    argparse@2.0.1:
        resolution:
            {
                integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
            }

    aria-hidden@1.2.4:
        resolution:
            {
                integrity: sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==,
            }
        engines: { node: ">=10" }

    aria-query@5.3.2:
        resolution:
            {
                integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==,
            }
        engines: { node: ">= 0.4" }

    array-buffer-byte-length@1.0.2:
        resolution:
            {
                integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==,
            }
        engines: { node: ">= 0.4" }

    array-flatten@1.1.1:
        resolution:
            {
                integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==,
            }

    array-includes@3.1.8:
        resolution:
            {
                integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==,
            }
        engines: { node: ">= 0.4" }

    array-union@2.1.0:
        resolution:
            {
                integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==,
            }
        engines: { node: ">=8" }

    array.prototype.findlast@1.2.5:
        resolution:
            {
                integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==,
            }
        engines: { node: ">= 0.4" }

    array.prototype.findlastindex@1.2.5:
        resolution:
            {
                integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==,
            }
        engines: { node: ">= 0.4" }

    array.prototype.flat@1.3.3:
        resolution:
            {
                integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==,
            }
        engines: { node: ">= 0.4" }

    array.prototype.flatmap@1.3.3:
        resolution:
            {
                integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==,
            }
        engines: { node: ">= 0.4" }

    array.prototype.tosorted@1.1.4:
        resolution:
            {
                integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==,
            }
        engines: { node: ">= 0.4" }

    arraybuffer.prototype.slice@1.0.4:
        resolution:
            {
                integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==,
            }
        engines: { node: ">= 0.4" }

    ast-types-flow@0.0.8:
        resolution:
            {
                integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==,
            }

    astring@1.9.0:
        resolution:
            {
                integrity: sha512-LElXdjswlqjWrPpJFg1Fx4wpkOCxj1TDHlSV4PlaRxHGWko024xICaa97ZkMfs6DRKlCguiAI+rbXv5GWwXIkg==,
            }
        hasBin: true

    autoprefixer@10.4.20:
        resolution:
            {
                integrity: sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==,
            }
        engines: { node: ^10 || ^12 || >=14 }
        hasBin: true
        peerDependencies:
            postcss: ^8.1.0

    available-typed-arrays@1.0.7:
        resolution:
            {
                integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==,
            }
        engines: { node: ">= 0.4" }

    axe-core@4.10.2:
        resolution:
            {
                integrity: sha512-RE3mdQ7P3FRSe7eqCWoeQ/Z9QXrtniSjp1wUjt5nRC3WIpz5rSCve6o3fsZ2aCpJtrZjSZgjwXAoTO5k4tEI0w==,
            }
        engines: { node: ">=4" }

    axobject-query@4.1.0:
        resolution:
            {
                integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==,
            }
        engines: { node: ">= 0.4" }

    bail@2.0.2:
        resolution:
            {
                integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==,
            }

    balanced-match@1.0.2:
        resolution:
            {
                integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
            }

    base64-js@1.5.1:
        resolution:
            {
                integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==,
            }

    basic-auth@2.0.1:
        resolution:
            {
                integrity: sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==,
            }
        engines: { node: ">= 0.8" }

    binary-extensions@2.3.0:
        resolution:
            {
                integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
            }
        engines: { node: ">=8" }

    bl@4.1.0:
        resolution:
            {
                integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==,
            }

    body-parser@1.20.3:
        resolution:
            {
                integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==,
            }
        engines: { node: ">= 0.8", npm: 1.2.8000 || >= 1.4.16 }

    brace-expansion@1.1.11:
        resolution:
            {
                integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
            }

    brace-expansion@2.0.1:
        resolution:
            {
                integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
            }

    braces@3.0.3:
        resolution:
            {
                integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==,
            }
        engines: { node: ">=8" }

    browserify-zlib@0.1.4:
        resolution:
            {
                integrity: sha512-19OEpq7vWgsH6WkvkBJQDFvJS1uPcbFOQ4v9CU839dO+ZZXUZO6XpE6hNCqvlIIj+4fZvRiJ6DsAQ382GwiyTQ==,
            }

    browserslist@4.24.3:
        resolution:
            {
                integrity: sha512-1CPmv8iobE2fyRMV97dAcMVegvvWKxmq94hkLiAkUGwKVTyDLw33K+ZxiFrREKmmps4rIw6grcCFCnTMSZ/YiA==,
            }
        engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
        hasBin: true

    buffer-from@1.1.2:
        resolution:
            {
                integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==,
            }

    buffer@5.7.1:
        resolution:
            {
                integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==,
            }

    bytes@3.1.2:
        resolution:
            {
                integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==,
            }
        engines: { node: ">= 0.8" }

    cac@6.7.14:
        resolution:
            {
                integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==,
            }
        engines: { node: ">=8" }

    cacache@17.1.4:
        resolution:
            {
                integrity: sha512-/aJwG2l3ZMJ1xNAnqbMpA40of9dj/pIH3QfiuQSqjfPJF747VR0J/bHn+/KdNnHKc6XQcWt/AfRSBft82W1d2A==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    call-bind-apply-helpers@1.0.1:
        resolution:
            {
                integrity: sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==,
            }
        engines: { node: ">= 0.4" }

    call-bind@1.0.8:
        resolution:
            {
                integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==,
            }
        engines: { node: ">= 0.4" }

    call-bound@1.0.3:
        resolution:
            {
                integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==,
            }
        engines: { node: ">= 0.4" }

    callsites@3.1.0:
        resolution:
            {
                integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==,
            }
        engines: { node: ">=6" }

    camelcase-css@2.0.1:
        resolution:
            {
                integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==,
            }
        engines: { node: ">= 6" }

    caniuse-lite@1.0.30001690:
        resolution:
            {
                integrity: sha512-5ExiE3qQN6oF8Clf8ifIDcMRCRE/dMGcETG/XGMD8/XiXm6HXQgQTh1yZYLXXpSOsEUlJm1Xr7kGULZTuGtP/w==,
            }

    ccount@2.0.1:
        resolution:
            {
                integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==,
            }

    chalk@4.1.2:
        resolution:
            {
                integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
            }
        engines: { node: ">=10" }

    character-entities-html4@2.1.0:
        resolution:
            {
                integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==,
            }

    character-entities-legacy@3.0.0:
        resolution:
            {
                integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==,
            }

    character-entities@2.0.2:
        resolution:
            {
                integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==,
            }

    character-reference-invalid@2.0.1:
        resolution:
            {
                integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==,
            }

    chokidar@3.6.0:
        resolution:
            {
                integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
            }
        engines: { node: ">= 8.10.0" }

    chownr@1.1.4:
        resolution:
            {
                integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==,
            }

    chownr@2.0.0:
        resolution:
            {
                integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==,
            }
        engines: { node: ">=10" }

    class-variance-authority@0.7.1:
        resolution:
            {
                integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==,
            }

    classnames@2.5.1:
        resolution:
            {
                integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==,
            }

    clean-stack@2.2.0:
        resolution:
            {
                integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==,
            }
        engines: { node: ">=6" }

    cli-cursor@3.1.0:
        resolution:
            {
                integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==,
            }
        engines: { node: ">=8" }

    cli-spinners@2.9.2:
        resolution:
            {
                integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==,
            }
        engines: { node: ">=6" }

    clone@1.0.4:
        resolution:
            {
                integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==,
            }
        engines: { node: ">=0.8" }

    clsx@2.1.1:
        resolution:
            {
                integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==,
            }
        engines: { node: ">=6" }

    color-convert@2.0.1:
        resolution:
            {
                integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
            }
        engines: { node: ">=7.0.0" }

    color-name@1.1.4:
        resolution:
            {
                integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
            }

    comma-separated-tokens@2.0.3:
        resolution:
            {
                integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==,
            }

    commander@4.1.1:
        resolution:
            {
                integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
            }
        engines: { node: ">= 6" }

    compressible@2.0.18:
        resolution:
            {
                integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==,
            }
        engines: { node: ">= 0.6" }

    compression@1.7.5:
        resolution:
            {
                integrity: sha512-bQJ0YRck5ak3LgtnpKkiabX5pNF7tMUh1BSy2ZBOTh0Dim0BUu6aPPwByIns6/A5Prh8PufSPerMDUklpzes2Q==,
            }
        engines: { node: ">= 0.8.0" }

    concat-map@0.0.1:
        resolution:
            {
                integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
            }

    confbox@0.1.8:
        resolution:
            {
                integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==,
            }

    content-disposition@0.5.4:
        resolution:
            {
                integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==,
            }
        engines: { node: ">= 0.6" }

    content-type@1.0.5:
        resolution:
            {
                integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==,
            }
        engines: { node: ">= 0.6" }

    convert-source-map@2.0.0:
        resolution:
            {
                integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==,
            }

    cookie-signature@1.0.6:
        resolution:
            {
                integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==,
            }

    cookie-signature@1.2.2:
        resolution:
            {
                integrity: sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==,
            }
        engines: { node: ">=6.6.0" }

    cookie@0.6.0:
        resolution:
            {
                integrity: sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==,
            }
        engines: { node: ">= 0.6" }

    cookie@0.7.1:
        resolution:
            {
                integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==,
            }
        engines: { node: ">= 0.6" }

    core-util-is@1.0.3:
        resolution:
            {
                integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==,
            }

    cross-spawn@7.0.6:
        resolution:
            {
                integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==,
            }
        engines: { node: ">= 8" }

    css-what@6.1.0:
        resolution:
            {
                integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==,
            }
        engines: { node: ">= 6" }

    cssesc@3.0.0:
        resolution:
            {
                integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==,
            }
        engines: { node: ">=4" }
        hasBin: true

    csstype@3.1.3:
        resolution:
            {
                integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==,
            }

    damerau-levenshtein@1.0.8:
        resolution:
            {
                integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==,
            }

    data-uri-to-buffer@3.0.1:
        resolution:
            {
                integrity: sha512-WboRycPNsVw3B3TL559F7kuBUM4d8CgMEvk6xEJlOp7OBPjt6G7z8WMWlD2rOFZLk6OYfFIUGsCOWzcQH9K2og==,
            }
        engines: { node: ">= 6" }

    data-view-buffer@1.0.2:
        resolution:
            {
                integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==,
            }
        engines: { node: ">= 0.4" }

    data-view-byte-length@1.0.2:
        resolution:
            {
                integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==,
            }
        engines: { node: ">= 0.4" }

    data-view-byte-offset@1.0.1:
        resolution:
            {
                integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==,
            }
        engines: { node: ">= 0.4" }

    dayjs@1.11.13:
        resolution:
            {
                integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==,
            }

    debug@2.6.9:
        resolution:
            {
                integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==,
            }
        peerDependencies:
            supports-color: "*"
        peerDependenciesMeta:
            supports-color:
                optional: true

    debug@3.2.7:
        resolution:
            {
                integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==,
            }
        peerDependencies:
            supports-color: "*"
        peerDependenciesMeta:
            supports-color:
                optional: true

    debug@4.4.0:
        resolution:
            {
                integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==,
            }
        engines: { node: ">=6.0" }
        peerDependencies:
            supports-color: "*"
        peerDependenciesMeta:
            supports-color:
                optional: true

    decode-named-character-reference@1.0.2:
        resolution:
            {
                integrity: sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==,
            }

    dedent@1.5.3:
        resolution:
            {
                integrity: sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==,
            }
        peerDependencies:
            babel-plugin-macros: ^3.1.0
        peerDependenciesMeta:
            babel-plugin-macros:
                optional: true

    deep-is@0.1.4:
        resolution:
            {
                integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==,
            }

    deep-object-diff@1.1.9:
        resolution:
            {
                integrity: sha512-Rn+RuwkmkDwCi2/oXOFS9Gsr5lJZu/yTGpK7wAaAIE75CC+LCGEZHpY6VQJa/RoJcrmaA/docWJZvYohlNkWPA==,
            }

    deepmerge@4.3.1:
        resolution:
            {
                integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==,
            }
        engines: { node: ">=0.10.0" }

    defaults@1.0.4:
        resolution:
            {
                integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==,
            }

    define-data-property@1.1.4:
        resolution:
            {
                integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==,
            }
        engines: { node: ">= 0.4" }

    define-properties@1.2.1:
        resolution:
            {
                integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==,
            }
        engines: { node: ">= 0.4" }

    depd@2.0.0:
        resolution:
            {
                integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==,
            }
        engines: { node: ">= 0.8" }

    dequal@2.0.3:
        resolution:
            {
                integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==,
            }
        engines: { node: ">=6" }

    destroy@1.2.0:
        resolution:
            {
                integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==,
            }
        engines: { node: ">= 0.8", npm: 1.2.8000 || >= 1.4.16 }

    detect-node-es@1.1.0:
        resolution:
            {
                integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==,
            }

    didyoumean@1.2.2:
        resolution:
            {
                integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==,
            }

    diff@5.2.0:
        resolution:
            {
                integrity: sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==,
            }
        engines: { node: ">=0.3.1" }

    dir-glob@3.0.1:
        resolution:
            {
                integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==,
            }
        engines: { node: ">=8" }

    dlv@1.1.3:
        resolution:
            {
                integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==,
            }

    doctrine@2.1.0:
        resolution:
            {
                integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==,
            }
        engines: { node: ">=0.10.0" }

    dotenv@16.4.7:
        resolution:
            {
                integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==,
            }
        engines: { node: ">=12" }

    dunder-proto@1.0.1:
        resolution:
            {
                integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==,
            }
        engines: { node: ">= 0.4" }

    duplexify@3.7.1:
        resolution:
            {
                integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==,
            }

    eastasianwidth@0.2.0:
        resolution:
            {
                integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
            }

    ee-first@1.1.1:
        resolution:
            {
                integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==,
            }

    electron-to-chromium@1.5.78:
        resolution:
            {
                integrity: sha512-UmwIt7HRKN1rsJfddG5UG7rCTCTAKoS9JeOy/R0zSenAyaZ8SU3RuXlwcratxhdxGRNpk03iq8O7BA3W7ibLVw==,
            }

    emoji-regex@8.0.0:
        resolution:
            {
                integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
            }

    emoji-regex@9.2.2:
        resolution:
            {
                integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
            }

    encodeurl@1.0.2:
        resolution:
            {
                integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==,
            }
        engines: { node: ">= 0.8" }

    encodeurl@2.0.0:
        resolution:
            {
                integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==,
            }
        engines: { node: ">= 0.8" }

    end-of-stream@1.4.4:
        resolution:
            {
                integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==,
            }

    enhanced-resolve@5.18.0:
        resolution:
            {
                integrity: sha512-0/r0MySGYG8YqlayBZ6MuCfECmHFdJ5qyPh8s8wa5Hnm6SaFLSK1VYCbj+NKp090Nm1caZhD+QTnmxO7esYGyQ==,
            }
        engines: { node: ">=10.13.0" }

    err-code@2.0.3:
        resolution:
            {
                integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==,
            }

    es-abstract@1.23.9:
        resolution:
            {
                integrity: sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==,
            }
        engines: { node: ">= 0.4" }

    es-define-property@1.0.1:
        resolution:
            {
                integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==,
            }
        engines: { node: ">= 0.4" }

    es-errors@1.3.0:
        resolution:
            {
                integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
            }
        engines: { node: ">= 0.4" }

    es-iterator-helpers@1.2.1:
        resolution:
            {
                integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==,
            }
        engines: { node: ">= 0.4" }

    es-module-lexer@1.6.0:
        resolution:
            {
                integrity: sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ==,
            }

    es-object-atoms@1.0.0:
        resolution:
            {
                integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==,
            }
        engines: { node: ">= 0.4" }

    es-set-tostringtag@2.1.0:
        resolution:
            {
                integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==,
            }
        engines: { node: ">= 0.4" }

    es-shim-unscopables@1.0.2:
        resolution:
            {
                integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==,
            }

    es-to-primitive@1.3.0:
        resolution:
            {
                integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==,
            }
        engines: { node: ">= 0.4" }

    esbuild-plugins-node-modules-polyfill@1.6.8:
        resolution:
            {
                integrity: sha512-bRB4qbgUDWrdY1eMk123KiaCSW9VzQ+QLZrmU7D//cCFkmksPd9mUMpmWoFK/rxjIeTfTSOpKCoGoimlvI+AWw==,
            }
        engines: { node: ">=14.0.0" }
        peerDependencies:
            esbuild: ">=0.14.0 <=0.24.x"

    esbuild@0.17.6:
        resolution:
            {
                integrity: sha512-TKFRp9TxrJDdRWfSsSERKEovm6v30iHnrjlcGhLBOtReE28Yp1VSBRfO3GTaOFMoxsNerx4TjrhzSuma9ha83Q==,
            }
        engines: { node: ">=12" }
        hasBin: true

    esbuild@0.21.5:
        resolution:
            {
                integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==,
            }
        engines: { node: ">=12" }
        hasBin: true

    esbuild@0.24.2:
        resolution:
            {
                integrity: sha512-+9egpBW8I3CD5XPe0n6BfT5fxLzxrlDzqydF3aviG+9ni1lDC/OvMHcxqEFV0+LANZG5R1bFMWfUrjVsdwxJvA==,
            }
        engines: { node: ">=18" }
        hasBin: true

    escalade@3.2.0:
        resolution:
            {
                integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==,
            }
        engines: { node: ">=6" }

    escape-html@1.0.3:
        resolution:
            {
                integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==,
            }

    escape-string-regexp@4.0.0:
        resolution:
            {
                integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==,
            }
        engines: { node: ">=10" }

    eslint-import-resolver-node@0.3.9:
        resolution:
            {
                integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==,
            }

    eslint-import-resolver-typescript@3.7.0:
        resolution:
            {
                integrity: sha512-Vrwyi8HHxY97K5ebydMtffsWAn1SCR9eol49eCd5fJS4O1WV7PaAjbcjmbfJJSMz/t4Mal212Uz/fQZrOB8mow==,
            }
        engines: { node: ^14.18.0 || >=16.0.0 }
        peerDependencies:
            eslint: "*"
            eslint-plugin-import: "*"
            eslint-plugin-import-x: "*"
        peerDependenciesMeta:
            eslint-plugin-import:
                optional: true
            eslint-plugin-import-x:
                optional: true

    eslint-module-utils@2.12.0:
        resolution:
            {
                integrity: sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==,
            }
        engines: { node: ">=4" }
        peerDependencies:
            "@typescript-eslint/parser": "*"
            eslint: "*"
            eslint-import-resolver-node: "*"
            eslint-import-resolver-typescript: "*"
            eslint-import-resolver-webpack: "*"
        peerDependenciesMeta:
            "@typescript-eslint/parser":
                optional: true
            eslint:
                optional: true
            eslint-import-resolver-node:
                optional: true
            eslint-import-resolver-typescript:
                optional: true
            eslint-import-resolver-webpack:
                optional: true

    eslint-plugin-import@2.31.0:
        resolution:
            {
                integrity: sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==,
            }
        engines: { node: ">=4" }
        peerDependencies:
            "@typescript-eslint/parser": "*"
            eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
        peerDependenciesMeta:
            "@typescript-eslint/parser":
                optional: true

    eslint-plugin-jsx-a11y@6.10.2:
        resolution:
            {
                integrity: sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==,
            }
        engines: { node: ">=4.0" }
        peerDependencies:
            eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9

    eslint-plugin-react-hooks@5.1.0:
        resolution:
            {
                integrity: sha512-mpJRtPgHN2tNAvZ35AMfqeB3Xqeo273QxrHJsbBEPWODRM4r0yB6jfoROqKEYrOn27UtRPpcpHc2UqyBSuUNTw==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

    eslint-plugin-react-refresh@0.4.16:
        resolution:
            {
                integrity: sha512-slterMlxAhov/DZO8NScf6mEeMBBXodFUolijDvrtTxyezyLoTQaa73FyYus/VbTdftd8wBgBxPMRk3poleXNQ==,
            }
        peerDependencies:
            eslint: ">=8.40"

    eslint-plugin-react@7.37.3:
        resolution:
            {
                integrity: sha512-DomWuTQPFYZwF/7c9W2fkKkStqZmBd3uugfqBYLdkZ3Hii23WzZuOLUskGxB8qkSKqftxEeGL1TB2kMhrce0jA==,
            }
        engines: { node: ">=4" }
        peerDependencies:
            eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

    eslint-scope@8.2.0:
        resolution:
            {
                integrity: sha512-PHlWUfG6lvPc3yvP5A4PNyBL1W8fkDUccmI21JUu/+GKZBoH/W5u6usENXUrWFRsyoW5ACUjFGgAFQp5gUlb/A==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    eslint-visitor-keys@3.4.3:
        resolution:
            {
                integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==,
            }
        engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

    eslint-visitor-keys@4.2.0:
        resolution:
            {
                integrity: sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    eslint@9.17.0:
        resolution:
            {
                integrity: sha512-evtlNcpJg+cZLcnVKwsai8fExnqjGPicK7gnUtlNuzu+Fv9bI0aLpND5T44VLQtoMEnI57LoXO9XAkIXwohKrA==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        hasBin: true
        peerDependencies:
            jiti: "*"
        peerDependenciesMeta:
            jiti:
                optional: true

    espree@10.3.0:
        resolution:
            {
                integrity: sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

    esquery@1.6.0:
        resolution:
            {
                integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==,
            }
        engines: { node: ">=0.10" }

    esrecurse@4.3.0:
        resolution:
            {
                integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==,
            }
        engines: { node: ">=4.0" }

    estraverse@5.3.0:
        resolution:
            {
                integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==,
            }
        engines: { node: ">=4.0" }

    estree-util-attach-comments@2.1.1:
        resolution:
            {
                integrity: sha512-+5Ba/xGGS6mnwFbXIuQiDPTbuTxuMCooq3arVv7gPZtYpjp+VXH/NkHAP35OOefPhNG/UGqU3vt/LTABwcHX0w==,
            }

    estree-util-build-jsx@2.2.2:
        resolution:
            {
                integrity: sha512-m56vOXcOBuaF+Igpb9OPAy7f9w9OIkb5yhjsZuaPm7HoGi4oTOQi0h2+yZ+AtKklYFZ+rPC4n0wYCJCEU1ONqg==,
            }

    estree-util-is-identifier-name@1.1.0:
        resolution:
            {
                integrity: sha512-OVJZ3fGGt9By77Ix9NhaRbzfbDV/2rx9EP7YIDJTmsZSEc5kYn2vWcNccYyahJL2uAQZK2a5Or2i0wtIKTPoRQ==,
            }

    estree-util-is-identifier-name@2.1.0:
        resolution:
            {
                integrity: sha512-bEN9VHRyXAUOjkKVQVvArFym08BTWB0aJPppZZr0UNyAqWsLaVfAqP7hbaTJjzHifmB5ebnR8Wm7r7yGN/HonQ==,
            }

    estree-util-to-js@1.2.0:
        resolution:
            {
                integrity: sha512-IzU74r1PK5IMMGZXUVZbmiu4A1uhiPgW5hm1GjcOfr4ZzHaMPpLNJjR7HjXiIOzi25nZDrgFTobHTkV5Q6ITjA==,
            }

    estree-util-value-to-estree@1.3.0:
        resolution:
            {
                integrity: sha512-Y+ughcF9jSUJvncXwqRageavjrNPAI+1M/L3BI3PyLp1nmgYTGUXU6t5z1Y7OWuThoDdhPME07bQU+d5LxdJqw==,
            }
        engines: { node: ">=12.0.0" }

    estree-util-visit@1.2.1:
        resolution:
            {
                integrity: sha512-xbgqcrkIVbIG+lI/gzbvd9SGTJL4zqJKBFttUl5pP27KhAjtMKbX/mQXJ7qgyXpMgVy/zvpm0xoQQaGL8OloOw==,
            }

    estree-walker@3.0.3:
        resolution:
            {
                integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==,
            }

    esutils@2.0.3:
        resolution:
            {
                integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==,
            }
        engines: { node: ">=0.10.0" }

    etag@1.8.1:
        resolution:
            {
                integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==,
            }
        engines: { node: ">= 0.6" }

    eval@0.1.8:
        resolution:
            {
                integrity: sha512-EzV94NYKoO09GLXGjXj9JIlXijVck4ONSr5wiCWDvhsvj5jxSrzTmRU/9C1DyB6uToszLs8aifA6NQ7lEQdvFw==,
            }
        engines: { node: ">= 0.8" }

    event-target-shim@5.0.1:
        resolution:
            {
                integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==,
            }
        engines: { node: ">=6" }

    execa@5.1.1:
        resolution:
            {
                integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==,
            }
        engines: { node: ">=10" }

    exit-hook@2.2.1:
        resolution:
            {
                integrity: sha512-eNTPlAD67BmP31LDINZ3U7HSF8l57TxOY2PmBJ1shpCvpnxBF93mWCE8YHBnXs8qiUZJc9WDcWIeC3a2HIAMfw==,
            }
        engines: { node: ">=6" }

    express@4.21.2:
        resolution:
            {
                integrity: sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==,
            }
        engines: { node: ">= 0.10.0" }

    extend@3.0.2:
        resolution:
            {
                integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==,
            }

    fast-deep-equal@3.1.3:
        resolution:
            {
                integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
            }

    fast-glob@3.3.3:
        resolution:
            {
                integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==,
            }
        engines: { node: ">=8.6.0" }

    fast-json-stable-stringify@2.1.0:
        resolution:
            {
                integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==,
            }

    fast-levenshtein@2.0.6:
        resolution:
            {
                integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==,
            }

    fastq@1.18.0:
        resolution:
            {
                integrity: sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==,
            }

    fault@2.0.1:
        resolution:
            {
                integrity: sha512-WtySTkS4OKev5JtpHXnib4Gxiurzh5NCGvWrFaZ34m6JehfTUhKZvn9njTfw48t6JumVQOmrKqpmGcdwxnhqBQ==,
            }

    file-entry-cache@8.0.0:
        resolution:
            {
                integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==,
            }
        engines: { node: ">=16.0.0" }

    fill-range@7.1.1:
        resolution:
            {
                integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==,
            }
        engines: { node: ">=8" }

    finalhandler@1.3.1:
        resolution:
            {
                integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==,
            }
        engines: { node: ">= 0.8" }

    find-up@5.0.0:
        resolution:
            {
                integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==,
            }
        engines: { node: ">=10" }

    flat-cache@4.0.1:
        resolution:
            {
                integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==,
            }
        engines: { node: ">=16" }

    flatted@3.3.2:
        resolution:
            {
                integrity: sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==,
            }

    for-each@0.3.3:
        resolution:
            {
                integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==,
            }

    foreground-child@3.3.0:
        resolution:
            {
                integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==,
            }
        engines: { node: ">=14" }

    format@0.2.2:
        resolution:
            {
                integrity: sha512-wzsgA6WOq+09wrU1tsJ09udeR/YZRaeArL9e1wPbFg3GG2yDnC2ldKpxs4xunpFF9DgqCqOIra3bc1HWrJ37Ww==,
            }
        engines: { node: ">=0.4.x" }

    forwarded@0.2.0:
        resolution:
            {
                integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==,
            }
        engines: { node: ">= 0.6" }

    fraction.js@4.3.7:
        resolution:
            {
                integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==,
            }

    framer-motion@11.16.0:
        resolution:
            {
                integrity: sha512-oL2AWqLQuw0+CNEUa0sz3mWC/n3i147CckvpQn8bLRs30b+HxTxlRi0YR2FpHHhAbWV7DKjNdHU42KHLfBWh/g==,
            }
        peerDependencies:
            "@emotion/is-prop-valid": "*"
            react: ^18.0.0 || ^19.0.0
            react-dom: ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            "@emotion/is-prop-valid":
                optional: true
            react:
                optional: true
            react-dom:
                optional: true

    fresh@0.5.2:
        resolution:
            {
                integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==,
            }
        engines: { node: ">= 0.6" }

    fs-constants@1.0.0:
        resolution:
            {
                integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==,
            }

    fs-extra@10.1.0:
        resolution:
            {
                integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==,
            }
        engines: { node: ">=12" }

    fs-minipass@2.1.0:
        resolution:
            {
                integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==,
            }
        engines: { node: ">= 8" }

    fs-minipass@3.0.3:
        resolution:
            {
                integrity: sha512-XUBA9XClHbnJWSfBzjkm6RvPsyg3sryZt06BEQoXcF7EK/xpGaQYJgQKDJSUH5SGZ76Y7pFx1QBnXz09rU5Fbw==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    fsevents@2.3.3:
        resolution:
            {
                integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
            }
        engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
        os: [darwin]

    function-bind@1.1.2:
        resolution:
            {
                integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
            }

    function.prototype.name@1.1.8:
        resolution:
            {
                integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==,
            }
        engines: { node: ">= 0.4" }

    functions-have-names@1.2.3:
        resolution:
            {
                integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==,
            }

    generic-names@4.0.0:
        resolution:
            {
                integrity: sha512-ySFolZQfw9FoDb3ed9d80Cm9f0+r7qj+HJkWjeD9RBfpxEVTlVhol+gvaQB/78WbwYfbnNh8nWHHBSlg072y6A==,
            }

    gensync@1.0.0-beta.2:
        resolution:
            {
                integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==,
            }
        engines: { node: ">=6.9.0" }

    get-intrinsic@1.2.7:
        resolution:
            {
                integrity: sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==,
            }
        engines: { node: ">= 0.4" }

    get-nonce@1.0.1:
        resolution:
            {
                integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==,
            }
        engines: { node: ">=6" }

    get-port@5.1.1:
        resolution:
            {
                integrity: sha512-g/Q1aTSDOxFpchXC4i8ZWvxA1lnPqx/JHqcpIw0/LX9T8x/GBbi6YnlN5nhaKIFkT8oFsscUKgDJYxfwfS6QsQ==,
            }
        engines: { node: ">=8" }

    get-proto@1.0.1:
        resolution:
            {
                integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==,
            }
        engines: { node: ">= 0.4" }

    get-stream@6.0.1:
        resolution:
            {
                integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==,
            }
        engines: { node: ">=10" }

    get-symbol-description@1.1.0:
        resolution:
            {
                integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==,
            }
        engines: { node: ">= 0.4" }

    get-tsconfig@4.8.1:
        resolution:
            {
                integrity: sha512-k9PN+cFBmaLWtVz29SkUoqU5O0slLuHJXt/2P+tMVFT+phsSGXGkp9t3rQIqdz0e+06EHNGs3oM6ZX1s2zHxRg==,
            }

    glob-parent@5.1.2:
        resolution:
            {
                integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
            }
        engines: { node: ">= 6" }

    glob-parent@6.0.2:
        resolution:
            {
                integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==,
            }
        engines: { node: ">=10.13.0" }

    glob@10.4.5:
        resolution:
            {
                integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==,
            }
        hasBin: true

    globals@11.12.0:
        resolution:
            {
                integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==,
            }
        engines: { node: ">=4" }

    globals@14.0.0:
        resolution:
            {
                integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==,
            }
        engines: { node: ">=18" }

    globals@15.14.0:
        resolution:
            {
                integrity: sha512-OkToC372DtlQeje9/zHIo5CT8lRP/FUgEOKBEhU4e0abL7J7CD24fD9ohiLN5hagG/kWCYj4K5oaxxtj2Z0Dig==,
            }
        engines: { node: ">=18" }

    globalthis@1.0.4:
        resolution:
            {
                integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==,
            }
        engines: { node: ">= 0.4" }

    globby@11.1.0:
        resolution:
            {
                integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==,
            }
        engines: { node: ">=10" }

    globrex@0.1.2:
        resolution:
            {
                integrity: sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==,
            }

    gopd@1.2.0:
        resolution:
            {
                integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==,
            }
        engines: { node: ">= 0.4" }

    graceful-fs@4.2.11:
        resolution:
            {
                integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
            }

    graphemer@1.4.0:
        resolution:
            {
                integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==,
            }

    gunzip-maybe@1.4.2:
        resolution:
            {
                integrity: sha512-4haO1M4mLO91PW57BMsDFf75UmwoRX0GkdD+Faw+Lr+r/OZrOCS0pIBwOL1xCKQqnQzbNFGgK2V2CpBUPeFNTw==,
            }
        hasBin: true

    has-bigints@1.1.0:
        resolution:
            {
                integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==,
            }
        engines: { node: ">= 0.4" }

    has-flag@4.0.0:
        resolution:
            {
                integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
            }
        engines: { node: ">=8" }

    has-property-descriptors@1.0.2:
        resolution:
            {
                integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==,
            }

    has-proto@1.2.0:
        resolution:
            {
                integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==,
            }
        engines: { node: ">= 0.4" }

    has-symbols@1.1.0:
        resolution:
            {
                integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==,
            }
        engines: { node: ">= 0.4" }

    has-tostringtag@1.0.2:
        resolution:
            {
                integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
            }
        engines: { node: ">= 0.4" }

    hasown@2.0.2:
        resolution:
            {
                integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
            }
        engines: { node: ">= 0.4" }

    hast-util-to-estree@2.3.3:
        resolution:
            {
                integrity: sha512-ihhPIUPxN0v0w6M5+IiAZZrn0LH2uZomeWwhn7uP7avZC6TE7lIiEh2yBMPr5+zi1aUCXq6VoYRgs2Bw9xmycQ==,
            }

    hast-util-whitespace@2.0.1:
        resolution:
            {
                integrity: sha512-nAxA0v8+vXSBDt3AnRUNjyRIQ0rD+ntpbAp4LnPkumc5M9yUbSMa4XDU9Q6etY4f1Wp4bNgvc1yjiZtsTTrSng==,
            }

    hosted-git-info@6.1.3:
        resolution:
            {
                integrity: sha512-HVJyzUrLIL1c0QmviVh5E8VGyUS7xCFPS6yydaVd1UegW+ibV/CohqTH9MkOLDp5o+rb82DMo77PTuc9F/8GKw==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    howler@2.2.4:
        resolution:
            {
                integrity: sha512-iARIBPgcQrwtEr+tALF+rapJ8qSc+Set2GJQl7xT1MQzWaVkFebdJhR3alVlSiUf5U7nAANKuj3aWpwerocD5w==,
            }

    http-errors@2.0.0:
        resolution:
            {
                integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==,
            }
        engines: { node: ">= 0.8" }

    human-signals@2.1.0:
        resolution:
            {
                integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==,
            }
        engines: { node: ">=10.17.0" }

    iconv-lite@0.4.24:
        resolution:
            {
                integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==,
            }
        engines: { node: ">=0.10.0" }

    icss-utils@5.1.0:
        resolution:
            {
                integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==,
            }
        engines: { node: ^10 || ^12 || >= 14 }
        peerDependencies:
            postcss: ^8.1.0

    ieee754@1.2.1:
        resolution:
            {
                integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==,
            }

    ignore@5.3.2:
        resolution:
            {
                integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==,
            }
        engines: { node: ">= 4" }

    import-fresh@3.3.0:
        resolution:
            {
                integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==,
            }
        engines: { node: ">=6" }

    imurmurhash@0.1.4:
        resolution:
            {
                integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
            }
        engines: { node: ">=0.8.19" }

    indent-string@4.0.0:
        resolution:
            {
                integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==,
            }
        engines: { node: ">=8" }

    inherits@2.0.4:
        resolution:
            {
                integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
            }

    inline-style-parser@0.1.1:
        resolution:
            {
                integrity: sha512-7NXolsK4CAS5+xvdj5OMMbI962hU/wvwoxk+LWR9Ek9bVtyuuYScDN6eS0rUm6TxApFpw7CX1o4uJzcd4AyD3Q==,
            }

    internal-slot@1.1.0:
        resolution:
            {
                integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==,
            }
        engines: { node: ">= 0.4" }

    ipaddr.js@1.9.1:
        resolution:
            {
                integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==,
            }
        engines: { node: ">= 0.10" }

    is-alphabetical@2.0.1:
        resolution:
            {
                integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==,
            }

    is-alphanumerical@2.0.1:
        resolution:
            {
                integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==,
            }

    is-arguments@1.2.0:
        resolution:
            {
                integrity: sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==,
            }
        engines: { node: ">= 0.4" }

    is-array-buffer@3.0.5:
        resolution:
            {
                integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==,
            }
        engines: { node: ">= 0.4" }

    is-async-function@2.1.0:
        resolution:
            {
                integrity: sha512-GExz9MtyhlZyXYLxzlJRj5WUCE661zhDa1Yna52CN57AJsymh+DvXXjyveSioqSRdxvUrdKdvqB1b5cVKsNpWQ==,
            }
        engines: { node: ">= 0.4" }

    is-bigint@1.1.0:
        resolution:
            {
                integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==,
            }
        engines: { node: ">= 0.4" }

    is-binary-path@2.1.0:
        resolution:
            {
                integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
            }
        engines: { node: ">=8" }

    is-boolean-object@1.2.1:
        resolution:
            {
                integrity: sha512-l9qO6eFlUETHtuihLcYOaLKByJ1f+N4kthcU9YjHy3N+B3hWv0y/2Nd0mu/7lTFnRQHTrSdXF50HQ3bl5fEnng==,
            }
        engines: { node: ">= 0.4" }

    is-buffer@2.0.5:
        resolution:
            {
                integrity: sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==,
            }
        engines: { node: ">=4" }

    is-bun-module@1.3.0:
        resolution:
            {
                integrity: sha512-DgXeu5UWI0IsMQundYb5UAOzm6G2eVnarJ0byP6Tm55iZNKceD59LNPA2L4VvsScTtHcw0yEkVwSf7PC+QoLSA==,
            }

    is-callable@1.2.7:
        resolution:
            {
                integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==,
            }
        engines: { node: ">= 0.4" }

    is-core-module@2.16.1:
        resolution:
            {
                integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==,
            }
        engines: { node: ">= 0.4" }

    is-data-view@1.0.2:
        resolution:
            {
                integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==,
            }
        engines: { node: ">= 0.4" }

    is-date-object@1.1.0:
        resolution:
            {
                integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==,
            }
        engines: { node: ">= 0.4" }

    is-decimal@2.0.1:
        resolution:
            {
                integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==,
            }

    is-deflate@1.0.0:
        resolution:
            {
                integrity: sha512-YDoFpuZWu1VRXlsnlYMzKyVRITXj7Ej/V9gXQ2/pAe7X1J7M/RNOqaIYi6qUn+B7nGyB9pDXrv02dsB58d2ZAQ==,
            }

    is-extglob@2.1.1:
        resolution:
            {
                integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
            }
        engines: { node: ">=0.10.0" }

    is-finalizationregistry@1.1.1:
        resolution:
            {
                integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==,
            }
        engines: { node: ">= 0.4" }

    is-fullwidth-code-point@3.0.0:
        resolution:
            {
                integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
            }
        engines: { node: ">=8" }

    is-generator-function@1.1.0:
        resolution:
            {
                integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==,
            }
        engines: { node: ">= 0.4" }

    is-glob@4.0.3:
        resolution:
            {
                integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
            }
        engines: { node: ">=0.10.0" }

    is-gzip@1.0.0:
        resolution:
            {
                integrity: sha512-rcfALRIb1YewtnksfRIHGcIY93QnK8BIQ/2c9yDYcG/Y6+vRoJuTWBmmSEbyLLYtXm7q35pHOHbZFQBaLrhlWQ==,
            }
        engines: { node: ">=0.10.0" }

    is-hexadecimal@2.0.1:
        resolution:
            {
                integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==,
            }

    is-interactive@1.0.0:
        resolution:
            {
                integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==,
            }
        engines: { node: ">=8" }

    is-map@2.0.3:
        resolution:
            {
                integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==,
            }
        engines: { node: ">= 0.4" }

    is-number-object@1.1.1:
        resolution:
            {
                integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==,
            }
        engines: { node: ">= 0.4" }

    is-number@7.0.0:
        resolution:
            {
                integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
            }
        engines: { node: ">=0.12.0" }

    is-plain-obj@3.0.0:
        resolution:
            {
                integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==,
            }
        engines: { node: ">=10" }

    is-plain-obj@4.1.0:
        resolution:
            {
                integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==,
            }
        engines: { node: ">=12" }

    is-reference@3.0.3:
        resolution:
            {
                integrity: sha512-ixkJoqQvAP88E6wLydLGGqCJsrFUnqoH6HnaczB8XmDH1oaWU+xxdptvikTgaEhtZ53Ky6YXiBuUI2WXLMCwjw==,
            }

    is-regex@1.2.1:
        resolution:
            {
                integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==,
            }
        engines: { node: ">= 0.4" }

    is-set@2.0.3:
        resolution:
            {
                integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==,
            }
        engines: { node: ">= 0.4" }

    is-shared-array-buffer@1.0.4:
        resolution:
            {
                integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==,
            }
        engines: { node: ">= 0.4" }

    is-stream@2.0.1:
        resolution:
            {
                integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==,
            }
        engines: { node: ">=8" }

    is-string@1.1.1:
        resolution:
            {
                integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==,
            }
        engines: { node: ">= 0.4" }

    is-symbol@1.1.1:
        resolution:
            {
                integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==,
            }
        engines: { node: ">= 0.4" }

    is-typed-array@1.1.15:
        resolution:
            {
                integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==,
            }
        engines: { node: ">= 0.4" }

    is-unicode-supported@0.1.0:
        resolution:
            {
                integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==,
            }
        engines: { node: ">=10" }

    is-weakmap@2.0.2:
        resolution:
            {
                integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==,
            }
        engines: { node: ">= 0.4" }

    is-weakref@1.1.0:
        resolution:
            {
                integrity: sha512-SXM8Nwyys6nT5WP6pltOwKytLV7FqQ4UiibxVmW+EIosHcmCqkkjViTb5SNssDlkCiEYRP1/pdWUKVvZBmsR2Q==,
            }
        engines: { node: ">= 0.4" }

    is-weakset@2.0.4:
        resolution:
            {
                integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==,
            }
        engines: { node: ">= 0.4" }

    isarray@1.0.0:
        resolution:
            {
                integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==,
            }

    isarray@2.0.5:
        resolution:
            {
                integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==,
            }

    isbot@4.4.0:
        resolution:
            {
                integrity: sha512-8ZvOWUA68kyJO4hHJdWjyreq7TYNWTS9y15IzeqVdKxR9pPr3P/3r9AHcoIv9M0Rllkao5qWz2v1lmcyKIVCzQ==,
            }
        engines: { node: ">=18" }

    isexe@2.0.0:
        resolution:
            {
                integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
            }

    iterator.prototype@1.1.5:
        resolution:
            {
                integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==,
            }
        engines: { node: ">= 0.4" }

    jackspeak@3.4.3:
        resolution:
            {
                integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==,
            }

    javascript-stringify@2.1.0:
        resolution:
            {
                integrity: sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg==,
            }

    jiti@1.21.7:
        resolution:
            {
                integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==,
            }
        hasBin: true

    js-tokens@4.0.0:
        resolution:
            {
                integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
            }

    js-yaml@4.1.0:
        resolution:
            {
                integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
            }
        hasBin: true

    jsesc@3.0.2:
        resolution:
            {
                integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==,
            }
        engines: { node: ">=6" }
        hasBin: true

    json-buffer@3.0.1:
        resolution:
            {
                integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==,
            }

    json-parse-even-better-errors@3.0.2:
        resolution:
            {
                integrity: sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    json-schema-traverse@0.4.1:
        resolution:
            {
                integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==,
            }

    json-schema@0.4.0:
        resolution:
            {
                integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==,
            }

    json-stable-stringify-without-jsonify@1.0.1:
        resolution:
            {
                integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==,
            }

    json5@1.0.2:
        resolution:
            {
                integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==,
            }
        hasBin: true

    json5@2.2.3:
        resolution:
            {
                integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==,
            }
        engines: { node: ">=6" }
        hasBin: true

    jsonfile@6.1.0:
        resolution:
            {
                integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==,
            }

    jsx-ast-utils@3.3.5:
        resolution:
            {
                integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==,
            }
        engines: { node: ">=4.0" }

    keyv@4.5.4:
        resolution:
            {
                integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==,
            }

    kleur@4.1.5:
        resolution:
            {
                integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==,
            }
        engines: { node: ">=6" }

    language-subtag-registry@0.3.23:
        resolution:
            {
                integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==,
            }

    language-tags@1.0.9:
        resolution:
            {
                integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==,
            }
        engines: { node: ">=0.10" }

    levn@0.4.1:
        resolution:
            {
                integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==,
            }
        engines: { node: ">= 0.8.0" }

    lilconfig@3.1.3:
        resolution:
            {
                integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==,
            }
        engines: { node: ">=14" }

    lines-and-columns@1.2.4:
        resolution:
            {
                integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
            }

    loader-utils@3.3.1:
        resolution:
            {
                integrity: sha512-FMJTLMXfCLMLfJxcX9PFqX5qD88Z5MRGaZCVzfuqeZSPsyiBzs+pahDQjbIWz2QIzPZz0NX9Zy4FX3lmK6YHIg==,
            }
        engines: { node: ">= 12.13.0" }

    local-pkg@0.5.1:
        resolution:
            {
                integrity: sha512-9rrA30MRRP3gBD3HTGnC6cDFpaE1kVDWxWgqWJUN0RvDNAo+Nz/9GxB+nHOH0ifbVFy0hSA1V6vFDvnx54lTEQ==,
            }
        engines: { node: ">=14" }

    locate-path@6.0.0:
        resolution:
            {
                integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==,
            }
        engines: { node: ">=10" }

    lodash.camelcase@4.3.0:
        resolution:
            {
                integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==,
            }

    lodash.debounce@4.0.8:
        resolution:
            {
                integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==,
            }

    lodash.merge@4.6.2:
        resolution:
            {
                integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==,
            }

    lodash@4.17.21:
        resolution:
            {
                integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==,
            }

    log-symbols@4.1.0:
        resolution:
            {
                integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==,
            }
        engines: { node: ">=10" }

    longest-streak@3.1.0:
        resolution:
            {
                integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==,
            }

    loose-envify@1.4.0:
        resolution:
            {
                integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==,
            }
        hasBin: true

    lru-cache@10.4.3:
        resolution:
            {
                integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==,
            }

    lru-cache@5.1.1:
        resolution:
            {
                integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==,
            }

    lru-cache@7.18.3:
        resolution:
            {
                integrity: sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==,
            }
        engines: { node: ">=12" }

    lucide-react@0.469.0:
        resolution:
            {
                integrity: sha512-28vvUnnKQ/dBwiCQtwJw7QauYnE7yd2Cyp4tTTJpvglX4EMpbflcdBgrgToX2j71B3YvugK/NH3BGUk+E/p/Fw==,
            }
        peerDependencies:
            react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

    markdown-extensions@1.1.1:
        resolution:
            {
                integrity: sha512-WWC0ZuMzCyDHYCasEGs4IPvLyTGftYwh6wIEOULOF0HXcqZlhwRzrK0w2VUlxWA98xnvb/jszw4ZSkJ6ADpM6Q==,
            }
        engines: { node: ">=0.10.0" }

    math-intrinsics@1.1.0:
        resolution:
            {
                integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==,
            }
        engines: { node: ">= 0.4" }

    mdast-util-definitions@5.1.2:
        resolution:
            {
                integrity: sha512-8SVPMuHqlPME/z3gqVwWY4zVXn8lqKv/pAhC57FuJ40ImXyBpmO5ukh98zB2v7Blql2FiHjHv9LVztSIqjY+MA==,
            }

    mdast-util-from-markdown@1.3.1:
        resolution:
            {
                integrity: sha512-4xTO/M8c82qBcnQc1tgpNtubGUW/Y1tBQ1B0i5CtSoelOLKFYlElIr3bvgREYYO5iRqbMY1YuqZng0GVOI8Qww==,
            }

    mdast-util-frontmatter@1.0.1:
        resolution:
            {
                integrity: sha512-JjA2OjxRqAa8wEG8hloD0uTU0kdn8kbtOWpPP94NBkfAlbxn4S8gCGf/9DwFtEeGPXrDcNXdiDjVaRdUFqYokw==,
            }

    mdast-util-mdx-expression@1.3.2:
        resolution:
            {
                integrity: sha512-xIPmR5ReJDu/DHH1OoIT1HkuybIfRGYRywC+gJtI7qHjCJp/M9jrmBEJW22O8lskDWm562BX2W8TiAwRTb0rKA==,
            }

    mdast-util-mdx-jsx@2.1.4:
        resolution:
            {
                integrity: sha512-DtMn9CmVhVzZx3f+optVDF8yFgQVt7FghCRNdlIaS3X5Bnym3hZwPbg/XW86vdpKjlc1PVj26SpnLGeJBXD3JA==,
            }

    mdast-util-mdx@2.0.1:
        resolution:
            {
                integrity: sha512-38w5y+r8nyKlGvNjSEqWrhG0w5PmnRA+wnBvm+ulYCct7nsGYhFVb0lljS9bQav4psDAS1eGkP2LMVcZBi/aqw==,
            }

    mdast-util-mdxjs-esm@1.3.1:
        resolution:
            {
                integrity: sha512-SXqglS0HrEvSdUEfoXFtcg7DRl7S2cwOXc7jkuusG472Mmjag34DUDeOJUZtl+BVnyeO1frIgVpHlNRWc2gk/w==,
            }

    mdast-util-phrasing@3.0.1:
        resolution:
            {
                integrity: sha512-WmI1gTXUBJo4/ZmSk79Wcb2HcjPJBzM1nlI/OUWA8yk2X9ik3ffNbBGsU+09BFmXaL1IBb9fiuvq6/KMiNycSg==,
            }

    mdast-util-to-hast@12.3.0:
        resolution:
            {
                integrity: sha512-pits93r8PhnIoU4Vy9bjW39M2jJ6/tdHyja9rrot9uujkN7UTU9SDnE6WNJz/IGyQk3XHX6yNNtrBH6cQzm8Hw==,
            }

    mdast-util-to-markdown@1.5.0:
        resolution:
            {
                integrity: sha512-bbv7TPv/WC49thZPg3jXuqzuvI45IL2EVAr/KxF0BSdHsU0ceFHOmwQn6evxAh1GaoK/6GQ1wp4R4oW2+LFL/A==,
            }

    mdast-util-to-string@3.2.0:
        resolution:
            {
                integrity: sha512-V4Zn/ncyN1QNSqSBxTrMOLpjr+IKdHl2v3KVLoWmDPscP4r9GcCi71gjgvUV1SFSKh92AjAG4peFuBl2/YgCJg==,
            }

    media-query-parser@2.0.2:
        resolution:
            {
                integrity: sha512-1N4qp+jE0pL5Xv4uEcwVUhIkwdUO3S/9gML90nqKA7v7FcOS5vUtatfzok9S9U1EJU8dHWlcv95WLnKmmxZI9w==,
            }

    media-typer@0.3.0:
        resolution:
            {
                integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==,
            }
        engines: { node: ">= 0.6" }

    merge-descriptors@1.0.3:
        resolution:
            {
                integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==,
            }

    merge-stream@2.0.0:
        resolution:
            {
                integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==,
            }

    merge2@1.4.1:
        resolution:
            {
                integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
            }
        engines: { node: ">= 8" }

    methods@1.1.2:
        resolution:
            {
                integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==,
            }
        engines: { node: ">= 0.6" }

    micromark-core-commonmark@1.1.0:
        resolution:
            {
                integrity: sha512-BgHO1aRbolh2hcrzL2d1La37V0Aoz73ymF8rAcKnohLy93titmv62E0gP8Hrx9PKcKrqCZ1BbLGbP3bEhoXYlw==,
            }

    micromark-extension-frontmatter@1.1.1:
        resolution:
            {
                integrity: sha512-m2UH9a7n3W8VAH9JO9y01APpPKmNNNs71P0RbknEmYSaZU5Ghogv38BYO94AI5Xw6OYfxZRdHZZ2nYjs/Z+SZQ==,
            }

    micromark-extension-mdx-expression@1.0.8:
        resolution:
            {
                integrity: sha512-zZpeQtc5wfWKdzDsHRBY003H2Smg+PUi2REhqgIhdzAa5xonhP03FcXxqFSerFiNUr5AWmHpaNPQTBVOS4lrXw==,
            }

    micromark-extension-mdx-jsx@1.0.5:
        resolution:
            {
                integrity: sha512-gPH+9ZdmDflbu19Xkb8+gheqEDqkSpdCEubQyxuz/Hn8DOXiXvrXeikOoBA71+e8Pfi0/UYmU3wW3H58kr7akA==,
            }

    micromark-extension-mdx-md@1.0.1:
        resolution:
            {
                integrity: sha512-7MSuj2S7xjOQXAjjkbjBsHkMtb+mDGVW6uI2dBL9snOBCbZmoNgDAeZ0nSn9j3T42UE/g2xVNMn18PJxZvkBEA==,
            }

    micromark-extension-mdxjs-esm@1.0.5:
        resolution:
            {
                integrity: sha512-xNRBw4aoURcyz/S69B19WnZAkWJMxHMT5hE36GtDAyhoyn/8TuAeqjFJQlwk+MKQsUD7b3l7kFX+vlfVWgcX1w==,
            }

    micromark-extension-mdxjs@1.0.1:
        resolution:
            {
                integrity: sha512-7YA7hF6i5eKOfFUzZ+0z6avRG52GpWR8DL+kN47y3f2KhxbBZMhmxe7auOeaTBrW2DenbbZTf1ea9tA2hDpC2Q==,
            }

    micromark-factory-destination@1.1.0:
        resolution:
            {
                integrity: sha512-XaNDROBgx9SgSChd69pjiGKbV+nfHGDPVYFs5dOoDd7ZnMAE+Cuu91BCpsY8RT2NP9vo/B8pds2VQNCLiu0zhg==,
            }

    micromark-factory-label@1.1.0:
        resolution:
            {
                integrity: sha512-OLtyez4vZo/1NjxGhcpDSbHQ+m0IIGnT8BoPamh+7jVlzLJBH98zzuCoUeMxvM6WsNeh8wx8cKvqLiPHEACn0w==,
            }

    micromark-factory-mdx-expression@1.0.9:
        resolution:
            {
                integrity: sha512-jGIWzSmNfdnkJq05c7b0+Wv0Kfz3NJ3N4cBjnbO4zjXIlxJr+f8lk+5ZmwFvqdAbUy2q6B5rCY//g0QAAaXDWA==,
            }

    micromark-factory-space@1.1.0:
        resolution:
            {
                integrity: sha512-cRzEj7c0OL4Mw2v6nwzttyOZe8XY/Z8G0rzmWQZTBi/jjwyw/U4uqKtUORXQrR5bAZZnbTI/feRV/R7hc4jQYQ==,
            }

    micromark-factory-title@1.1.0:
        resolution:
            {
                integrity: sha512-J7n9R3vMmgjDOCY8NPw55jiyaQnH5kBdV2/UXCtZIpnHH3P6nHUKaH7XXEYuWwx/xUJcawa8plLBEjMPU24HzQ==,
            }

    micromark-factory-whitespace@1.1.0:
        resolution:
            {
                integrity: sha512-v2WlmiymVSp5oMg+1Q0N1Lxmt6pMhIHD457whWM7/GUlEks1hI9xj5w3zbc4uuMKXGisksZk8DzP2UyGbGqNsQ==,
            }

    micromark-util-character@1.2.0:
        resolution:
            {
                integrity: sha512-lXraTwcX3yH/vMDaFWCQJP1uIszLVebzUa3ZHdrgxr7KEU/9mL4mVgCpGbyhvNLNlauROiNUq7WN5u7ndbY6xg==,
            }

    micromark-util-chunked@1.1.0:
        resolution:
            {
                integrity: sha512-Ye01HXpkZPNcV6FiyoW2fGZDUw4Yc7vT0E9Sad83+bEDiCJ1uXu0S3mr8WLpsz3HaG3x2q0HM6CTuPdcZcluFQ==,
            }

    micromark-util-classify-character@1.1.0:
        resolution:
            {
                integrity: sha512-SL0wLxtKSnklKSUplok1WQFoGhUdWYKggKUiqhX+Swala+BtptGCu5iPRc+xvzJ4PXE/hwM3FNXsfEVgoZsWbw==,
            }

    micromark-util-combine-extensions@1.1.0:
        resolution:
            {
                integrity: sha512-Q20sp4mfNf9yEqDL50WwuWZHUrCO4fEyeDCnMGmG5Pr0Cz15Uo7KBs6jq+dq0EgX4DPwwrh9m0X+zPV1ypFvUA==,
            }

    micromark-util-decode-numeric-character-reference@1.1.0:
        resolution:
            {
                integrity: sha512-m9V0ExGv0jB1OT21mrWcuf4QhP46pH1KkfWy9ZEezqHKAxkj4mPCy3nIH1rkbdMlChLHX531eOrymlwyZIf2iw==,
            }

    micromark-util-decode-string@1.1.0:
        resolution:
            {
                integrity: sha512-YphLGCK8gM1tG1bd54azwyrQRjCFcmgj2S2GoJDNnh4vYtnL38JS8M4gpxzOPNyHdNEpheyWXCTnnTDY3N+NVQ==,
            }

    micromark-util-encode@1.1.0:
        resolution:
            {
                integrity: sha512-EuEzTWSTAj9PA5GOAs992GzNh2dGQO52UvAbtSOMvXTxv3Criqb6IOzJUBCmEqrrXSblJIJBbFFv6zPxpreiJw==,
            }

    micromark-util-events-to-acorn@1.2.3:
        resolution:
            {
                integrity: sha512-ij4X7Wuc4fED6UoLWkmo0xJQhsktfNh1J0m8g4PbIMPlx+ek/4YdW5mvbye8z/aZvAPUoxgXHrwVlXAPKMRp1w==,
            }

    micromark-util-html-tag-name@1.2.0:
        resolution:
            {
                integrity: sha512-VTQzcuQgFUD7yYztuQFKXT49KghjtETQ+Wv/zUjGSGBioZnkA4P1XXZPT1FHeJA6RwRXSF47yvJ1tsJdoxwO+Q==,
            }

    micromark-util-normalize-identifier@1.1.0:
        resolution:
            {
                integrity: sha512-N+w5vhqrBihhjdpM8+5Xsxy71QWqGn7HYNUvch71iV2PM7+E3uWGox1Qp90loa1ephtCxG2ftRV/Conitc6P2Q==,
            }

    micromark-util-resolve-all@1.1.0:
        resolution:
            {
                integrity: sha512-b/G6BTMSg+bX+xVCshPTPyAu2tmA0E4X98NSR7eIbeC6ycCqCeE7wjfDIgzEbkzdEVJXRtOG4FbEm/uGbCRouA==,
            }

    micromark-util-sanitize-uri@1.2.0:
        resolution:
            {
                integrity: sha512-QO4GXv0XZfWey4pYFndLUKEAktKkG5kZTdUNaTAkzbuJxn2tNBOr+QtxR2XpWaMhbImT2dPzyLrPXLlPhph34A==,
            }

    micromark-util-subtokenize@1.1.0:
        resolution:
            {
                integrity: sha512-kUQHyzRoxvZO2PuLzMt2P/dwVsTiivCK8icYTeR+3WgbuPqfHgPPy7nFKbeqRivBvn/3N3GBiNC+JRTMSxEC7A==,
            }

    micromark-util-symbol@1.1.0:
        resolution:
            {
                integrity: sha512-uEjpEYY6KMs1g7QfJ2eX1SQEV+ZT4rUD3UcF6l57acZvLNK7PBZL+ty82Z1qhK1/yXIY4bdx04FKMgR0g4IAag==,
            }

    micromark-util-types@1.1.0:
        resolution:
            {
                integrity: sha512-ukRBgie8TIAcacscVHSiddHjO4k/q3pnedmzMQ4iwDcK0FtFCohKOlFbaOL/mPgfnPsL3C1ZyxJa4sbWrBl3jg==,
            }

    micromark@3.2.0:
        resolution:
            {
                integrity: sha512-uD66tJj54JLYq0De10AhWycZWGQNUvDI55xPgk2sQM5kn1JYlhbCMTtEeT27+vAhW2FBQxLlOmS3pmA7/2z4aA==,
            }

    micromatch@4.0.8:
        resolution:
            {
                integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==,
            }
        engines: { node: ">=8.6" }

    mime-db@1.52.0:
        resolution:
            {
                integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
            }
        engines: { node: ">= 0.6" }

    mime-db@1.53.0:
        resolution:
            {
                integrity: sha512-oHlN/w+3MQ3rba9rqFr6V/ypF10LSkdwUysQL7GkXoTgIWeV+tcXGA852TBxH+gsh8UWoyhR1hKcoMJTuWflpg==,
            }
        engines: { node: ">= 0.6" }

    mime-types@2.1.35:
        resolution:
            {
                integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
            }
        engines: { node: ">= 0.6" }

    mime@1.6.0:
        resolution:
            {
                integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==,
            }
        engines: { node: ">=4" }
        hasBin: true

    mimic-fn@2.1.0:
        resolution:
            {
                integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==,
            }
        engines: { node: ">=6" }

    minimatch@3.1.2:
        resolution:
            {
                integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
            }

    minimatch@9.0.3:
        resolution:
            {
                integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==,
            }
        engines: { node: ">=16 || 14 >=14.17" }

    minimatch@9.0.5:
        resolution:
            {
                integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==,
            }
        engines: { node: ">=16 || 14 >=14.17" }

    minimist@1.2.8:
        resolution:
            {
                integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
            }

    minipass-collect@1.0.2:
        resolution:
            {
                integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==,
            }
        engines: { node: ">= 8" }

    minipass-flush@1.0.5:
        resolution:
            {
                integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==,
            }
        engines: { node: ">= 8" }

    minipass-pipeline@1.2.4:
        resolution:
            {
                integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==,
            }
        engines: { node: ">=8" }

    minipass@3.3.6:
        resolution:
            {
                integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==,
            }
        engines: { node: ">=8" }

    minipass@5.0.0:
        resolution:
            {
                integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==,
            }
        engines: { node: ">=8" }

    minipass@7.1.2:
        resolution:
            {
                integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
            }
        engines: { node: ">=16 || 14 >=14.17" }

    minizlib@2.1.2:
        resolution:
            {
                integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==,
            }
        engines: { node: ">= 8" }

    mkdirp-classic@0.5.3:
        resolution:
            {
                integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==,
            }

    mkdirp@1.0.4:
        resolution:
            {
                integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==,
            }
        engines: { node: ">=10" }
        hasBin: true

    mlly@1.7.3:
        resolution:
            {
                integrity: sha512-xUsx5n/mN0uQf4V548PKQ+YShA4/IW0KI1dZhrNrPCLG+xizETbHTkOa1f8/xut9JRPp8kQuMnz0oqwkTiLo/A==,
            }

    modern-ahocorasick@1.1.0:
        resolution:
            {
                integrity: sha512-sEKPVl2rM+MNVkGQt3ChdmD8YsigmXdn5NifZn6jiwn9LRJpWm8F3guhaqrJT/JOat6pwpbXEk6kv+b9DMIjsQ==,
            }

    morgan@1.10.0:
        resolution:
            {
                integrity: sha512-AbegBVI4sh6El+1gNwvD5YIck7nSA36weD7xvIxG4in80j/UoK8AEGaWnnz8v1GxonMCltmlNs5ZKbGvl9b1XQ==,
            }
        engines: { node: ">= 0.8.0" }

    motion-dom@11.16.0:
        resolution:
            {
                integrity: sha512-4bmEwajSdrljzDAYpu6ceEdtI4J5PH25fmN8YSx7Qxk6OMrC10CXM0D5y+VO/pFZjhmCvm2bGf7Rus482kwhzA==,
            }

    motion-utils@11.16.0:
        resolution:
            {
                integrity: sha512-ngdWPjg31rD4WGXFi0eZ00DQQqKKu04QExyv/ymlC+3k+WIgYVFbt6gS5JsFPbJODTF/r8XiE/X+SsoT9c0ocw==,
            }

    mri@1.2.0:
        resolution:
            {
                integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==,
            }
        engines: { node: ">=4" }

    mrmime@1.0.1:
        resolution:
            {
                integrity: sha512-hzzEagAgDyoU1Q6yg5uI+AorQgdvMCur3FcKf7NhMKWsaYg+RnbTyHRa/9IlLF9rf455MOCtcqqrQQ83pPP7Uw==,
            }
        engines: { node: ">=10" }

    ms@2.0.0:
        resolution:
            {
                integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==,
            }

    ms@2.1.3:
        resolution:
            {
                integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
            }

    mz@2.7.0:
        resolution:
            {
                integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
            }

    nanoid@3.3.8:
        resolution:
            {
                integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==,
            }
        engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
        hasBin: true

    nanoid@5.0.9:
        resolution:
            {
                integrity: sha512-Aooyr6MXU6HpvvWXKoVoXwKMs/KyVakWwg7xQfv5/S/RIgJMy0Ifa45H9qqYy7pTCszrHzP21Uk4PZq2HpEM8Q==,
            }
        engines: { node: ^18 || >=20 }
        hasBin: true

    natural-compare@1.4.0:
        resolution:
            {
                integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==,
            }

    negotiator@0.6.3:
        resolution:
            {
                integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==,
            }
        engines: { node: ">= 0.6" }

    negotiator@0.6.4:
        resolution:
            {
                integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==,
            }
        engines: { node: ">= 0.6" }

    node-releases@2.0.19:
        resolution:
            {
                integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==,
            }

    normalize-package-data@5.0.0:
        resolution:
            {
                integrity: sha512-h9iPVIfrVZ9wVYQnxFgtw1ugSvGEMOlyPWWtm8BMJhnwyEL/FLbYbTY3V3PpjI/BUK67n9PEWDu6eHzu1fB15Q==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    normalize-path@3.0.0:
        resolution:
            {
                integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
            }
        engines: { node: ">=0.10.0" }

    normalize-range@0.1.2:
        resolution:
            {
                integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==,
            }
        engines: { node: ">=0.10.0" }

    npm-install-checks@6.3.0:
        resolution:
            {
                integrity: sha512-W29RiK/xtpCGqn6f3ixfRYGk+zRyr+Ew9F2E20BfXxT5/euLdA/Nm7fO7OeTGuAmTs30cpgInyJ0cYe708YTZw==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    npm-normalize-package-bin@3.0.1:
        resolution:
            {
                integrity: sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    npm-package-arg@10.1.0:
        resolution:
            {
                integrity: sha512-uFyyCEmgBfZTtrKk/5xDfHp6+MdrqGotX/VoOyEEl3mBwiEE5FlBaePanazJSVMPT7vKepcjYBY2ztg9A3yPIA==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    npm-pick-manifest@8.0.2:
        resolution:
            {
                integrity: sha512-1dKY+86/AIiq1tkKVD3l0WI+Gd3vkknVGAggsFeBkTvbhMQ1OND/LKkYv4JtXPKUJ8bOTCyLiqEg2P6QNdK+Gg==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    npm-run-path@4.0.1:
        resolution:
            {
                integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==,
            }
        engines: { node: ">=8" }

    object-assign@4.1.1:
        resolution:
            {
                integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
            }
        engines: { node: ">=0.10.0" }

    object-hash@3.0.0:
        resolution:
            {
                integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==,
            }
        engines: { node: ">= 6" }

    object-inspect@1.13.3:
        resolution:
            {
                integrity: sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==,
            }
        engines: { node: ">= 0.4" }

    object-keys@1.1.1:
        resolution:
            {
                integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==,
            }
        engines: { node: ">= 0.4" }

    object.assign@4.1.7:
        resolution:
            {
                integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==,
            }
        engines: { node: ">= 0.4" }

    object.entries@1.1.8:
        resolution:
            {
                integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==,
            }
        engines: { node: ">= 0.4" }

    object.fromentries@2.0.8:
        resolution:
            {
                integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==,
            }
        engines: { node: ">= 0.4" }

    object.groupby@1.0.3:
        resolution:
            {
                integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==,
            }
        engines: { node: ">= 0.4" }

    object.values@1.2.1:
        resolution:
            {
                integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==,
            }
        engines: { node: ">= 0.4" }

    on-finished@2.3.0:
        resolution:
            {
                integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==,
            }
        engines: { node: ">= 0.8" }

    on-finished@2.4.1:
        resolution:
            {
                integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==,
            }
        engines: { node: ">= 0.8" }

    on-headers@1.0.2:
        resolution:
            {
                integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==,
            }
        engines: { node: ">= 0.8" }

    once@1.4.0:
        resolution:
            {
                integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
            }

    onetime@5.1.2:
        resolution:
            {
                integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==,
            }
        engines: { node: ">=6" }

    optionator@0.9.4:
        resolution:
            {
                integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==,
            }
        engines: { node: ">= 0.8.0" }

    ora@5.4.1:
        resolution:
            {
                integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==,
            }
        engines: { node: ">=10" }

    outdent@0.8.0:
        resolution:
            {
                integrity: sha512-KiOAIsdpUTcAXuykya5fnVVT+/5uS0Q1mrkRHcF89tpieSmY33O/tmc54CqwA+bfhbtEfZUNLHaPUiB9X3jt1A==,
            }

    own-keys@1.0.1:
        resolution:
            {
                integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==,
            }
        engines: { node: ">= 0.4" }

    p-limit@3.1.0:
        resolution:
            {
                integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==,
            }
        engines: { node: ">=10" }

    p-locate@5.0.0:
        resolution:
            {
                integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==,
            }
        engines: { node: ">=10" }

    p-map@4.0.0:
        resolution:
            {
                integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==,
            }
        engines: { node: ">=10" }

    package-json-from-dist@1.0.1:
        resolution:
            {
                integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==,
            }

    pako@0.2.9:
        resolution:
            {
                integrity: sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==,
            }

    parent-module@1.0.1:
        resolution:
            {
                integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==,
            }
        engines: { node: ">=6" }

    parse-entities@4.0.2:
        resolution:
            {
                integrity: sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==,
            }

    parse-ms@2.1.0:
        resolution:
            {
                integrity: sha512-kHt7kzLoS9VBZfUsiKjv43mr91ea+U05EyKkEtqp7vNbHxmaVuEqN7XxeEVnGrMtYOAxGrDElSi96K7EgO1zCA==,
            }
        engines: { node: ">=6" }

    parseurl@1.3.3:
        resolution:
            {
                integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==,
            }
        engines: { node: ">= 0.8" }

    path-exists@4.0.0:
        resolution:
            {
                integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
            }
        engines: { node: ">=8" }

    path-key@3.1.1:
        resolution:
            {
                integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
            }
        engines: { node: ">=8" }

    path-parse@1.0.7:
        resolution:
            {
                integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
            }

    path-scurry@1.11.1:
        resolution:
            {
                integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
            }
        engines: { node: ">=16 || 14 >=14.18" }

    path-to-regexp@0.1.12:
        resolution:
            {
                integrity: sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==,
            }

    path-type@4.0.0:
        resolution:
            {
                integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==,
            }
        engines: { node: ">=8" }

    pathe@1.1.2:
        resolution:
            {
                integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==,
            }

    peek-stream@1.1.3:
        resolution:
            {
                integrity: sha512-FhJ+YbOSBb9/rIl2ZeE/QHEsWn7PqNYt8ARAY3kIgNGOk13g9FGyIY6JIl/xB/3TFRVoTv5as0l11weORrTekA==,
            }

    periscopic@3.1.0:
        resolution:
            {
                integrity: sha512-vKiQ8RRtkl9P+r/+oefh25C3fhybptkHKCZSPlcXiJux2tJF55GnEj3BVn4A5gKfq9NWWXXrxkHBwVPUfH0opw==,
            }

    picocolors@1.1.1:
        resolution:
            {
                integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
            }

    picomatch@2.3.1:
        resolution:
            {
                integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
            }
        engines: { node: ">=8.6" }

    pidtree@0.6.0:
        resolution:
            {
                integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==,
            }
        engines: { node: ">=0.10" }
        hasBin: true

    pify@2.3.0:
        resolution:
            {
                integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==,
            }
        engines: { node: ">=0.10.0" }

    pirates@4.0.6:
        resolution:
            {
                integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==,
            }
        engines: { node: ">= 6" }

    pkg-types@1.3.0:
        resolution:
            {
                integrity: sha512-kS7yWjVFCkIw9hqdJBoMxDdzEngmkr5FXeWZZfQ6GoYacjVnsW6l2CcYW/0ThD0vF4LPJgVYnrg4d0uuhwYQbg==,
            }

    possible-typed-array-names@1.0.0:
        resolution:
            {
                integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==,
            }
        engines: { node: ">= 0.4" }

    postcss-discard-duplicates@5.1.0:
        resolution:
            {
                integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==,
            }
        engines: { node: ^10 || ^12 || >=14.0 }
        peerDependencies:
            postcss: ^8.2.15

    postcss-import@15.1.0:
        resolution:
            {
                integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==,
            }
        engines: { node: ">=14.0.0" }
        peerDependencies:
            postcss: ^8.0.0

    postcss-js@4.0.1:
        resolution:
            {
                integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==,
            }
        engines: { node: ^12 || ^14 || >= 16 }
        peerDependencies:
            postcss: ^8.4.21

    postcss-load-config@4.0.2:
        resolution:
            {
                integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==,
            }
        engines: { node: ">= 14" }
        peerDependencies:
            postcss: ">=8.0.9"
            ts-node: ">=9.0.0"
        peerDependenciesMeta:
            postcss:
                optional: true
            ts-node:
                optional: true

    postcss-modules-extract-imports@3.1.0:
        resolution:
            {
                integrity: sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==,
            }
        engines: { node: ^10 || ^12 || >= 14 }
        peerDependencies:
            postcss: ^8.1.0

    postcss-modules-local-by-default@4.2.0:
        resolution:
            {
                integrity: sha512-5kcJm/zk+GJDSfw+V/42fJ5fhjL5YbFDl8nVdXkJPLLW+Vf9mTD5Xe0wqIaDnLuL2U6cDNpTr+UQ+v2HWIBhzw==,
            }
        engines: { node: ^10 || ^12 || >= 14 }
        peerDependencies:
            postcss: ^8.1.0

    postcss-modules-scope@3.2.1:
        resolution:
            {
                integrity: sha512-m9jZstCVaqGjTAuny8MdgE88scJnCiQSlSrOWcTQgM2t32UBe+MUmFSO5t7VMSfAf/FJKImAxBav8ooCHJXCJA==,
            }
        engines: { node: ^10 || ^12 || >= 14 }
        peerDependencies:
            postcss: ^8.1.0

    postcss-modules-values@4.0.0:
        resolution:
            {
                integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==,
            }
        engines: { node: ^10 || ^12 || >= 14 }
        peerDependencies:
            postcss: ^8.1.0

    postcss-modules@6.0.1:
        resolution:
            {
                integrity: sha512-zyo2sAkVvuZFFy0gc2+4O+xar5dYlaVy/ebO24KT0ftk/iJevSNyPyQellsBLlnccwh7f6V6Y4GvuKRYToNgpQ==,
            }
        peerDependencies:
            postcss: ^8.0.0

    postcss-nested@6.2.0:
        resolution:
            {
                integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==,
            }
        engines: { node: ">=12.0" }
        peerDependencies:
            postcss: ^8.2.14

    postcss-selector-parser@6.1.2:
        resolution:
            {
                integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==,
            }
        engines: { node: ">=4" }

    postcss-selector-parser@7.0.0:
        resolution:
            {
                integrity: sha512-9RbEr1Y7FFfptd/1eEdntyjMwLeghW1bHX9GWjXo19vx4ytPQhANltvVxDggzJl7mnWM+dX28kb6cyS/4iQjlQ==,
            }
        engines: { node: ">=4" }

    postcss-value-parser@4.2.0:
        resolution:
            {
                integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==,
            }

    postcss@8.4.49:
        resolution:
            {
                integrity: sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==,
            }
        engines: { node: ^10 || ^12 || >=14 }

    prelude-ls@1.2.1:
        resolution:
            {
                integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==,
            }
        engines: { node: ">= 0.8.0" }

    prettier@2.8.8:
        resolution:
            {
                integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==,
            }
        engines: { node: ">=10.13.0" }
        hasBin: true

    pretty-ms@7.0.1:
        resolution:
            {
                integrity: sha512-973driJZvxiGOQ5ONsFhOF/DtzPMOMtgC11kCpUrPGMTgqp2q/1gwzCquocrN33is0VZ5GFHXZYMM9l6h67v2Q==,
            }
        engines: { node: ">=10" }

    proc-log@3.0.0:
        resolution:
            {
                integrity: sha512-++Vn7NS4Xf9NacaU9Xq3URUuqZETPsf8L4j5/ckhaRYsfPeRyzGw+iDjFhV/Jr3uNmTvvddEJFWh5R1gRgUH8A==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    process-nextick-args@2.0.1:
        resolution:
            {
                integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==,
            }

    promise-inflight@1.0.1:
        resolution:
            {
                integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==,
            }
        peerDependencies:
            bluebird: "*"
        peerDependenciesMeta:
            bluebird:
                optional: true

    promise-retry@2.0.1:
        resolution:
            {
                integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==,
            }
        engines: { node: ">=10" }

    prop-types@15.8.1:
        resolution:
            {
                integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==,
            }

    property-information@6.5.0:
        resolution:
            {
                integrity: sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==,
            }

    proxy-addr@2.0.7:
        resolution:
            {
                integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==,
            }
        engines: { node: ">= 0.10" }

    pump@2.0.1:
        resolution:
            {
                integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==,
            }

    pump@3.0.2:
        resolution:
            {
                integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==,
            }

    pumpify@1.5.1:
        resolution:
            {
                integrity: sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==,
            }

    punycode@2.3.1:
        resolution:
            {
                integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
            }
        engines: { node: ">=6" }

    qs@6.13.0:
        resolution:
            {
                integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==,
            }
        engines: { node: ">=0.6" }

    queue-microtask@1.2.3:
        resolution:
            {
                integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
            }

    range-parser@1.2.1:
        resolution:
            {
                integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==,
            }
        engines: { node: ">= 0.6" }

    raw-body@2.5.2:
        resolution:
            {
                integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==,
            }
        engines: { node: ">= 0.8" }

    react-aiwriter@1.0.0:
        resolution:
            {
                integrity: sha512-MxNAN3FUMNeHlbuPIiWFtXJlHBJx1+o5wKGtkmbmEywc7DjdjK6Xq1XVK3w+vBsuRARTACza3uxwsae1SqrAAw==,
            }

    react-dom@18.3.1:
        resolution:
            {
                integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==,
            }
        peerDependencies:
            react: ^18.3.1

    react-is@16.13.1:
        resolution:
            {
                integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==,
            }

    react-refresh@0.14.2:
        resolution:
            {
                integrity: sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==,
            }
        engines: { node: ">=0.10.0" }

    react-remove-scroll-bar@2.3.8:
        resolution:
            {
                integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            "@types/react":
                optional: true

    react-remove-scroll@2.6.2:
        resolution:
            {
                integrity: sha512-KmONPx5fnlXYJQqC62Q+lwIeAk64ws/cUw6omIumRzMRPqgnYqhSSti99nbj0Ry13bv7dF+BKn7NB+OqkdZGTw==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    react-router-dom@6.28.1:
        resolution:
            {
                integrity: sha512-YraE27C/RdjcZwl5UCqF/ffXnZDxpJdk9Q6jw38SZHjXs7NNdpViq2l2c7fO7+4uWaEfcwfGCv3RSg4e1By/fQ==,
            }
        engines: { node: ">=14.0.0" }
        peerDependencies:
            react: ">=16.8"
            react-dom: ">=16.8"

    react-router@6.28.1:
        resolution:
            {
                integrity: sha512-2omQTA3rkMljmrvvo6WtewGdVh45SpL9hGiCI9uUrwGGfNFDIvGK4gYJsKlJoNVi6AQZcopSCballL+QGOm7fA==,
            }
        engines: { node: ">=14.0.0" }
        peerDependencies:
            react: ">=16.8"

    react-style-singleton@2.2.3:
        resolution:
            {
                integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    react-textarea-autosize@8.5.6:
        resolution:
            {
                integrity: sha512-aT3ioKXMa8f6zHYGebhbdMD2L00tKeRX1zuVuDx9YQK/JLLRSaSxq3ugECEmUB9z2kvk6bFSIoRHLkkUv0RJiw==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

    react@18.3.1:
        resolution:
            {
                integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==,
            }
        engines: { node: ">=0.10.0" }

    read-cache@1.0.0:
        resolution:
            {
                integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==,
            }

    readable-stream@2.3.8:
        resolution:
            {
                integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==,
            }

    readable-stream@3.6.2:
        resolution:
            {
                integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
            }
        engines: { node: ">= 6" }

    readdirp@3.6.0:
        resolution:
            {
                integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
            }
        engines: { node: ">=8.10.0" }

    reflect.getprototypeof@1.0.10:
        resolution:
            {
                integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==,
            }
        engines: { node: ">= 0.4" }

    regenerator-runtime@0.14.1:
        resolution:
            {
                integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==,
            }

    regexp.prototype.flags@1.5.4:
        resolution:
            {
                integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==,
            }
        engines: { node: ">= 0.4" }

    remark-frontmatter@4.0.1:
        resolution:
            {
                integrity: sha512-38fJrB0KnmD3E33a5jZC/5+gGAC2WKNiPw1/fdXJvijBlhA7RCsvJklrYJakS0HedninvaCYW8lQGf9C918GfA==,
            }

    remark-mdx-frontmatter@1.1.1:
        resolution:
            {
                integrity: sha512-7teX9DW4tI2WZkXS4DBxneYSY7NHiXl4AKdWDO9LXVweULlCT8OPWsOjLEnMIXViN1j+QcY8mfbq3k0EK6x3uA==,
            }
        engines: { node: ">=12.2.0" }

    remark-mdx@2.3.0:
        resolution:
            {
                integrity: sha512-g53hMkpM0I98MU266IzDFMrTD980gNF3BJnkyFcmN+dD873mQeD5rdMO3Y2X+x8umQfbSE0PcoEDl7ledSA+2g==,
            }

    remark-parse@10.0.2:
        resolution:
            {
                integrity: sha512-3ydxgHa/ZQzG8LvC7jTXccARYDcRld3VfcgIIFs7bI6vbRSxJJmzgLEIIoYKyrfhaY+ujuWaf/PJiMZXoiCXgw==,
            }

    remark-rehype@10.1.0:
        resolution:
            {
                integrity: sha512-EFmR5zppdBp0WQeDVZ/b66CWJipB2q2VLNFMabzDSGR66Z2fQii83G5gTBbgGEnEEA0QRussvrFHxk1HWGJskw==,
            }

    require-like@0.1.2:
        resolution:
            {
                integrity: sha512-oyrU88skkMtDdauHDuKVrgR+zuItqr6/c//FXzvmxRGMexSDc6hNvJInGW3LL46n+8b50RykrvwSUIIQH2LQ5A==,
            }

    resolve-from@4.0.0:
        resolution:
            {
                integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==,
            }
        engines: { node: ">=4" }

    resolve-pkg-maps@1.0.0:
        resolution:
            {
                integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==,
            }

    resolve.exports@2.0.3:
        resolution:
            {
                integrity: sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==,
            }
        engines: { node: ">=10" }

    resolve@1.22.10:
        resolution:
            {
                integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==,
            }
        engines: { node: ">= 0.4" }
        hasBin: true

    resolve@2.0.0-next.5:
        resolution:
            {
                integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==,
            }
        hasBin: true

    restore-cursor@3.1.0:
        resolution:
            {
                integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==,
            }
        engines: { node: ">=8" }

    retry@0.12.0:
        resolution:
            {
                integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==,
            }
        engines: { node: ">= 4" }

    reusify@1.0.4:
        resolution:
            {
                integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==,
            }
        engines: { iojs: ">=1.0.0", node: ">=0.10.0" }

    rollup@4.30.1:
        resolution:
            {
                integrity: sha512-mlJ4glW020fPuLi7DkM/lN97mYEZGWeqBnrljzN0gs7GLctqX3lNWxKQ7Gl712UAX+6fog/L3jh4gb7R6aVi3w==,
            }
        engines: { node: ">=18.0.0", npm: ">=8.0.0" }
        hasBin: true

    run-parallel@1.2.0:
        resolution:
            {
                integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
            }

    sade@1.8.1:
        resolution:
            {
                integrity: sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==,
            }
        engines: { node: ">=6" }

    safe-array-concat@1.1.3:
        resolution:
            {
                integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==,
            }
        engines: { node: ">=0.4" }

    safe-buffer@5.1.2:
        resolution:
            {
                integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==,
            }

    safe-buffer@5.2.1:
        resolution:
            {
                integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==,
            }

    safe-push-apply@1.0.0:
        resolution:
            {
                integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==,
            }
        engines: { node: ">= 0.4" }

    safe-regex-test@1.1.0:
        resolution:
            {
                integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==,
            }
        engines: { node: ">= 0.4" }

    safer-buffer@2.1.2:
        resolution:
            {
                integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
            }

    scheduler@0.23.2:
        resolution:
            {
                integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==,
            }

    secure-json-parse@3.0.2:
        resolution:
            {
                integrity: sha512-H6nS2o8bWfpFEV6U38sOSjS7bTbdgbCGU9wEM6W14P5H0QOsz94KCusifV44GpHDTu2nqZbuDNhTzu+mjDSw1w==,
            }

    semver@6.3.1:
        resolution:
            {
                integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
            }
        hasBin: true

    semver@7.6.3:
        resolution:
            {
                integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==,
            }
        engines: { node: ">=10" }
        hasBin: true

    send@0.19.0:
        resolution:
            {
                integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==,
            }
        engines: { node: ">= 0.8.0" }

    serve-static@1.16.2:
        resolution:
            {
                integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==,
            }
        engines: { node: ">= 0.8.0" }

    set-cookie-parser@2.7.1:
        resolution:
            {
                integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==,
            }

    set-function-length@1.2.2:
        resolution:
            {
                integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==,
            }
        engines: { node: ">= 0.4" }

    set-function-name@2.0.2:
        resolution:
            {
                integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==,
            }
        engines: { node: ">= 0.4" }

    set-proto@1.0.0:
        resolution:
            {
                integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==,
            }
        engines: { node: ">= 0.4" }

    setprototypeof@1.2.0:
        resolution:
            {
                integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==,
            }

    shebang-command@2.0.0:
        resolution:
            {
                integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
            }
        engines: { node: ">=8" }

    shebang-regex@3.0.0:
        resolution:
            {
                integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
            }
        engines: { node: ">=8" }

    side-channel-list@1.0.0:
        resolution:
            {
                integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==,
            }
        engines: { node: ">= 0.4" }

    side-channel-map@1.0.1:
        resolution:
            {
                integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==,
            }
        engines: { node: ">= 0.4" }

    side-channel-weakmap@1.0.2:
        resolution:
            {
                integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==,
            }
        engines: { node: ">= 0.4" }

    side-channel@1.1.0:
        resolution:
            {
                integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==,
            }
        engines: { node: ">= 0.4" }

    signal-exit@3.0.7:
        resolution:
            {
                integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==,
            }

    signal-exit@4.1.0:
        resolution:
            {
                integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
            }
        engines: { node: ">=14" }

    slash@3.0.0:
        resolution:
            {
                integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==,
            }
        engines: { node: ">=8" }

    source-map-js@1.2.1:
        resolution:
            {
                integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
            }
        engines: { node: ">=0.10.0" }

    source-map-support@0.5.21:
        resolution:
            {
                integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==,
            }

    source-map@0.6.1:
        resolution:
            {
                integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==,
            }
        engines: { node: ">=0.10.0" }

    source-map@0.7.4:
        resolution:
            {
                integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==,
            }
        engines: { node: ">= 8" }

    space-separated-tokens@2.0.2:
        resolution:
            {
                integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==,
            }

    spdx-correct@3.2.0:
        resolution:
            {
                integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==,
            }

    spdx-exceptions@2.5.0:
        resolution:
            {
                integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==,
            }

    spdx-expression-parse@3.0.1:
        resolution:
            {
                integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==,
            }

    spdx-license-ids@3.0.20:
        resolution:
            {
                integrity: sha512-jg25NiDV/1fLtSgEgyvVyDunvaNHbuwF9lfNV17gSmPFAlYzdfNBlLtLzXTevwkPj7DhGbmN9VnmJIgLnhvaBw==,
            }

    ssri@10.0.6:
        resolution:
            {
                integrity: sha512-MGrFH9Z4NP9Iyhqn16sDtBpRRNJ0Y2hNa6D65h736fVSaPCHr4DM4sWUNvVaSuC+0OBGhwsrydQwmgfg5LncqQ==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    stable-hash@0.0.4:
        resolution:
            {
                integrity: sha512-LjdcbuBeLcdETCrPn9i8AYAZ1eCtu4ECAWtP7UleOiZ9LzVxRzzUZEoZ8zB24nhkQnDWyET0I+3sWokSDS3E7g==,
            }

    statuses@2.0.1:
        resolution:
            {
                integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==,
            }
        engines: { node: ">= 0.8" }

    stream-shift@1.0.3:
        resolution:
            {
                integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==,
            }

    stream-slice@0.1.2:
        resolution:
            {
                integrity: sha512-QzQxpoacatkreL6jsxnVb7X5R/pGw9OUv2qWTYWnmLpg4NdN31snPy/f3TdQE1ZUXaThRvj1Zw4/OGg0ZkaLMA==,
            }

    string-hash@1.1.3:
        resolution:
            {
                integrity: sha512-kJUvRUFK49aub+a7T1nNE66EJbZBMnBgoC1UbCZ5n6bsZKBRga4KgBRTMn/pFkeCZSYtNeSyMxPDM0AXWELk2A==,
            }

    string-width@4.2.3:
        resolution:
            {
                integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
            }
        engines: { node: ">=8" }

    string-width@5.1.2:
        resolution:
            {
                integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
            }
        engines: { node: ">=12" }

    string.prototype.includes@2.0.1:
        resolution:
            {
                integrity: sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==,
            }
        engines: { node: ">= 0.4" }

    string.prototype.matchall@4.0.12:
        resolution:
            {
                integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==,
            }
        engines: { node: ">= 0.4" }

    string.prototype.repeat@1.0.0:
        resolution:
            {
                integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==,
            }

    string.prototype.trim@1.2.10:
        resolution:
            {
                integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==,
            }
        engines: { node: ">= 0.4" }

    string.prototype.trimend@1.0.9:
        resolution:
            {
                integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==,
            }
        engines: { node: ">= 0.4" }

    string.prototype.trimstart@1.0.8:
        resolution:
            {
                integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==,
            }
        engines: { node: ">= 0.4" }

    string_decoder@1.1.1:
        resolution:
            {
                integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==,
            }

    string_decoder@1.3.0:
        resolution:
            {
                integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==,
            }

    stringify-entities@4.0.4:
        resolution:
            {
                integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==,
            }

    strip-ansi@6.0.1:
        resolution:
            {
                integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
            }
        engines: { node: ">=8" }

    strip-ansi@7.1.0:
        resolution:
            {
                integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
            }
        engines: { node: ">=12" }

    strip-bom@3.0.0:
        resolution:
            {
                integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==,
            }
        engines: { node: ">=4" }

    strip-final-newline@2.0.0:
        resolution:
            {
                integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==,
            }
        engines: { node: ">=6" }

    strip-json-comments@3.1.1:
        resolution:
            {
                integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==,
            }
        engines: { node: ">=8" }

    style-to-object@0.4.4:
        resolution:
            {
                integrity: sha512-HYNoHZa2GorYNyqiCaBgsxvcJIn7OHq6inEga+E6Ke3m5JkoqpQbnFssk4jwe+K7AhGa2fcha4wSOf1Kn01dMg==,
            }

    sucrase@3.35.0:
        resolution:
            {
                integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
            }
        engines: { node: ">=16 || 14 >=14.17" }
        hasBin: true

    supports-color@7.2.0:
        resolution:
            {
                integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
            }
        engines: { node: ">=8" }

    supports-preserve-symlinks-flag@1.0.0:
        resolution:
            {
                integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
            }
        engines: { node: ">= 0.4" }

    tailwind-merge@2.6.0:
        resolution:
            {
                integrity: sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==,
            }

    tailwindcss-animate@1.0.7:
        resolution:
            {
                integrity: sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==,
            }
        peerDependencies:
            tailwindcss: ">=3.0.0 || insiders"

    tailwindcss@3.4.17:
        resolution:
            {
                integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==,
            }
        engines: { node: ">=14.0.0" }
        hasBin: true

    tapable@2.2.1:
        resolution:
            {
                integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==,
            }
        engines: { node: ">=6" }

    tar-fs@2.1.1:
        resolution:
            {
                integrity: sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==,
            }

    tar-stream@2.2.0:
        resolution:
            {
                integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==,
            }
        engines: { node: ">=6" }

    tar@6.2.1:
        resolution:
            {
                integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==,
            }
        engines: { node: ">=10" }

    thenify-all@1.6.0:
        resolution:
            {
                integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
            }
        engines: { node: ">=0.8" }

    thenify@3.3.1:
        resolution:
            {
                integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
            }

    through2@2.0.5:
        resolution:
            {
                integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==,
            }

    to-regex-range@5.0.1:
        resolution:
            {
                integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
            }
        engines: { node: ">=8.0" }

    toidentifier@1.0.1:
        resolution:
            {
                integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==,
            }
        engines: { node: ">=0.6" }

    toml@3.0.0:
        resolution:
            {
                integrity: sha512-y/mWCZinnvxjTKYhJ+pYxwD0mRLVvOtdS2Awbgxln6iEnt4rk0yBxeSBHkGJcPucRiG0e55mwWp+g/05rsrd6w==,
            }

    trim-lines@3.0.1:
        resolution:
            {
                integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==,
            }

    trough@2.2.0:
        resolution:
            {
                integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==,
            }

    ts-api-utils@1.4.3:
        resolution:
            {
                integrity: sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==,
            }
        engines: { node: ">=16" }
        peerDependencies:
            typescript: ">=4.2.0"

    ts-api-utils@2.0.0:
        resolution:
            {
                integrity: sha512-xCt/TOAc+EOHS1XPnijD3/yzpH6qg2xppZO1YDqGoVsNXfQfzHpOdNuXwrwOU8u4ITXJyDCTyt8w5g1sZv9ynQ==,
            }
        engines: { node: ">=18.12" }
        peerDependencies:
            typescript: ">=4.8.4"

    ts-interface-checker@0.1.13:
        resolution:
            {
                integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
            }

    tsconfck@3.1.4:
        resolution:
            {
                integrity: sha512-kdqWFGVJqe+KGYvlSO9NIaWn9jT1Ny4oKVzAJsKii5eoE9snzTJzL4+MMVOMn+fikWGFmKEylcXL710V/kIPJQ==,
            }
        engines: { node: ^18 || >=20 }
        hasBin: true
        peerDependencies:
            typescript: ^5.0.0
        peerDependenciesMeta:
            typescript:
                optional: true

    tsconfig-paths@3.15.0:
        resolution:
            {
                integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==,
            }

    tsconfig-paths@4.2.0:
        resolution:
            {
                integrity: sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==,
            }
        engines: { node: ">=6" }

    tslib@2.8.1:
        resolution:
            {
                integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==,
            }

    turbo-stream@2.4.0:
        resolution:
            {
                integrity: sha512-FHncC10WpBd2eOmGwpmQsWLDoK4cqsA/UT/GqNoaKOQnT8uzhtCbg3EoUDMvqpOSAI0S26mr0rkjzbOO6S3v1g==,
            }

    type-check@0.4.0:
        resolution:
            {
                integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==,
            }
        engines: { node: ">= 0.8.0" }

    type-is@1.6.18:
        resolution:
            {
                integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==,
            }
        engines: { node: ">= 0.6" }

    typed-array-buffer@1.0.3:
        resolution:
            {
                integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==,
            }
        engines: { node: ">= 0.4" }

    typed-array-byte-length@1.0.3:
        resolution:
            {
                integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==,
            }
        engines: { node: ">= 0.4" }

    typed-array-byte-offset@1.0.4:
        resolution:
            {
                integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==,
            }
        engines: { node: ">= 0.4" }

    typed-array-length@1.0.7:
        resolution:
            {
                integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==,
            }
        engines: { node: ">= 0.4" }

    typescript-eslint@8.19.1:
        resolution:
            {
                integrity: sha512-LKPUQpdEMVOeKluHi8md7rwLcoXHhwvWp3x+sJkMuq3gGm9yaYJtPo8sRZSblMFJ5pcOGCAak/scKf1mvZDlQw==,
            }
        engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
        peerDependencies:
            eslint: ^8.57.0 || ^9.0.0
            typescript: ">=4.8.4 <5.8.0"

    typescript@5.6.3:
        resolution:
            {
                integrity: sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==,
            }
        engines: { node: ">=14.17" }
        hasBin: true

    ufo@1.5.4:
        resolution:
            {
                integrity: sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==,
            }

    unbox-primitive@1.1.0:
        resolution:
            {
                integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==,
            }
        engines: { node: ">= 0.4" }

    undici-types@6.20.0:
        resolution:
            {
                integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==,
            }

    undici@6.21.0:
        resolution:
            {
                integrity: sha512-BUgJXc752Kou3oOIuU1i+yZZypyZRqNPW0vqoMPl8VaoalSfeR0D8/t4iAS3yirs79SSMTxTag+ZC86uswv+Cw==,
            }
        engines: { node: ">=18.17" }

    unified@10.1.2:
        resolution:
            {
                integrity: sha512-pUSWAi/RAnVy1Pif2kAoeWNBa3JVrx0MId2LASj8G+7AiHWoKZNTomq6LG326T68U7/e263X6fTdcXIy7XnF7Q==,
            }

    unique-filename@3.0.0:
        resolution:
            {
                integrity: sha512-afXhuC55wkAmZ0P18QsVE6kp8JaxrEokN2HGIoIVv2ijHQd419H0+6EigAFcIzXeMIkcIkNBpB3L/DXB3cTS/g==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    unique-slug@4.0.0:
        resolution:
            {
                integrity: sha512-WrcA6AyEfqDX5bWige/4NQfPZMtASNVxdmWR76WESYQVAACSgWcR6e9i0mofqqBxYFtL4oAxPIptY73/0YE1DQ==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    unist-util-generated@2.0.1:
        resolution:
            {
                integrity: sha512-qF72kLmPxAw0oN2fwpWIqbXAVyEqUzDHMsbtPvOudIlUzXYFIeQIuxXQCRCFh22B7cixvU0MG7m3MW8FTq/S+A==,
            }

    unist-util-is@5.2.1:
        resolution:
            {
                integrity: sha512-u9njyyfEh43npf1M+yGKDGVPbY/JWEemg5nH05ncKPfi+kBbKBJoTdsogMu33uhytuLlv9y0O7GH7fEdwLdLQw==,
            }

    unist-util-position-from-estree@1.1.2:
        resolution:
            {
                integrity: sha512-poZa0eXpS+/XpoQwGwl79UUdea4ol2ZuCYguVaJS4qzIOMDzbqz8a3erUCOmubSZkaOuGamb3tX790iwOIROww==,
            }

    unist-util-position@4.0.4:
        resolution:
            {
                integrity: sha512-kUBE91efOWfIVBo8xzh/uZQ7p9ffYRtUbMRZBNFYwf0RK8koUMx6dGUfwylLOKmaT2cs4wSW96QoYUSXAyEtpg==,
            }

    unist-util-remove-position@4.0.2:
        resolution:
            {
                integrity: sha512-TkBb0HABNmxzAcfLf4qsIbFbaPDvMO6wa3b3j4VcEzFVaw1LBKwnW4/sRJ/atSLSzoIg41JWEdnE7N6DIhGDGQ==,
            }

    unist-util-stringify-position@3.0.3:
        resolution:
            {
                integrity: sha512-k5GzIBZ/QatR8N5X2y+drfpWG8IDBzdnVj6OInRNWm1oXrzydiaAT2OQiA8DPRRZyAKb9b6I2a6PxYklZD0gKg==,
            }

    unist-util-visit-parents@5.1.3:
        resolution:
            {
                integrity: sha512-x6+y8g7wWMyQhL1iZfhIPhDAs7Xwbn9nRosDXl7qoPTSCy0yNxnKc+hWokFifWQIDGi154rdUqKvbCa4+1kLhg==,
            }

    unist-util-visit@4.1.2:
        resolution:
            {
                integrity: sha512-MSd8OUGISqHdVvfY9TPhyK2VdUrPgxkUtWSuMHF6XAAFuL4LokseigBnZtPnJMu+FbynTkFNnFlyjxpVKujMRg==,
            }

    universalify@2.0.1:
        resolution:
            {
                integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==,
            }
        engines: { node: ">= 10.0.0" }

    unpipe@1.0.0:
        resolution:
            {
                integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==,
            }
        engines: { node: ">= 0.8" }

    update-browserslist-db@1.1.1:
        resolution:
            {
                integrity: sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==,
            }
        hasBin: true
        peerDependencies:
            browserslist: ">= 4.21.0"

    uri-js@4.4.1:
        resolution:
            {
                integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==,
            }

    use-callback-ref@1.3.3:
        resolution:
            {
                integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    use-composed-ref@1.4.0:
        resolution:
            {
                integrity: sha512-djviaxuOOh7wkj0paeO1Q/4wMZ8Zrnag5H6yBvzN7AKKe8beOaED9SF5/ByLqsku8NP4zQqsvM2u3ew/tJK8/w==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            "@types/react":
                optional: true

    use-isomorphic-layout-effect@1.2.0:
        resolution:
            {
                integrity: sha512-q6ayo8DWoPZT0VdG4u3D3uxcgONP3Mevx2i2b0434cwWBoL+aelL1DzkXI6w3PhTZzUeR2kaVlZn70iCiseP6w==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            "@types/react":
                optional: true

    use-latest@1.3.0:
        resolution:
            {
                integrity: sha512-mhg3xdm9NaM8q+gLT8KryJPnRFOz1/5XPBhmDEVZK1webPzDjrPk7f/mbpeLqTgB9msytYWANxgALOCJKnLvcQ==,
            }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
        peerDependenciesMeta:
            "@types/react":
                optional: true

    use-sidecar@1.1.3:
        resolution:
            {
                integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==,
            }
        engines: { node: ">=10" }
        peerDependencies:
            "@types/react": "*"
            react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
        peerDependenciesMeta:
            "@types/react":
                optional: true

    use-sound@4.0.3:
        resolution:
            {
                integrity: sha512-L205pEUFIrLsGYsCUKHQVCt0ajs//YQOFbEQeNwaWaqQj3y3st4SuR+rvpMHLmv8hgTcfUFlvMQawZNI3OE18w==,
            }
        peerDependencies:
            react: ">=16.8"

    util-deprecate@1.0.2:
        resolution:
            {
                integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
            }

    util@0.12.5:
        resolution:
            {
                integrity: sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==,
            }

    utils-merge@1.0.1:
        resolution:
            {
                integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==,
            }
        engines: { node: ">= 0.4.0" }

    uvu@0.5.6:
        resolution:
            {
                integrity: sha512-+g8ENReyr8YsOc6fv/NVJs2vFdHBnBNdfE49rshrTzDWOlUx4Gq7KOS2GD8eqhy2j+Ejq29+SbKH8yjkAqXqoA==,
            }
        engines: { node: ">=8" }
        hasBin: true

    valibot@0.41.0:
        resolution:
            {
                integrity: sha512-igDBb8CTYr8YTQlOKgaN9nSS0Be7z+WRuaeYqGf3Cjz3aKmSnqEmYnkfVjzIuumGqfHpa3fLIvMEAfhrpqN8ng==,
            }
        peerDependencies:
            typescript: ">=5"
        peerDependenciesMeta:
            typescript:
                optional: true

    validate-npm-package-license@3.0.4:
        resolution:
            {
                integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==,
            }

    validate-npm-package-name@5.0.1:
        resolution:
            {
                integrity: sha512-OljLrQ9SQdOUqTaQxqL5dEfZWrXExyyWsozYlAWFawPVNuD83igl7uJD2RTkNMbniIYgt8l81eCJGIdQF7avLQ==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

    vary@1.1.2:
        resolution:
            {
                integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==,
            }
        engines: { node: ">= 0.8" }

    vfile-message@3.1.4:
        resolution:
            {
                integrity: sha512-fa0Z6P8HUrQN4BZaX05SIVXic+7kE3b05PWAtPuYP9QLHsLKYR7/AlLW3NtOrpXRLeawpDLMsVkmk5DG0NXgWw==,
            }

    vfile@5.3.7:
        resolution:
            {
                integrity: sha512-r7qlzkgErKjobAmyNIkkSpizsFPYiUPuJb5pNW1RB4JcYVZhs4lIbVqk8XPk033CV/1z8ss5pkax8SuhGpcG8g==,
            }

    vite-node@1.6.0:
        resolution:
            {
                integrity: sha512-de6HJgzC+TFzOu0NTC4RAIsyf/DY/ibWDYQUcuEA84EMHhcefTUGkjFHKKEJhQN4A+6I0u++kr3l36ZF2d7XRw==,
            }
        engines: { node: ^18.0.0 || >=20.0.0 }
        hasBin: true

    vite-tsconfig-paths@4.3.2:
        resolution:
            {
                integrity: sha512-0Vd/a6po6Q+86rPlntHye7F31zA2URZMbH8M3saAZ/xR9QoGN/L21bxEGfXdWmFdNkqPpRdxFT7nmNe12e9/uA==,
            }
        peerDependencies:
            vite: "*"
        peerDependenciesMeta:
            vite:
                optional: true

    vite@5.4.11:
        resolution:
            {
                integrity: sha512-c7jFQRklXua0mTzneGW9QVyxFjUgwcihC4bXEtujIo2ouWCe1Ajt/amn2PCxYnhYfd5k09JX3SB7OYWFKYqj8Q==,
            }
        engines: { node: ^18.0.0 || >=20.0.0 }
        hasBin: true
        peerDependencies:
            "@types/node": ^18.0.0 || >=20.0.0
            less: "*"
            lightningcss: ^1.21.0
            sass: "*"
            sass-embedded: "*"
            stylus: "*"
            sugarss: "*"
            terser: ^5.4.0
        peerDependenciesMeta:
            "@types/node":
                optional: true
            less:
                optional: true
            lightningcss:
                optional: true
            sass:
                optional: true
            sass-embedded:
                optional: true
            stylus:
                optional: true
            sugarss:
                optional: true
            terser:
                optional: true

    vite@6.0.7:
        resolution:
            {
                integrity: sha512-RDt8r/7qx9940f8FcOIAH9PTViRrghKaK2K1jY3RaAURrEUbm9Du1mJ72G+jlhtG3WwodnfzY8ORQZbBavZEAQ==,
            }
        engines: { node: ^18.0.0 || ^20.0.0 || >=22.0.0 }
        hasBin: true
        peerDependencies:
            "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
            jiti: ">=1.21.0"
            less: "*"
            lightningcss: ^1.21.0
            sass: "*"
            sass-embedded: "*"
            stylus: "*"
            sugarss: "*"
            terser: ^5.16.0
            tsx: ^4.8.1
            yaml: ^2.4.2
        peerDependenciesMeta:
            "@types/node":
                optional: true
            jiti:
                optional: true
            less:
                optional: true
            lightningcss:
                optional: true
            sass:
                optional: true
            sass-embedded:
                optional: true
            stylus:
                optional: true
            sugarss:
                optional: true
            terser:
                optional: true
            tsx:
                optional: true
            yaml:
                optional: true

    wcwidth@1.0.1:
        resolution:
            {
                integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==,
            }

    web-encoding@1.1.5:
        resolution:
            {
                integrity: sha512-HYLeVCdJ0+lBYV2FvNZmv3HJ2Nt0QYXqZojk3d9FJOLkwnuhzM9tmamh8d7HPM8QqjKH8DeHkFTx+CFlWpZZDA==,
            }

    web-streams-polyfill@3.3.3:
        resolution:
            {
                integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==,
            }
        engines: { node: ">= 8" }

    which-boxed-primitive@1.1.1:
        resolution:
            {
                integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==,
            }
        engines: { node: ">= 0.4" }

    which-builtin-type@1.2.1:
        resolution:
            {
                integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==,
            }
        engines: { node: ">= 0.4" }

    which-collection@1.0.2:
        resolution:
            {
                integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==,
            }
        engines: { node: ">= 0.4" }

    which-typed-array@1.1.18:
        resolution:
            {
                integrity: sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==,
            }
        engines: { node: ">= 0.4" }

    which@2.0.2:
        resolution:
            {
                integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
            }
        engines: { node: ">= 8" }
        hasBin: true

    which@3.0.1:
        resolution:
            {
                integrity: sha512-XA1b62dzQzLfaEOSQFTCOd5KFf/1VSzZo7/7TUjnya6u0vGGKzU96UQBZTAThCb2j4/xjBAyii1OhRLJEivHvg==,
            }
        engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }
        hasBin: true

    word-wrap@1.2.5:
        resolution:
            {
                integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==,
            }
        engines: { node: ">=0.10.0" }

    wrap-ansi@7.0.0:
        resolution:
            {
                integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
            }
        engines: { node: ">=10" }

    wrap-ansi@8.1.0:
        resolution:
            {
                integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
            }
        engines: { node: ">=12" }

    wrappy@1.0.2:
        resolution:
            {
                integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
            }

    ws@7.5.10:
        resolution:
            {
                integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==,
            }
        engines: { node: ">=8.3.0" }
        peerDependencies:
            bufferutil: ^4.0.1
            utf-8-validate: ^5.0.2
        peerDependenciesMeta:
            bufferutil:
                optional: true
            utf-8-validate:
                optional: true

    xtend@4.0.2:
        resolution:
            {
                integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==,
            }
        engines: { node: ">=0.4" }

    yallist@3.1.1:
        resolution:
            {
                integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==,
            }

    yallist@4.0.0:
        resolution:
            {
                integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==,
            }

    yaml@2.7.0:
        resolution:
            {
                integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==,
            }
        engines: { node: ">= 14" }
        hasBin: true

    yocto-queue@0.1.0:
        resolution:
            {
                integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==,
            }
        engines: { node: ">=10" }

    zod-to-json-schema@3.24.1:
        resolution:
            {
                integrity: sha512-3h08nf3Vw3Wl3PK+q3ow/lIil81IT2Oa7YpQyUUDsEWbXveMesdfK1xBd2RhCkynwZndAxixji/7SYJJowr62w==,
            }
        peerDependencies:
            zod: ^3.24.1

    zod@3.24.1:
        resolution:
            {
                integrity: sha512-muH7gBL9sI1nciMZV67X5fTKKBLtwpZ5VBp1vsOQzj1MhrBZ4wlVCm3gedKZWLp0Oyel8sIGfeiz54Su+OVT+A==,
            }

    zustand@5.0.3:
        resolution:
            {
                integrity: sha512-14fwWQtU3pH4dE0dOpdMiWjddcH+QzKIgk1cl8epwSE7yag43k/AD/m4L6+K7DytAOr9gGBe3/EXj9g7cdostg==,
            }
        engines: { node: ">=12.20.0" }
        peerDependencies:
            "@types/react": ">=18.0.0"
            immer: ">=9.0.6"
            react: ">=18.0.0"
            use-sync-external-store: ">=1.2.0"
        peerDependenciesMeta:
            "@types/react":
                optional: true
            immer:
                optional: true
            react:
                optional: true
            use-sync-external-store:
                optional: true

    zwitch@2.0.4:
        resolution:
            {
                integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==,
            }

snapshots:
    "@ai-sdk/provider@1.0.6":
        dependencies:
            json-schema: 0.4.0

    "@alloc/quick-lru@5.2.0": {}

    "@ampproject/remapping@2.3.0":
        dependencies:
            "@jridgewell/gen-mapping": 0.3.8
            "@jridgewell/trace-mapping": 0.3.25

    "@assistant-ui/react@0.7.33(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(tailwindcss@3.4.17)":
        dependencies:
            "@ai-sdk/provider": 1.0.6
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-avatar": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-dialog": 1.1.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-popover": 1.1.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-slot": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-tooltip": 1.1.6(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-escape-keydown": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            class-variance-authority: 0.7.1
            classnames: 2.5.1
            json-schema: 0.4.0
            lucide-react: 0.469.0(react@18.3.1)
            nanoid: 5.0.9
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
            react-textarea-autosize: 8.5.6(@types/react@18.3.18)(react@18.3.1)
            secure-json-parse: 3.0.2
            zod: 3.24.1
            zod-to-json-schema: 3.24.1(zod@3.24.1)
            zustand: 5.0.3(@types/react@18.3.18)(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)
            tailwindcss: 3.4.17
        transitivePeerDependencies:
            - immer
            - use-sync-external-store

    "@babel/code-frame@7.26.2":
        dependencies:
            "@babel/helper-validator-identifier": 7.25.9
            js-tokens: 4.0.0
            picocolors: 1.1.1

    "@babel/compat-data@7.26.3": {}

    "@babel/core@7.26.0":
        dependencies:
            "@ampproject/remapping": 2.3.0
            "@babel/code-frame": 7.26.2
            "@babel/generator": 7.26.3
            "@babel/helper-compilation-targets": 7.25.9
            "@babel/helper-module-transforms": 7.26.0(@babel/core@7.26.0)
            "@babel/helpers": 7.26.0
            "@babel/parser": 7.26.3
            "@babel/template": 7.25.9
            "@babel/traverse": 7.26.4
            "@babel/types": 7.26.3
            convert-source-map: 2.0.0
            debug: 4.4.0
            gensync: 1.0.0-beta.2
            json5: 2.2.3
            semver: 6.3.1
        transitivePeerDependencies:
            - supports-color

    "@babel/generator@7.26.3":
        dependencies:
            "@babel/parser": 7.26.3
            "@babel/types": 7.26.3
            "@jridgewell/gen-mapping": 0.3.8
            "@jridgewell/trace-mapping": 0.3.25
            jsesc: 3.0.2

    "@babel/helper-annotate-as-pure@7.25.9":
        dependencies:
            "@babel/types": 7.26.3

    "@babel/helper-compilation-targets@7.25.9":
        dependencies:
            "@babel/compat-data": 7.26.3
            "@babel/helper-validator-option": 7.25.9
            browserslist: 4.24.3
            lru-cache: 5.1.1
            semver: 6.3.1

    "@babel/helper-create-class-features-plugin@7.25.9(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-annotate-as-pure": 7.25.9
            "@babel/helper-member-expression-to-functions": 7.25.9
            "@babel/helper-optimise-call-expression": 7.25.9
            "@babel/helper-replace-supers": 7.25.9(@babel/core@7.26.0)
            "@babel/helper-skip-transparent-expression-wrappers": 7.25.9
            "@babel/traverse": 7.26.4
            semver: 6.3.1
        transitivePeerDependencies:
            - supports-color

    "@babel/helper-member-expression-to-functions@7.25.9":
        dependencies:
            "@babel/traverse": 7.26.4
            "@babel/types": 7.26.3
        transitivePeerDependencies:
            - supports-color

    "@babel/helper-module-imports@7.25.9":
        dependencies:
            "@babel/traverse": 7.26.4
            "@babel/types": 7.26.3
        transitivePeerDependencies:
            - supports-color

    "@babel/helper-module-transforms@7.26.0(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-module-imports": 7.25.9
            "@babel/helper-validator-identifier": 7.25.9
            "@babel/traverse": 7.26.4
        transitivePeerDependencies:
            - supports-color

    "@babel/helper-optimise-call-expression@7.25.9":
        dependencies:
            "@babel/types": 7.26.3

    "@babel/helper-plugin-utils@7.25.9": {}

    "@babel/helper-replace-supers@7.25.9(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-member-expression-to-functions": 7.25.9
            "@babel/helper-optimise-call-expression": 7.25.9
            "@babel/traverse": 7.26.4
        transitivePeerDependencies:
            - supports-color

    "@babel/helper-skip-transparent-expression-wrappers@7.25.9":
        dependencies:
            "@babel/traverse": 7.26.4
            "@babel/types": 7.26.3
        transitivePeerDependencies:
            - supports-color

    "@babel/helper-string-parser@7.25.9": {}

    "@babel/helper-validator-identifier@7.25.9": {}

    "@babel/helper-validator-option@7.25.9": {}

    "@babel/helpers@7.26.0":
        dependencies:
            "@babel/template": 7.25.9
            "@babel/types": 7.26.3

    "@babel/parser@7.26.3":
        dependencies:
            "@babel/types": 7.26.3

    "@babel/plugin-syntax-decorators@7.25.9(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-plugin-utils": 7.25.9

    "@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-plugin-utils": 7.25.9

    "@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-plugin-utils": 7.25.9

    "@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-module-transforms": 7.26.0(@babel/core@7.26.0)
            "@babel/helper-plugin-utils": 7.25.9
        transitivePeerDependencies:
            - supports-color

    "@babel/plugin-transform-typescript@7.26.3(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-annotate-as-pure": 7.25.9
            "@babel/helper-create-class-features-plugin": 7.25.9(@babel/core@7.26.0)
            "@babel/helper-plugin-utils": 7.25.9
            "@babel/helper-skip-transparent-expression-wrappers": 7.25.9
            "@babel/plugin-syntax-typescript": 7.25.9(@babel/core@7.26.0)
        transitivePeerDependencies:
            - supports-color

    "@babel/preset-typescript@7.26.0(@babel/core@7.26.0)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/helper-plugin-utils": 7.25.9
            "@babel/helper-validator-option": 7.25.9
            "@babel/plugin-syntax-jsx": 7.25.9(@babel/core@7.26.0)
            "@babel/plugin-transform-modules-commonjs": 7.26.3(@babel/core@7.26.0)
            "@babel/plugin-transform-typescript": 7.26.3(@babel/core@7.26.0)
        transitivePeerDependencies:
            - supports-color

    "@babel/runtime@7.26.0":
        dependencies:
            regenerator-runtime: 0.14.1

    "@babel/template@7.25.9":
        dependencies:
            "@babel/code-frame": 7.26.2
            "@babel/parser": 7.26.3
            "@babel/types": 7.26.3

    "@babel/traverse@7.26.4":
        dependencies:
            "@babel/code-frame": 7.26.2
            "@babel/generator": 7.26.3
            "@babel/parser": 7.26.3
            "@babel/template": 7.25.9
            "@babel/types": 7.26.3
            debug: 4.4.0
            globals: 11.12.0
        transitivePeerDependencies:
            - supports-color

    "@babel/types@7.26.3":
        dependencies:
            "@babel/helper-string-parser": 7.25.9
            "@babel/helper-validator-identifier": 7.25.9

    "@emotion/hash@0.9.2": {}

    "@esbuild/aix-ppc64@0.21.5":
        optional: true

    "@esbuild/aix-ppc64@0.24.2":
        optional: true

    "@esbuild/android-arm64@0.17.6":
        optional: true

    "@esbuild/android-arm64@0.21.5":
        optional: true

    "@esbuild/android-arm64@0.24.2":
        optional: true

    "@esbuild/android-arm@0.17.6":
        optional: true

    "@esbuild/android-arm@0.21.5":
        optional: true

    "@esbuild/android-arm@0.24.2":
        optional: true

    "@esbuild/android-x64@0.17.6":
        optional: true

    "@esbuild/android-x64@0.21.5":
        optional: true

    "@esbuild/android-x64@0.24.2":
        optional: true

    "@esbuild/darwin-arm64@0.17.6":
        optional: true

    "@esbuild/darwin-arm64@0.21.5":
        optional: true

    "@esbuild/darwin-arm64@0.24.2":
        optional: true

    "@esbuild/darwin-x64@0.17.6":
        optional: true

    "@esbuild/darwin-x64@0.21.5":
        optional: true

    "@esbuild/darwin-x64@0.24.2":
        optional: true

    "@esbuild/freebsd-arm64@0.17.6":
        optional: true

    "@esbuild/freebsd-arm64@0.21.5":
        optional: true

    "@esbuild/freebsd-arm64@0.24.2":
        optional: true

    "@esbuild/freebsd-x64@0.17.6":
        optional: true

    "@esbuild/freebsd-x64@0.21.5":
        optional: true

    "@esbuild/freebsd-x64@0.24.2":
        optional: true

    "@esbuild/linux-arm64@0.17.6":
        optional: true

    "@esbuild/linux-arm64@0.21.5":
        optional: true

    "@esbuild/linux-arm64@0.24.2":
        optional: true

    "@esbuild/linux-arm@0.17.6":
        optional: true

    "@esbuild/linux-arm@0.21.5":
        optional: true

    "@esbuild/linux-arm@0.24.2":
        optional: true

    "@esbuild/linux-ia32@0.17.6":
        optional: true

    "@esbuild/linux-ia32@0.21.5":
        optional: true

    "@esbuild/linux-ia32@0.24.2":
        optional: true

    "@esbuild/linux-loong64@0.17.6":
        optional: true

    "@esbuild/linux-loong64@0.21.5":
        optional: true

    "@esbuild/linux-loong64@0.24.2":
        optional: true

    "@esbuild/linux-mips64el@0.17.6":
        optional: true

    "@esbuild/linux-mips64el@0.21.5":
        optional: true

    "@esbuild/linux-mips64el@0.24.2":
        optional: true

    "@esbuild/linux-ppc64@0.17.6":
        optional: true

    "@esbuild/linux-ppc64@0.21.5":
        optional: true

    "@esbuild/linux-ppc64@0.24.2":
        optional: true

    "@esbuild/linux-riscv64@0.17.6":
        optional: true

    "@esbuild/linux-riscv64@0.21.5":
        optional: true

    "@esbuild/linux-riscv64@0.24.2":
        optional: true

    "@esbuild/linux-s390x@0.17.6":
        optional: true

    "@esbuild/linux-s390x@0.21.5":
        optional: true

    "@esbuild/linux-s390x@0.24.2":
        optional: true

    "@esbuild/linux-x64@0.17.6":
        optional: true

    "@esbuild/linux-x64@0.21.5":
        optional: true

    "@esbuild/linux-x64@0.24.2":
        optional: true

    "@esbuild/netbsd-arm64@0.24.2":
        optional: true

    "@esbuild/netbsd-x64@0.17.6":
        optional: true

    "@esbuild/netbsd-x64@0.21.5":
        optional: true

    "@esbuild/netbsd-x64@0.24.2":
        optional: true

    "@esbuild/openbsd-arm64@0.24.2":
        optional: true

    "@esbuild/openbsd-x64@0.17.6":
        optional: true

    "@esbuild/openbsd-x64@0.21.5":
        optional: true

    "@esbuild/openbsd-x64@0.24.2":
        optional: true

    "@esbuild/sunos-x64@0.17.6":
        optional: true

    "@esbuild/sunos-x64@0.21.5":
        optional: true

    "@esbuild/sunos-x64@0.24.2":
        optional: true

    "@esbuild/win32-arm64@0.17.6":
        optional: true

    "@esbuild/win32-arm64@0.21.5":
        optional: true

    "@esbuild/win32-arm64@0.24.2":
        optional: true

    "@esbuild/win32-ia32@0.17.6":
        optional: true

    "@esbuild/win32-ia32@0.21.5":
        optional: true

    "@esbuild/win32-ia32@0.24.2":
        optional: true

    "@esbuild/win32-x64@0.17.6":
        optional: true

    "@esbuild/win32-x64@0.21.5":
        optional: true

    "@esbuild/win32-x64@0.24.2":
        optional: true

    "@eslint-community/eslint-utils@4.4.1(eslint@9.17.0(jiti@1.21.7))":
        dependencies:
            eslint: 9.17.0(jiti@1.21.7)
            eslint-visitor-keys: 3.4.3

    "@eslint-community/regexpp@4.12.1": {}

    "@eslint/config-array@0.19.1":
        dependencies:
            "@eslint/object-schema": 2.1.5
            debug: 4.4.0
            minimatch: 3.1.2
        transitivePeerDependencies:
            - supports-color

    "@eslint/core@0.9.1":
        dependencies:
            "@types/json-schema": 7.0.15

    "@eslint/eslintrc@3.2.0":
        dependencies:
            ajv: 6.12.6
            debug: 4.4.0
            espree: 10.3.0
            globals: 14.0.0
            ignore: 5.3.2
            import-fresh: 3.3.0
            js-yaml: 4.1.0
            minimatch: 3.1.2
            strip-json-comments: 3.1.1
        transitivePeerDependencies:
            - supports-color

    "@eslint/js@9.17.0": {}

    "@eslint/object-schema@2.1.5": {}

    "@eslint/plugin-kit@0.2.4":
        dependencies:
            levn: 0.4.1

    "@floating-ui/core@1.6.9":
        dependencies:
            "@floating-ui/utils": 0.2.9

    "@floating-ui/dom@1.6.13":
        dependencies:
            "@floating-ui/core": 1.6.9
            "@floating-ui/utils": 0.2.9

    "@floating-ui/react-dom@2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@floating-ui/dom": 1.6.13
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)

    "@floating-ui/utils@0.2.9": {}

    "@humanfs/core@0.19.1": {}

    "@humanfs/node@0.16.6":
        dependencies:
            "@humanfs/core": 0.19.1
            "@humanwhocodes/retry": 0.3.1

    "@humanwhocodes/module-importer@1.0.1": {}

    "@humanwhocodes/retry@0.3.1": {}

    "@humanwhocodes/retry@0.4.1": {}

    "@isaacs/cliui@8.0.2":
        dependencies:
            string-width: 5.1.2
            string-width-cjs: string-width@4.2.3
            strip-ansi: 7.1.0
            strip-ansi-cjs: strip-ansi@6.0.1
            wrap-ansi: 8.1.0
            wrap-ansi-cjs: wrap-ansi@7.0.0

    "@jridgewell/gen-mapping@0.3.8":
        dependencies:
            "@jridgewell/set-array": 1.2.1
            "@jridgewell/sourcemap-codec": 1.5.0
            "@jridgewell/trace-mapping": 0.3.25

    "@jridgewell/resolve-uri@3.1.2": {}

    "@jridgewell/set-array@1.2.1": {}

    "@jridgewell/sourcemap-codec@1.5.0": {}

    "@jridgewell/trace-mapping@0.3.25":
        dependencies:
            "@jridgewell/resolve-uri": 3.1.2
            "@jridgewell/sourcemap-codec": 1.5.0

    "@jspm/core@2.0.1": {}

    "@mdx-js/mdx@2.3.0":
        dependencies:
            "@types/estree-jsx": 1.0.5
            "@types/mdx": 2.0.13
            estree-util-build-jsx: 2.2.2
            estree-util-is-identifier-name: 2.1.0
            estree-util-to-js: 1.2.0
            estree-walker: 3.0.3
            hast-util-to-estree: 2.3.3
            markdown-extensions: 1.1.1
            periscopic: 3.1.0
            remark-mdx: 2.3.0
            remark-parse: 10.0.2
            remark-rehype: 10.1.0
            unified: 10.1.2
            unist-util-position-from-estree: 1.1.2
            unist-util-stringify-position: 3.0.3
            unist-util-visit: 4.1.2
            vfile: 5.3.7
        transitivePeerDependencies:
            - supports-color

    "@nodelib/fs.scandir@2.1.5":
        dependencies:
            "@nodelib/fs.stat": 2.0.5
            run-parallel: 1.2.0

    "@nodelib/fs.stat@2.0.5": {}

    "@nodelib/fs.walk@1.2.8":
        dependencies:
            "@nodelib/fs.scandir": 2.1.5
            fastq: 1.18.0

    "@nolyfill/is-core-module@1.0.39": {}

    "@npmcli/fs@3.1.1":
        dependencies:
            semver: 7.6.3

    "@npmcli/git@4.1.0":
        dependencies:
            "@npmcli/promise-spawn": 6.0.2
            lru-cache: 7.18.3
            npm-pick-manifest: 8.0.2
            proc-log: 3.0.0
            promise-inflight: 1.0.1
            promise-retry: 2.0.1
            semver: 7.6.3
            which: 3.0.1
        transitivePeerDependencies:
            - bluebird

    "@npmcli/package-json@4.0.1":
        dependencies:
            "@npmcli/git": 4.1.0
            glob: 10.4.5
            hosted-git-info: 6.1.3
            json-parse-even-better-errors: 3.0.2
            normalize-package-data: 5.0.0
            proc-log: 3.0.0
            semver: 7.6.3
        transitivePeerDependencies:
            - bluebird

    "@npmcli/promise-spawn@6.0.2":
        dependencies:
            which: 3.0.1

    "@pkgjs/parseargs@0.11.0":
        optional: true

    "@radix-ui/primitive@1.1.1": {}

    "@radix-ui/react-arrow@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-avatar@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-collapsible@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-id": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-presence": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-collection@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-slot": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-compose-refs@1.1.1(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-context@1.1.1(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-dialog@1.1.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-dismissable-layer": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-focus-guards": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-focus-scope": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-id": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-portal": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-presence": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-slot": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            aria-hidden: 1.2.4
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
            react-remove-scroll: 2.6.2(@types/react@18.3.18)(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-direction@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-dismissable-layer@1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-escape-keydown": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-focus-guards@1.1.1(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-focus-scope@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-id@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-label@2.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-popover@1.1.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-dismissable-layer": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-focus-guards": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-focus-scope": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-id": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-popper": 1.2.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-portal": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-presence": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-slot": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            aria-hidden: 1.2.4
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
            react-remove-scroll: 2.6.2(@types/react@18.3.18)(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-popper@1.2.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@floating-ui/react-dom": 2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-arrow": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-rect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-size": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/rect": 1.1.0
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-portal@1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-presence@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-primitive@2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-slot": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-roving-focus@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-collection": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-direction": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-id": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-separator@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-slot@1.1.1(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-tabs@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-direction": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-id": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-presence": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-roving-focus": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-toast@1.2.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-collection": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-dismissable-layer": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-portal": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-presence": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-visually-hidden": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-tooltip@1.1.6(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/primitive": 1.1.1
            "@radix-ui/react-compose-refs": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-context": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-dismissable-layer": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-id": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-popper": 1.2.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-portal": 1.1.3(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-presence": 1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            "@radix-ui/react-slot": 1.1.1(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-use-controllable-state": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            "@radix-ui/react-visually-hidden": 1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            "@radix-ui/react-use-callback-ref": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-use-rect@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            "@radix-ui/rect": 1.1.0
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-use-size@1.1.0(@types/react@18.3.18)(react@18.3.1)":
        dependencies:
            "@radix-ui/react-use-layout-effect": 1.1.0(@types/react@18.3.18)(react@18.3.1)
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    "@radix-ui/react-visually-hidden@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
        dependencies:
            "@radix-ui/react-primitive": 2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18
            "@types/react-dom": 18.3.5(@types/react@18.3.18)

    "@radix-ui/rect@1.1.0": {}

    "@remix-run/dev@2.15.2(@remix-run/react@2.15.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.6.3))(@remix-run/serve@2.15.2(typescript@5.6.3))(@types/node@22.10.5)(typescript@5.6.3)(vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0))":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/generator": 7.26.3
            "@babel/parser": 7.26.3
            "@babel/plugin-syntax-decorators": 7.25.9(@babel/core@7.26.0)
            "@babel/plugin-syntax-jsx": 7.25.9(@babel/core@7.26.0)
            "@babel/preset-typescript": 7.26.0(@babel/core@7.26.0)
            "@babel/traverse": 7.26.4
            "@babel/types": 7.26.3
            "@mdx-js/mdx": 2.3.0
            "@npmcli/package-json": 4.0.1
            "@remix-run/node": 2.15.2(typescript@5.6.3)
            "@remix-run/react": 2.15.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.6.3)
            "@remix-run/router": 1.21.0
            "@remix-run/server-runtime": 2.15.2(typescript@5.6.3)
            "@types/mdx": 2.0.13
            "@vanilla-extract/integration": 6.5.0(@types/node@22.10.5)
            arg: 5.0.2
            cacache: 17.1.4
            chalk: 4.1.2
            chokidar: 3.6.0
            cross-spawn: 7.0.6
            dotenv: 16.4.7
            es-module-lexer: 1.6.0
            esbuild: 0.17.6
            esbuild-plugins-node-modules-polyfill: 1.6.8(esbuild@0.17.6)
            execa: 5.1.1
            exit-hook: 2.2.1
            express: 4.21.2
            fs-extra: 10.1.0
            get-port: 5.1.1
            gunzip-maybe: 1.4.2
            jsesc: 3.0.2
            json5: 2.2.3
            lodash: 4.17.21
            lodash.debounce: 4.0.8
            minimatch: 9.0.5
            ora: 5.4.1
            picocolors: 1.1.1
            picomatch: 2.3.1
            pidtree: 0.6.0
            postcss: 8.4.49
            postcss-discard-duplicates: 5.1.0(postcss@8.4.49)
            postcss-load-config: 4.0.2(postcss@8.4.49)
            postcss-modules: 6.0.1(postcss@8.4.49)
            prettier: 2.8.8
            pretty-ms: 7.0.1
            react-refresh: 0.14.2
            remark-frontmatter: 4.0.1
            remark-mdx-frontmatter: 1.1.1
            semver: 7.6.3
            set-cookie-parser: 2.7.1
            tar-fs: 2.1.1
            tsconfig-paths: 4.2.0
            valibot: 0.41.0(typescript@5.6.3)
            vite-node: 1.6.0(@types/node@22.10.5)
            ws: 7.5.10
        optionalDependencies:
            "@remix-run/serve": 2.15.2(typescript@5.6.3)
            typescript: 5.6.3
            vite: 6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0)
        transitivePeerDependencies:
            - "@types/node"
            - babel-plugin-macros
            - bluebird
            - bufferutil
            - less
            - lightningcss
            - sass
            - sass-embedded
            - stylus
            - sugarss
            - supports-color
            - terser
            - ts-node
            - utf-8-validate

    "@remix-run/express@2.15.2(express@4.21.2)(typescript@5.6.3)":
        dependencies:
            "@remix-run/node": 2.15.2(typescript@5.6.3)
            express: 4.21.2
        optionalDependencies:
            typescript: 5.6.3

    "@remix-run/node@2.15.2(typescript@5.6.3)":
        dependencies:
            "@remix-run/server-runtime": 2.15.2(typescript@5.6.3)
            "@remix-run/web-fetch": 4.4.2
            "@web3-storage/multipart-parser": 1.0.0
            cookie-signature: 1.2.2
            source-map-support: 0.5.21
            stream-slice: 0.1.2
            undici: 6.21.0
        optionalDependencies:
            typescript: 5.6.3

    "@remix-run/react@2.15.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.6.3)":
        dependencies:
            "@remix-run/router": 1.21.0
            "@remix-run/server-runtime": 2.15.2(typescript@5.6.3)
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
            react-router: 6.28.1(react@18.3.1)
            react-router-dom: 6.28.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
            turbo-stream: 2.4.0
        optionalDependencies:
            typescript: 5.6.3

    "@remix-run/router@1.21.0": {}

    "@remix-run/serve@2.15.2(typescript@5.6.3)":
        dependencies:
            "@remix-run/express": 2.15.2(express@4.21.2)(typescript@5.6.3)
            "@remix-run/node": 2.15.2(typescript@5.6.3)
            chokidar: 3.6.0
            compression: 1.7.5
            express: 4.21.2
            get-port: 5.1.1
            morgan: 1.10.0
            source-map-support: 0.5.21
        transitivePeerDependencies:
            - supports-color
            - typescript

    "@remix-run/server-runtime@2.15.2(typescript@5.6.3)":
        dependencies:
            "@remix-run/router": 1.21.0
            "@types/cookie": 0.6.0
            "@web3-storage/multipart-parser": 1.0.0
            cookie: 0.6.0
            set-cookie-parser: 2.7.1
            source-map: 0.7.4
            turbo-stream: 2.4.0
        optionalDependencies:
            typescript: 5.6.3

    "@remix-run/web-blob@3.1.0":
        dependencies:
            "@remix-run/web-stream": 1.1.0
            web-encoding: 1.1.5

    "@remix-run/web-fetch@4.4.2":
        dependencies:
            "@remix-run/web-blob": 3.1.0
            "@remix-run/web-file": 3.1.0
            "@remix-run/web-form-data": 3.1.0
            "@remix-run/web-stream": 1.1.0
            "@web3-storage/multipart-parser": 1.0.0
            abort-controller: 3.0.0
            data-uri-to-buffer: 3.0.1
            mrmime: 1.0.1

    "@remix-run/web-file@3.1.0":
        dependencies:
            "@remix-run/web-blob": 3.1.0

    "@remix-run/web-form-data@3.1.0":
        dependencies:
            web-encoding: 1.1.5

    "@remix-run/web-stream@1.1.0":
        dependencies:
            web-streams-polyfill: 3.3.3

    "@rollup/rollup-android-arm-eabi@4.30.1":
        optional: true

    "@rollup/rollup-android-arm64@4.30.1":
        optional: true

    "@rollup/rollup-darwin-arm64@4.30.1":
        optional: true

    "@rollup/rollup-darwin-x64@4.30.1":
        optional: true

    "@rollup/rollup-freebsd-arm64@4.30.1":
        optional: true

    "@rollup/rollup-freebsd-x64@4.30.1":
        optional: true

    "@rollup/rollup-linux-arm-gnueabihf@4.30.1":
        optional: true

    "@rollup/rollup-linux-arm-musleabihf@4.30.1":
        optional: true

    "@rollup/rollup-linux-arm64-gnu@4.30.1":
        optional: true

    "@rollup/rollup-linux-arm64-musl@4.30.1":
        optional: true

    "@rollup/rollup-linux-loongarch64-gnu@4.30.1":
        optional: true

    "@rollup/rollup-linux-powerpc64le-gnu@4.30.1":
        optional: true

    "@rollup/rollup-linux-riscv64-gnu@4.30.1":
        optional: true

    "@rollup/rollup-linux-s390x-gnu@4.30.1":
        optional: true

    "@rollup/rollup-linux-x64-gnu@4.30.1":
        optional: true

    "@rollup/rollup-linux-x64-musl@4.30.1":
        optional: true

    "@rollup/rollup-win32-arm64-msvc@4.30.1":
        optional: true

    "@rollup/rollup-win32-ia32-msvc@4.30.1":
        optional: true

    "@rollup/rollup-win32-x64-msvc@4.30.1":
        optional: true

    "@rtsao/scc@1.1.0": {}

    "@swc/core-darwin-arm64@1.10.6":
        optional: true

    "@swc/core-darwin-x64@1.10.6":
        optional: true

    "@swc/core-linux-arm-gnueabihf@1.10.6":
        optional: true

    "@swc/core-linux-arm64-gnu@1.10.6":
        optional: true

    "@swc/core-linux-arm64-musl@1.10.6":
        optional: true

    "@swc/core-linux-x64-gnu@1.10.6":
        optional: true

    "@swc/core-linux-x64-musl@1.10.6":
        optional: true

    "@swc/core-win32-arm64-msvc@1.10.6":
        optional: true

    "@swc/core-win32-ia32-msvc@1.10.6":
        optional: true

    "@swc/core-win32-x64-msvc@1.10.6":
        optional: true

    "@swc/core@1.10.6":
        dependencies:
            "@swc/counter": 0.1.3
            "@swc/types": 0.1.17
        optionalDependencies:
            "@swc/core-darwin-arm64": 1.10.6
            "@swc/core-darwin-x64": 1.10.6
            "@swc/core-linux-arm-gnueabihf": 1.10.6
            "@swc/core-linux-arm64-gnu": 1.10.6
            "@swc/core-linux-arm64-musl": 1.10.6
            "@swc/core-linux-x64-gnu": 1.10.6
            "@swc/core-linux-x64-musl": 1.10.6
            "@swc/core-win32-arm64-msvc": 1.10.6
            "@swc/core-win32-ia32-msvc": 1.10.6
            "@swc/core-win32-x64-msvc": 1.10.6

    "@swc/counter@0.1.3": {}

    "@swc/types@0.1.17":
        dependencies:
            "@swc/counter": 0.1.3

    "@tanstack/query-core@5.62.16": {}

    "@tanstack/react-query@5.62.16(react@18.3.1)":
        dependencies:
            "@tanstack/query-core": 5.62.16
            react: 18.3.1

    "@types/acorn@4.0.6":
        dependencies:
            "@types/estree": 1.0.6

    "@types/cookie@0.6.0": {}

    "@types/debug@4.1.12":
        dependencies:
            "@types/ms": 0.7.34

    "@types/estree-jsx@1.0.5":
        dependencies:
            "@types/estree": 1.0.6

    "@types/estree@1.0.6": {}

    "@types/hast@2.3.10":
        dependencies:
            "@types/unist": 2.0.11

    "@types/json-schema@7.0.15": {}

    "@types/json5@0.0.29": {}

    "@types/mdast@3.0.15":
        dependencies:
            "@types/unist": 2.0.11

    "@types/mdx@2.0.13": {}

    "@types/ms@0.7.34": {}

    "@types/node@22.10.5":
        dependencies:
            undici-types: 6.20.0

    "@types/prop-types@15.7.14": {}

    "@types/react-dom@18.3.5(@types/react@18.3.18)":
        dependencies:
            "@types/react": 18.3.18

    "@types/react@18.3.18":
        dependencies:
            "@types/prop-types": 15.7.14
            csstype: 3.1.3

    "@types/semver@7.5.8": {}

    "@types/unist@2.0.11": {}

    "@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@eslint-community/regexpp": 4.12.1
            "@typescript-eslint/parser": 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/scope-manager": 6.21.0
            "@typescript-eslint/type-utils": 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/utils": 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/visitor-keys": 6.21.0
            debug: 4.4.0
            eslint: 9.17.0(jiti@1.21.7)
            graphemer: 1.4.0
            ignore: 5.3.2
            natural-compare: 1.4.0
            semver: 7.6.3
            ts-api-utils: 1.4.3(typescript@5.6.3)
        optionalDependencies:
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/eslint-plugin@8.19.1(@typescript-eslint/parser@8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@eslint-community/regexpp": 4.12.1
            "@typescript-eslint/parser": 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/scope-manager": 8.19.1
            "@typescript-eslint/type-utils": 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/utils": 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/visitor-keys": 8.19.1
            eslint: 9.17.0(jiti@1.21.7)
            graphemer: 1.4.0
            ignore: 5.3.2
            natural-compare: 1.4.0
            ts-api-utils: 2.0.0(typescript@5.6.3)
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@typescript-eslint/scope-manager": 6.21.0
            "@typescript-eslint/types": 6.21.0
            "@typescript-eslint/typescript-estree": 6.21.0(typescript@5.6.3)
            "@typescript-eslint/visitor-keys": 6.21.0
            debug: 4.4.0
            eslint: 9.17.0(jiti@1.21.7)
        optionalDependencies:
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/parser@8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@typescript-eslint/scope-manager": 8.19.1
            "@typescript-eslint/types": 8.19.1
            "@typescript-eslint/typescript-estree": 8.19.1(typescript@5.6.3)
            "@typescript-eslint/visitor-keys": 8.19.1
            debug: 4.4.0
            eslint: 9.17.0(jiti@1.21.7)
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/scope-manager@6.21.0":
        dependencies:
            "@typescript-eslint/types": 6.21.0
            "@typescript-eslint/visitor-keys": 6.21.0

    "@typescript-eslint/scope-manager@8.19.1":
        dependencies:
            "@typescript-eslint/types": 8.19.1
            "@typescript-eslint/visitor-keys": 8.19.1

    "@typescript-eslint/type-utils@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@typescript-eslint/typescript-estree": 6.21.0(typescript@5.6.3)
            "@typescript-eslint/utils": 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            debug: 4.4.0
            eslint: 9.17.0(jiti@1.21.7)
            ts-api-utils: 1.4.3(typescript@5.6.3)
        optionalDependencies:
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/type-utils@8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@typescript-eslint/typescript-estree": 8.19.1(typescript@5.6.3)
            "@typescript-eslint/utils": 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            debug: 4.4.0
            eslint: 9.17.0(jiti@1.21.7)
            ts-api-utils: 2.0.0(typescript@5.6.3)
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/types@6.21.0": {}

    "@typescript-eslint/types@8.19.1": {}

    "@typescript-eslint/typescript-estree@6.21.0(typescript@5.6.3)":
        dependencies:
            "@typescript-eslint/types": 6.21.0
            "@typescript-eslint/visitor-keys": 6.21.0
            debug: 4.4.0
            globby: 11.1.0
            is-glob: 4.0.3
            minimatch: 9.0.3
            semver: 7.6.3
            ts-api-utils: 1.4.3(typescript@5.6.3)
        optionalDependencies:
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/typescript-estree@8.19.1(typescript@5.6.3)":
        dependencies:
            "@typescript-eslint/types": 8.19.1
            "@typescript-eslint/visitor-keys": 8.19.1
            debug: 4.4.0
            fast-glob: 3.3.3
            is-glob: 4.0.3
            minimatch: 9.0.5
            semver: 7.6.3
            ts-api-utils: 2.0.0(typescript@5.6.3)
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/utils@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@eslint-community/eslint-utils": 4.4.1(eslint@9.17.0(jiti@1.21.7))
            "@types/json-schema": 7.0.15
            "@types/semver": 7.5.8
            "@typescript-eslint/scope-manager": 6.21.0
            "@typescript-eslint/types": 6.21.0
            "@typescript-eslint/typescript-estree": 6.21.0(typescript@5.6.3)
            eslint: 9.17.0(jiti@1.21.7)
            semver: 7.6.3
        transitivePeerDependencies:
            - supports-color
            - typescript

    "@typescript-eslint/utils@8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)":
        dependencies:
            "@eslint-community/eslint-utils": 4.4.1(eslint@9.17.0(jiti@1.21.7))
            "@typescript-eslint/scope-manager": 8.19.1
            "@typescript-eslint/types": 8.19.1
            "@typescript-eslint/typescript-estree": 8.19.1(typescript@5.6.3)
            eslint: 9.17.0(jiti@1.21.7)
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    "@typescript-eslint/visitor-keys@6.21.0":
        dependencies:
            "@typescript-eslint/types": 6.21.0
            eslint-visitor-keys: 3.4.3

    "@typescript-eslint/visitor-keys@8.19.1":
        dependencies:
            "@typescript-eslint/types": 8.19.1
            eslint-visitor-keys: 4.2.0

    "@vanilla-extract/babel-plugin-debug-ids@1.2.0":
        dependencies:
            "@babel/core": 7.26.0
        transitivePeerDependencies:
            - supports-color

    "@vanilla-extract/css@1.17.0":
        dependencies:
            "@emotion/hash": 0.9.2
            "@vanilla-extract/private": 1.0.6
            css-what: 6.1.0
            cssesc: 3.0.0
            csstype: 3.1.3
            dedent: 1.5.3
            deep-object-diff: 1.1.9
            deepmerge: 4.3.1
            lru-cache: 10.4.3
            media-query-parser: 2.0.2
            modern-ahocorasick: 1.1.0
            picocolors: 1.1.1
        transitivePeerDependencies:
            - babel-plugin-macros

    "@vanilla-extract/integration@6.5.0(@types/node@22.10.5)":
        dependencies:
            "@babel/core": 7.26.0
            "@babel/plugin-syntax-typescript": 7.25.9(@babel/core@7.26.0)
            "@vanilla-extract/babel-plugin-debug-ids": 1.2.0
            "@vanilla-extract/css": 1.17.0
            esbuild: 0.17.6
            eval: 0.1.8
            find-up: 5.0.0
            javascript-stringify: 2.1.0
            lodash: 4.17.21
            mlly: 1.7.3
            outdent: 0.8.0
            vite: 5.4.11(@types/node@22.10.5)
            vite-node: 1.6.0(@types/node@22.10.5)
        transitivePeerDependencies:
            - "@types/node"
            - babel-plugin-macros
            - less
            - lightningcss
            - sass
            - sass-embedded
            - stylus
            - sugarss
            - supports-color
            - terser

    "@vanilla-extract/private@1.0.6": {}

    "@vitejs/plugin-react-swc@3.7.2(vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0))":
        dependencies:
            "@swc/core": 1.10.6
            vite: 6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0)
        transitivePeerDependencies:
            - "@swc/helpers"

    "@web3-storage/multipart-parser@1.0.0": {}

    "@zxing/text-encoding@0.9.0":
        optional: true

    abort-controller@3.0.0:
        dependencies:
            event-target-shim: 5.0.1

    accepts@1.3.8:
        dependencies:
            mime-types: 2.1.35
            negotiator: 0.6.3

    acorn-jsx@5.3.2(acorn@8.14.0):
        dependencies:
            acorn: 8.14.0

    acorn@8.14.0: {}

    aggregate-error@3.1.0:
        dependencies:
            clean-stack: 2.2.0
            indent-string: 4.0.0

    ajv@6.12.6:
        dependencies:
            fast-deep-equal: 3.1.3
            fast-json-stable-stringify: 2.1.0
            json-schema-traverse: 0.4.1
            uri-js: 4.4.1

    ansi-regex@5.0.1: {}

    ansi-regex@6.1.0: {}

    ansi-styles@4.3.0:
        dependencies:
            color-convert: 2.0.1

    ansi-styles@6.2.1: {}

    any-promise@1.3.0: {}

    anymatch@3.1.3:
        dependencies:
            normalize-path: 3.0.0
            picomatch: 2.3.1

    arg@5.0.2: {}

    argparse@2.0.1: {}

    aria-hidden@1.2.4:
        dependencies:
            tslib: 2.8.1

    aria-query@5.3.2: {}

    array-buffer-byte-length@1.0.2:
        dependencies:
            call-bound: 1.0.3
            is-array-buffer: 3.0.5

    array-flatten@1.1.1: {}

    array-includes@3.1.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-object-atoms: 1.0.0
            get-intrinsic: 1.2.7
            is-string: 1.1.1

    array-union@2.1.0: {}

    array.prototype.findlast@1.2.5:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.0.0
            es-shim-unscopables: 1.0.2

    array.prototype.findlastindex@1.2.5:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.0.0
            es-shim-unscopables: 1.0.2

    array.prototype.flat@1.3.3:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-shim-unscopables: 1.0.2

    array.prototype.flatmap@1.3.3:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-shim-unscopables: 1.0.2

    array.prototype.tosorted@1.1.4:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-shim-unscopables: 1.0.2

    arraybuffer.prototype.slice@1.0.4:
        dependencies:
            array-buffer-byte-length: 1.0.2
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            is-array-buffer: 3.0.5

    ast-types-flow@0.0.8: {}

    astring@1.9.0: {}

    autoprefixer@10.4.20(postcss@8.4.49):
        dependencies:
            browserslist: 4.24.3
            caniuse-lite: 1.0.30001690
            fraction.js: 4.3.7
            normalize-range: 0.1.2
            picocolors: 1.1.1
            postcss: 8.4.49
            postcss-value-parser: 4.2.0

    available-typed-arrays@1.0.7:
        dependencies:
            possible-typed-array-names: 1.0.0

    axe-core@4.10.2: {}

    axobject-query@4.1.0: {}

    bail@2.0.2: {}

    balanced-match@1.0.2: {}

    base64-js@1.5.1: {}

    basic-auth@2.0.1:
        dependencies:
            safe-buffer: 5.1.2

    binary-extensions@2.3.0: {}

    bl@4.1.0:
        dependencies:
            buffer: 5.7.1
            inherits: 2.0.4
            readable-stream: 3.6.2

    body-parser@1.20.3:
        dependencies:
            bytes: 3.1.2
            content-type: 1.0.5
            debug: 2.6.9
            depd: 2.0.0
            destroy: 1.2.0
            http-errors: 2.0.0
            iconv-lite: 0.4.24
            on-finished: 2.4.1
            qs: 6.13.0
            raw-body: 2.5.2
            type-is: 1.6.18
            unpipe: 1.0.0
        transitivePeerDependencies:
            - supports-color

    brace-expansion@1.1.11:
        dependencies:
            balanced-match: 1.0.2
            concat-map: 0.0.1

    brace-expansion@2.0.1:
        dependencies:
            balanced-match: 1.0.2

    braces@3.0.3:
        dependencies:
            fill-range: 7.1.1

    browserify-zlib@0.1.4:
        dependencies:
            pako: 0.2.9

    browserslist@4.24.3:
        dependencies:
            caniuse-lite: 1.0.30001690
            electron-to-chromium: 1.5.78
            node-releases: 2.0.19
            update-browserslist-db: 1.1.1(browserslist@4.24.3)

    buffer-from@1.1.2: {}

    buffer@5.7.1:
        dependencies:
            base64-js: 1.5.1
            ieee754: 1.2.1

    bytes@3.1.2: {}

    cac@6.7.14: {}

    cacache@17.1.4:
        dependencies:
            "@npmcli/fs": 3.1.1
            fs-minipass: 3.0.3
            glob: 10.4.5
            lru-cache: 7.18.3
            minipass: 7.1.2
            minipass-collect: 1.0.2
            minipass-flush: 1.0.5
            minipass-pipeline: 1.2.4
            p-map: 4.0.0
            ssri: 10.0.6
            tar: 6.2.1
            unique-filename: 3.0.0

    call-bind-apply-helpers@1.0.1:
        dependencies:
            es-errors: 1.3.0
            function-bind: 1.1.2

    call-bind@1.0.8:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            es-define-property: 1.0.1
            get-intrinsic: 1.2.7
            set-function-length: 1.2.2

    call-bound@1.0.3:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            get-intrinsic: 1.2.7

    callsites@3.1.0: {}

    camelcase-css@2.0.1: {}

    caniuse-lite@1.0.30001690: {}

    ccount@2.0.1: {}

    chalk@4.1.2:
        dependencies:
            ansi-styles: 4.3.0
            supports-color: 7.2.0

    character-entities-html4@2.1.0: {}

    character-entities-legacy@3.0.0: {}

    character-entities@2.0.2: {}

    character-reference-invalid@2.0.1: {}

    chokidar@3.6.0:
        dependencies:
            anymatch: 3.1.3
            braces: 3.0.3
            glob-parent: 5.1.2
            is-binary-path: 2.1.0
            is-glob: 4.0.3
            normalize-path: 3.0.0
            readdirp: 3.6.0
        optionalDependencies:
            fsevents: 2.3.3

    chownr@1.1.4: {}

    chownr@2.0.0: {}

    class-variance-authority@0.7.1:
        dependencies:
            clsx: 2.1.1

    classnames@2.5.1: {}

    clean-stack@2.2.0: {}

    cli-cursor@3.1.0:
        dependencies:
            restore-cursor: 3.1.0

    cli-spinners@2.9.2: {}

    clone@1.0.4: {}

    clsx@2.1.1: {}

    color-convert@2.0.1:
        dependencies:
            color-name: 1.1.4

    color-name@1.1.4: {}

    comma-separated-tokens@2.0.3: {}

    commander@4.1.1: {}

    compressible@2.0.18:
        dependencies:
            mime-db: 1.53.0

    compression@1.7.5:
        dependencies:
            bytes: 3.1.2
            compressible: 2.0.18
            debug: 2.6.9
            negotiator: 0.6.4
            on-headers: 1.0.2
            safe-buffer: 5.2.1
            vary: 1.1.2
        transitivePeerDependencies:
            - supports-color

    concat-map@0.0.1: {}

    confbox@0.1.8: {}

    content-disposition@0.5.4:
        dependencies:
            safe-buffer: 5.2.1

    content-type@1.0.5: {}

    convert-source-map@2.0.0: {}

    cookie-signature@1.0.6: {}

    cookie-signature@1.2.2: {}

    cookie@0.6.0: {}

    cookie@0.7.1: {}

    core-util-is@1.0.3: {}

    cross-spawn@7.0.6:
        dependencies:
            path-key: 3.1.1
            shebang-command: 2.0.0
            which: 2.0.2

    css-what@6.1.0: {}

    cssesc@3.0.0: {}

    csstype@3.1.3: {}

    damerau-levenshtein@1.0.8: {}

    data-uri-to-buffer@3.0.1: {}

    data-view-buffer@1.0.2:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-data-view: 1.0.2

    data-view-byte-length@1.0.2:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-data-view: 1.0.2

    data-view-byte-offset@1.0.1:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-data-view: 1.0.2

    dayjs@1.11.13: {}

    debug@2.6.9:
        dependencies:
            ms: 2.0.0

    debug@3.2.7:
        dependencies:
            ms: 2.1.3

    debug@4.4.0:
        dependencies:
            ms: 2.1.3

    decode-named-character-reference@1.0.2:
        dependencies:
            character-entities: 2.0.2

    dedent@1.5.3: {}

    deep-is@0.1.4: {}

    deep-object-diff@1.1.9: {}

    deepmerge@4.3.1: {}

    defaults@1.0.4:
        dependencies:
            clone: 1.0.4

    define-data-property@1.1.4:
        dependencies:
            es-define-property: 1.0.1
            es-errors: 1.3.0
            gopd: 1.2.0

    define-properties@1.2.1:
        dependencies:
            define-data-property: 1.1.4
            has-property-descriptors: 1.0.2
            object-keys: 1.1.1

    depd@2.0.0: {}

    dequal@2.0.3: {}

    destroy@1.2.0: {}

    detect-node-es@1.1.0: {}

    didyoumean@1.2.2: {}

    diff@5.2.0: {}

    dir-glob@3.0.1:
        dependencies:
            path-type: 4.0.0

    dlv@1.1.3: {}

    doctrine@2.1.0:
        dependencies:
            esutils: 2.0.3

    dotenv@16.4.7: {}

    dunder-proto@1.0.1:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            es-errors: 1.3.0
            gopd: 1.2.0

    duplexify@3.7.1:
        dependencies:
            end-of-stream: 1.4.4
            inherits: 2.0.4
            readable-stream: 2.3.8
            stream-shift: 1.0.3

    eastasianwidth@0.2.0: {}

    ee-first@1.1.1: {}

    electron-to-chromium@1.5.78: {}

    emoji-regex@8.0.0: {}

    emoji-regex@9.2.2: {}

    encodeurl@1.0.2: {}

    encodeurl@2.0.0: {}

    end-of-stream@1.4.4:
        dependencies:
            once: 1.4.0

    enhanced-resolve@5.18.0:
        dependencies:
            graceful-fs: 4.2.11
            tapable: 2.2.1

    err-code@2.0.3: {}

    es-abstract@1.23.9:
        dependencies:
            array-buffer-byte-length: 1.0.2
            arraybuffer.prototype.slice: 1.0.4
            available-typed-arrays: 1.0.7
            call-bind: 1.0.8
            call-bound: 1.0.3
            data-view-buffer: 1.0.2
            data-view-byte-length: 1.0.2
            data-view-byte-offset: 1.0.1
            es-define-property: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.0.0
            es-set-tostringtag: 2.1.0
            es-to-primitive: 1.3.0
            function.prototype.name: 1.1.8
            get-intrinsic: 1.2.7
            get-proto: 1.0.1
            get-symbol-description: 1.1.0
            globalthis: 1.0.4
            gopd: 1.2.0
            has-property-descriptors: 1.0.2
            has-proto: 1.2.0
            has-symbols: 1.1.0
            hasown: 2.0.2
            internal-slot: 1.1.0
            is-array-buffer: 3.0.5
            is-callable: 1.2.7
            is-data-view: 1.0.2
            is-regex: 1.2.1
            is-shared-array-buffer: 1.0.4
            is-string: 1.1.1
            is-typed-array: 1.1.15
            is-weakref: 1.1.0
            math-intrinsics: 1.1.0
            object-inspect: 1.13.3
            object-keys: 1.1.1
            object.assign: 4.1.7
            own-keys: 1.0.1
            regexp.prototype.flags: 1.5.4
            safe-array-concat: 1.1.3
            safe-push-apply: 1.0.0
            safe-regex-test: 1.1.0
            set-proto: 1.0.0
            string.prototype.trim: 1.2.10
            string.prototype.trimend: 1.0.9
            string.prototype.trimstart: 1.0.8
            typed-array-buffer: 1.0.3
            typed-array-byte-length: 1.0.3
            typed-array-byte-offset: 1.0.4
            typed-array-length: 1.0.7
            unbox-primitive: 1.1.0
            which-typed-array: 1.1.18

    es-define-property@1.0.1: {}

    es-errors@1.3.0: {}

    es-iterator-helpers@1.2.1:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-set-tostringtag: 2.1.0
            function-bind: 1.1.2
            get-intrinsic: 1.2.7
            globalthis: 1.0.4
            gopd: 1.2.0
            has-property-descriptors: 1.0.2
            has-proto: 1.2.0
            has-symbols: 1.1.0
            internal-slot: 1.1.0
            iterator.prototype: 1.1.5
            safe-array-concat: 1.1.3

    es-module-lexer@1.6.0: {}

    es-object-atoms@1.0.0:
        dependencies:
            es-errors: 1.3.0

    es-set-tostringtag@2.1.0:
        dependencies:
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            has-tostringtag: 1.0.2
            hasown: 2.0.2

    es-shim-unscopables@1.0.2:
        dependencies:
            hasown: 2.0.2

    es-to-primitive@1.3.0:
        dependencies:
            is-callable: 1.2.7
            is-date-object: 1.1.0
            is-symbol: 1.1.1

    esbuild-plugins-node-modules-polyfill@1.6.8(esbuild@0.17.6):
        dependencies:
            "@jspm/core": 2.0.1
            esbuild: 0.17.6
            local-pkg: 0.5.1
            resolve.exports: 2.0.3

    esbuild@0.17.6:
        optionalDependencies:
            "@esbuild/android-arm": 0.17.6
            "@esbuild/android-arm64": 0.17.6
            "@esbuild/android-x64": 0.17.6
            "@esbuild/darwin-arm64": 0.17.6
            "@esbuild/darwin-x64": 0.17.6
            "@esbuild/freebsd-arm64": 0.17.6
            "@esbuild/freebsd-x64": 0.17.6
            "@esbuild/linux-arm": 0.17.6
            "@esbuild/linux-arm64": 0.17.6
            "@esbuild/linux-ia32": 0.17.6
            "@esbuild/linux-loong64": 0.17.6
            "@esbuild/linux-mips64el": 0.17.6
            "@esbuild/linux-ppc64": 0.17.6
            "@esbuild/linux-riscv64": 0.17.6
            "@esbuild/linux-s390x": 0.17.6
            "@esbuild/linux-x64": 0.17.6
            "@esbuild/netbsd-x64": 0.17.6
            "@esbuild/openbsd-x64": 0.17.6
            "@esbuild/sunos-x64": 0.17.6
            "@esbuild/win32-arm64": 0.17.6
            "@esbuild/win32-ia32": 0.17.6
            "@esbuild/win32-x64": 0.17.6

    esbuild@0.21.5:
        optionalDependencies:
            "@esbuild/aix-ppc64": 0.21.5
            "@esbuild/android-arm": 0.21.5
            "@esbuild/android-arm64": 0.21.5
            "@esbuild/android-x64": 0.21.5
            "@esbuild/darwin-arm64": 0.21.5
            "@esbuild/darwin-x64": 0.21.5
            "@esbuild/freebsd-arm64": 0.21.5
            "@esbuild/freebsd-x64": 0.21.5
            "@esbuild/linux-arm": 0.21.5
            "@esbuild/linux-arm64": 0.21.5
            "@esbuild/linux-ia32": 0.21.5
            "@esbuild/linux-loong64": 0.21.5
            "@esbuild/linux-mips64el": 0.21.5
            "@esbuild/linux-ppc64": 0.21.5
            "@esbuild/linux-riscv64": 0.21.5
            "@esbuild/linux-s390x": 0.21.5
            "@esbuild/linux-x64": 0.21.5
            "@esbuild/netbsd-x64": 0.21.5
            "@esbuild/openbsd-x64": 0.21.5
            "@esbuild/sunos-x64": 0.21.5
            "@esbuild/win32-arm64": 0.21.5
            "@esbuild/win32-ia32": 0.21.5
            "@esbuild/win32-x64": 0.21.5

    esbuild@0.24.2:
        optionalDependencies:
            "@esbuild/aix-ppc64": 0.24.2
            "@esbuild/android-arm": 0.24.2
            "@esbuild/android-arm64": 0.24.2
            "@esbuild/android-x64": 0.24.2
            "@esbuild/darwin-arm64": 0.24.2
            "@esbuild/darwin-x64": 0.24.2
            "@esbuild/freebsd-arm64": 0.24.2
            "@esbuild/freebsd-x64": 0.24.2
            "@esbuild/linux-arm": 0.24.2
            "@esbuild/linux-arm64": 0.24.2
            "@esbuild/linux-ia32": 0.24.2
            "@esbuild/linux-loong64": 0.24.2
            "@esbuild/linux-mips64el": 0.24.2
            "@esbuild/linux-ppc64": 0.24.2
            "@esbuild/linux-riscv64": 0.24.2
            "@esbuild/linux-s390x": 0.24.2
            "@esbuild/linux-x64": 0.24.2
            "@esbuild/netbsd-arm64": 0.24.2
            "@esbuild/netbsd-x64": 0.24.2
            "@esbuild/openbsd-arm64": 0.24.2
            "@esbuild/openbsd-x64": 0.24.2
            "@esbuild/sunos-x64": 0.24.2
            "@esbuild/win32-arm64": 0.24.2
            "@esbuild/win32-ia32": 0.24.2
            "@esbuild/win32-x64": 0.24.2

    escalade@3.2.0: {}

    escape-html@1.0.3: {}

    escape-string-regexp@4.0.0: {}

    eslint-import-resolver-node@0.3.9:
        dependencies:
            debug: 3.2.7
            is-core-module: 2.16.1
            resolve: 1.22.10
        transitivePeerDependencies:
            - supports-color

    eslint-import-resolver-typescript@3.7.0(eslint-plugin-import@2.31.0)(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            "@nolyfill/is-core-module": 1.0.39
            debug: 4.4.0
            enhanced-resolve: 5.18.0
            eslint: 9.17.0(jiti@1.21.7)
            fast-glob: 3.3.3
            get-tsconfig: 4.8.1
            is-bun-module: 1.3.0
            is-glob: 4.0.3
            stable-hash: 0.0.4
        optionalDependencies:
            eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.7))
        transitivePeerDependencies:
            - supports-color

    eslint-module-utils@2.12.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            debug: 3.2.7
        optionalDependencies:
            "@typescript-eslint/parser": 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            eslint: 9.17.0(jiti@1.21.7)
            eslint-import-resolver-node: 0.3.9
            eslint-import-resolver-typescript: 3.7.0(eslint-plugin-import@2.31.0)(eslint@9.17.0(jiti@1.21.7))
        transitivePeerDependencies:
            - supports-color

    eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            "@rtsao/scc": 1.1.0
            array-includes: 3.1.8
            array.prototype.findlastindex: 1.2.5
            array.prototype.flat: 1.3.3
            array.prototype.flatmap: 1.3.3
            debug: 3.2.7
            doctrine: 2.1.0
            eslint: 9.17.0(jiti@1.21.7)
            eslint-import-resolver-node: 0.3.9
            eslint-module-utils: 2.12.0(@typescript-eslint/parser@6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.7.0)(eslint@9.17.0(jiti@1.21.7))
            hasown: 2.0.2
            is-core-module: 2.16.1
            is-glob: 4.0.3
            minimatch: 3.1.2
            object.fromentries: 2.0.8
            object.groupby: 1.0.3
            object.values: 1.2.1
            semver: 6.3.1
            string.prototype.trimend: 1.0.9
            tsconfig-paths: 3.15.0
        optionalDependencies:
            "@typescript-eslint/parser": 6.21.0(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
        transitivePeerDependencies:
            - eslint-import-resolver-typescript
            - eslint-import-resolver-webpack
            - supports-color

    eslint-plugin-jsx-a11y@6.10.2(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            aria-query: 5.3.2
            array-includes: 3.1.8
            array.prototype.flatmap: 1.3.3
            ast-types-flow: 0.0.8
            axe-core: 4.10.2
            axobject-query: 4.1.0
            damerau-levenshtein: 1.0.8
            emoji-regex: 9.2.2
            eslint: 9.17.0(jiti@1.21.7)
            hasown: 2.0.2
            jsx-ast-utils: 3.3.5
            language-tags: 1.0.9
            minimatch: 3.1.2
            object.fromentries: 2.0.8
            safe-regex-test: 1.1.0
            string.prototype.includes: 2.0.1

    eslint-plugin-react-hooks@5.1.0(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            eslint: 9.17.0(jiti@1.21.7)

    eslint-plugin-react-refresh@0.4.16(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            eslint: 9.17.0(jiti@1.21.7)

    eslint-plugin-react@7.37.3(eslint@9.17.0(jiti@1.21.7)):
        dependencies:
            array-includes: 3.1.8
            array.prototype.findlast: 1.2.5
            array.prototype.flatmap: 1.3.3
            array.prototype.tosorted: 1.1.4
            doctrine: 2.1.0
            es-iterator-helpers: 1.2.1
            eslint: 9.17.0(jiti@1.21.7)
            estraverse: 5.3.0
            hasown: 2.0.2
            jsx-ast-utils: 3.3.5
            minimatch: 3.1.2
            object.entries: 1.1.8
            object.fromentries: 2.0.8
            object.values: 1.2.1
            prop-types: 15.8.1
            resolve: 2.0.0-next.5
            semver: 6.3.1
            string.prototype.matchall: 4.0.12
            string.prototype.repeat: 1.0.0

    eslint-scope@8.2.0:
        dependencies:
            esrecurse: 4.3.0
            estraverse: 5.3.0

    eslint-visitor-keys@3.4.3: {}

    eslint-visitor-keys@4.2.0: {}

    eslint@9.17.0(jiti@1.21.7):
        dependencies:
            "@eslint-community/eslint-utils": 4.4.1(eslint@9.17.0(jiti@1.21.7))
            "@eslint-community/regexpp": 4.12.1
            "@eslint/config-array": 0.19.1
            "@eslint/core": 0.9.1
            "@eslint/eslintrc": 3.2.0
            "@eslint/js": 9.17.0
            "@eslint/plugin-kit": 0.2.4
            "@humanfs/node": 0.16.6
            "@humanwhocodes/module-importer": 1.0.1
            "@humanwhocodes/retry": 0.4.1
            "@types/estree": 1.0.6
            "@types/json-schema": 7.0.15
            ajv: 6.12.6
            chalk: 4.1.2
            cross-spawn: 7.0.6
            debug: 4.4.0
            escape-string-regexp: 4.0.0
            eslint-scope: 8.2.0
            eslint-visitor-keys: 4.2.0
            espree: 10.3.0
            esquery: 1.6.0
            esutils: 2.0.3
            fast-deep-equal: 3.1.3
            file-entry-cache: 8.0.0
            find-up: 5.0.0
            glob-parent: 6.0.2
            ignore: 5.3.2
            imurmurhash: 0.1.4
            is-glob: 4.0.3
            json-stable-stringify-without-jsonify: 1.0.1
            lodash.merge: 4.6.2
            minimatch: 3.1.2
            natural-compare: 1.4.0
            optionator: 0.9.4
        optionalDependencies:
            jiti: 1.21.7
        transitivePeerDependencies:
            - supports-color

    espree@10.3.0:
        dependencies:
            acorn: 8.14.0
            acorn-jsx: 5.3.2(acorn@8.14.0)
            eslint-visitor-keys: 4.2.0

    esquery@1.6.0:
        dependencies:
            estraverse: 5.3.0

    esrecurse@4.3.0:
        dependencies:
            estraverse: 5.3.0

    estraverse@5.3.0: {}

    estree-util-attach-comments@2.1.1:
        dependencies:
            "@types/estree": 1.0.6

    estree-util-build-jsx@2.2.2:
        dependencies:
            "@types/estree-jsx": 1.0.5
            estree-util-is-identifier-name: 2.1.0
            estree-walker: 3.0.3

    estree-util-is-identifier-name@1.1.0: {}

    estree-util-is-identifier-name@2.1.0: {}

    estree-util-to-js@1.2.0:
        dependencies:
            "@types/estree-jsx": 1.0.5
            astring: 1.9.0
            source-map: 0.7.4

    estree-util-value-to-estree@1.3.0:
        dependencies:
            is-plain-obj: 3.0.0

    estree-util-visit@1.2.1:
        dependencies:
            "@types/estree-jsx": 1.0.5
            "@types/unist": 2.0.11

    estree-walker@3.0.3:
        dependencies:
            "@types/estree": 1.0.6

    esutils@2.0.3: {}

    etag@1.8.1: {}

    eval@0.1.8:
        dependencies:
            "@types/node": 22.10.5
            require-like: 0.1.2

    event-target-shim@5.0.1: {}

    execa@5.1.1:
        dependencies:
            cross-spawn: 7.0.6
            get-stream: 6.0.1
            human-signals: 2.1.0
            is-stream: 2.0.1
            merge-stream: 2.0.0
            npm-run-path: 4.0.1
            onetime: 5.1.2
            signal-exit: 3.0.7
            strip-final-newline: 2.0.0

    exit-hook@2.2.1: {}

    express@4.21.2:
        dependencies:
            accepts: 1.3.8
            array-flatten: 1.1.1
            body-parser: 1.20.3
            content-disposition: 0.5.4
            content-type: 1.0.5
            cookie: 0.7.1
            cookie-signature: 1.0.6
            debug: 2.6.9
            depd: 2.0.0
            encodeurl: 2.0.0
            escape-html: 1.0.3
            etag: 1.8.1
            finalhandler: 1.3.1
            fresh: 0.5.2
            http-errors: 2.0.0
            merge-descriptors: 1.0.3
            methods: 1.1.2
            on-finished: 2.4.1
            parseurl: 1.3.3
            path-to-regexp: 0.1.12
            proxy-addr: 2.0.7
            qs: 6.13.0
            range-parser: 1.2.1
            safe-buffer: 5.2.1
            send: 0.19.0
            serve-static: 1.16.2
            setprototypeof: 1.2.0
            statuses: 2.0.1
            type-is: 1.6.18
            utils-merge: 1.0.1
            vary: 1.1.2
        transitivePeerDependencies:
            - supports-color

    extend@3.0.2: {}

    fast-deep-equal@3.1.3: {}

    fast-glob@3.3.3:
        dependencies:
            "@nodelib/fs.stat": 2.0.5
            "@nodelib/fs.walk": 1.2.8
            glob-parent: 5.1.2
            merge2: 1.4.1
            micromatch: 4.0.8

    fast-json-stable-stringify@2.1.0: {}

    fast-levenshtein@2.0.6: {}

    fastq@1.18.0:
        dependencies:
            reusify: 1.0.4

    fault@2.0.1:
        dependencies:
            format: 0.2.2

    file-entry-cache@8.0.0:
        dependencies:
            flat-cache: 4.0.1

    fill-range@7.1.1:
        dependencies:
            to-regex-range: 5.0.1

    finalhandler@1.3.1:
        dependencies:
            debug: 2.6.9
            encodeurl: 2.0.0
            escape-html: 1.0.3
            on-finished: 2.4.1
            parseurl: 1.3.3
            statuses: 2.0.1
            unpipe: 1.0.0
        transitivePeerDependencies:
            - supports-color

    find-up@5.0.0:
        dependencies:
            locate-path: 6.0.0
            path-exists: 4.0.0

    flat-cache@4.0.1:
        dependencies:
            flatted: 3.3.2
            keyv: 4.5.4

    flatted@3.3.2: {}

    for-each@0.3.3:
        dependencies:
            is-callable: 1.2.7

    foreground-child@3.3.0:
        dependencies:
            cross-spawn: 7.0.6
            signal-exit: 4.1.0

    format@0.2.2: {}

    forwarded@0.2.0: {}

    fraction.js@4.3.7: {}

    framer-motion@11.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
        dependencies:
            motion-dom: 11.16.0
            motion-utils: 11.16.0
            tslib: 2.8.1
        optionalDependencies:
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)

    fresh@0.5.2: {}

    fs-constants@1.0.0: {}

    fs-extra@10.1.0:
        dependencies:
            graceful-fs: 4.2.11
            jsonfile: 6.1.0
            universalify: 2.0.1

    fs-minipass@2.1.0:
        dependencies:
            minipass: 3.3.6

    fs-minipass@3.0.3:
        dependencies:
            minipass: 7.1.2

    fsevents@2.3.3:
        optional: true

    function-bind@1.1.2: {}

    function.prototype.name@1.1.8:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            functions-have-names: 1.2.3
            hasown: 2.0.2
            is-callable: 1.2.7

    functions-have-names@1.2.3: {}

    generic-names@4.0.0:
        dependencies:
            loader-utils: 3.3.1

    gensync@1.0.0-beta.2: {}

    get-intrinsic@1.2.7:
        dependencies:
            call-bind-apply-helpers: 1.0.1
            es-define-property: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.0.0
            function-bind: 1.1.2
            get-proto: 1.0.1
            gopd: 1.2.0
            has-symbols: 1.1.0
            hasown: 2.0.2
            math-intrinsics: 1.1.0

    get-nonce@1.0.1: {}

    get-port@5.1.1: {}

    get-proto@1.0.1:
        dependencies:
            dunder-proto: 1.0.1
            es-object-atoms: 1.0.0

    get-stream@6.0.1: {}

    get-symbol-description@1.1.0:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            get-intrinsic: 1.2.7

    get-tsconfig@4.8.1:
        dependencies:
            resolve-pkg-maps: 1.0.0

    glob-parent@5.1.2:
        dependencies:
            is-glob: 4.0.3

    glob-parent@6.0.2:
        dependencies:
            is-glob: 4.0.3

    glob@10.4.5:
        dependencies:
            foreground-child: 3.3.0
            jackspeak: 3.4.3
            minimatch: 9.0.5
            minipass: 7.1.2
            package-json-from-dist: 1.0.1
            path-scurry: 1.11.1

    globals@11.12.0: {}

    globals@14.0.0: {}

    globals@15.14.0: {}

    globalthis@1.0.4:
        dependencies:
            define-properties: 1.2.1
            gopd: 1.2.0

    globby@11.1.0:
        dependencies:
            array-union: 2.1.0
            dir-glob: 3.0.1
            fast-glob: 3.3.3
            ignore: 5.3.2
            merge2: 1.4.1
            slash: 3.0.0

    globrex@0.1.2: {}

    gopd@1.2.0: {}

    graceful-fs@4.2.11: {}

    graphemer@1.4.0: {}

    gunzip-maybe@1.4.2:
        dependencies:
            browserify-zlib: 0.1.4
            is-deflate: 1.0.0
            is-gzip: 1.0.0
            peek-stream: 1.1.3
            pumpify: 1.5.1
            through2: 2.0.5

    has-bigints@1.1.0: {}

    has-flag@4.0.0: {}

    has-property-descriptors@1.0.2:
        dependencies:
            es-define-property: 1.0.1

    has-proto@1.2.0:
        dependencies:
            dunder-proto: 1.0.1

    has-symbols@1.1.0: {}

    has-tostringtag@1.0.2:
        dependencies:
            has-symbols: 1.1.0

    hasown@2.0.2:
        dependencies:
            function-bind: 1.1.2

    hast-util-to-estree@2.3.3:
        dependencies:
            "@types/estree": 1.0.6
            "@types/estree-jsx": 1.0.5
            "@types/hast": 2.3.10
            "@types/unist": 2.0.11
            comma-separated-tokens: 2.0.3
            estree-util-attach-comments: 2.1.1
            estree-util-is-identifier-name: 2.1.0
            hast-util-whitespace: 2.0.1
            mdast-util-mdx-expression: 1.3.2
            mdast-util-mdxjs-esm: 1.3.1
            property-information: 6.5.0
            space-separated-tokens: 2.0.2
            style-to-object: 0.4.4
            unist-util-position: 4.0.4
            zwitch: 2.0.4
        transitivePeerDependencies:
            - supports-color

    hast-util-whitespace@2.0.1: {}

    hosted-git-info@6.1.3:
        dependencies:
            lru-cache: 7.18.3

    howler@2.2.4: {}

    http-errors@2.0.0:
        dependencies:
            depd: 2.0.0
            inherits: 2.0.4
            setprototypeof: 1.2.0
            statuses: 2.0.1
            toidentifier: 1.0.1

    human-signals@2.1.0: {}

    iconv-lite@0.4.24:
        dependencies:
            safer-buffer: 2.1.2

    icss-utils@5.1.0(postcss@8.4.49):
        dependencies:
            postcss: 8.4.49

    ieee754@1.2.1: {}

    ignore@5.3.2: {}

    import-fresh@3.3.0:
        dependencies:
            parent-module: 1.0.1
            resolve-from: 4.0.0

    imurmurhash@0.1.4: {}

    indent-string@4.0.0: {}

    inherits@2.0.4: {}

    inline-style-parser@0.1.1: {}

    internal-slot@1.1.0:
        dependencies:
            es-errors: 1.3.0
            hasown: 2.0.2
            side-channel: 1.1.0

    ipaddr.js@1.9.1: {}

    is-alphabetical@2.0.1: {}

    is-alphanumerical@2.0.1:
        dependencies:
            is-alphabetical: 2.0.1
            is-decimal: 2.0.1

    is-arguments@1.2.0:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-array-buffer@3.0.5:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            get-intrinsic: 1.2.7

    is-async-function@2.1.0:
        dependencies:
            call-bound: 1.0.3
            get-proto: 1.0.1
            has-tostringtag: 1.0.2
            safe-regex-test: 1.1.0

    is-bigint@1.1.0:
        dependencies:
            has-bigints: 1.1.0

    is-binary-path@2.1.0:
        dependencies:
            binary-extensions: 2.3.0

    is-boolean-object@1.2.1:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-buffer@2.0.5: {}

    is-bun-module@1.3.0:
        dependencies:
            semver: 7.6.3

    is-callable@1.2.7: {}

    is-core-module@2.16.1:
        dependencies:
            hasown: 2.0.2

    is-data-view@1.0.2:
        dependencies:
            call-bound: 1.0.3
            get-intrinsic: 1.2.7
            is-typed-array: 1.1.15

    is-date-object@1.1.0:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-decimal@2.0.1: {}

    is-deflate@1.0.0: {}

    is-extglob@2.1.1: {}

    is-finalizationregistry@1.1.1:
        dependencies:
            call-bound: 1.0.3

    is-fullwidth-code-point@3.0.0: {}

    is-generator-function@1.1.0:
        dependencies:
            call-bound: 1.0.3
            get-proto: 1.0.1
            has-tostringtag: 1.0.2
            safe-regex-test: 1.1.0

    is-glob@4.0.3:
        dependencies:
            is-extglob: 2.1.1

    is-gzip@1.0.0: {}

    is-hexadecimal@2.0.1: {}

    is-interactive@1.0.0: {}

    is-map@2.0.3: {}

    is-number-object@1.1.1:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-number@7.0.0: {}

    is-plain-obj@3.0.0: {}

    is-plain-obj@4.1.0: {}

    is-reference@3.0.3:
        dependencies:
            "@types/estree": 1.0.6

    is-regex@1.2.1:
        dependencies:
            call-bound: 1.0.3
            gopd: 1.2.0
            has-tostringtag: 1.0.2
            hasown: 2.0.2

    is-set@2.0.3: {}

    is-shared-array-buffer@1.0.4:
        dependencies:
            call-bound: 1.0.3

    is-stream@2.0.1: {}

    is-string@1.1.1:
        dependencies:
            call-bound: 1.0.3
            has-tostringtag: 1.0.2

    is-symbol@1.1.1:
        dependencies:
            call-bound: 1.0.3
            has-symbols: 1.1.0
            safe-regex-test: 1.1.0

    is-typed-array@1.1.15:
        dependencies:
            which-typed-array: 1.1.18

    is-unicode-supported@0.1.0: {}

    is-weakmap@2.0.2: {}

    is-weakref@1.1.0:
        dependencies:
            call-bound: 1.0.3

    is-weakset@2.0.4:
        dependencies:
            call-bound: 1.0.3
            get-intrinsic: 1.2.7

    isarray@1.0.0: {}

    isarray@2.0.5: {}

    isbot@4.4.0: {}

    isexe@2.0.0: {}

    iterator.prototype@1.1.5:
        dependencies:
            define-data-property: 1.1.4
            es-object-atoms: 1.0.0
            get-intrinsic: 1.2.7
            get-proto: 1.0.1
            has-symbols: 1.1.0
            set-function-name: 2.0.2

    jackspeak@3.4.3:
        dependencies:
            "@isaacs/cliui": 8.0.2
        optionalDependencies:
            "@pkgjs/parseargs": 0.11.0

    javascript-stringify@2.1.0: {}

    jiti@1.21.7: {}

    js-tokens@4.0.0: {}

    js-yaml@4.1.0:
        dependencies:
            argparse: 2.0.1

    jsesc@3.0.2: {}

    json-buffer@3.0.1: {}

    json-parse-even-better-errors@3.0.2: {}

    json-schema-traverse@0.4.1: {}

    json-schema@0.4.0: {}

    json-stable-stringify-without-jsonify@1.0.1: {}

    json5@1.0.2:
        dependencies:
            minimist: 1.2.8

    json5@2.2.3: {}

    jsonfile@6.1.0:
        dependencies:
            universalify: 2.0.1
        optionalDependencies:
            graceful-fs: 4.2.11

    jsx-ast-utils@3.3.5:
        dependencies:
            array-includes: 3.1.8
            array.prototype.flat: 1.3.3
            object.assign: 4.1.7
            object.values: 1.2.1

    keyv@4.5.4:
        dependencies:
            json-buffer: 3.0.1

    kleur@4.1.5: {}

    language-subtag-registry@0.3.23: {}

    language-tags@1.0.9:
        dependencies:
            language-subtag-registry: 0.3.23

    levn@0.4.1:
        dependencies:
            prelude-ls: 1.2.1
            type-check: 0.4.0

    lilconfig@3.1.3: {}

    lines-and-columns@1.2.4: {}

    loader-utils@3.3.1: {}

    local-pkg@0.5.1:
        dependencies:
            mlly: 1.7.3
            pkg-types: 1.3.0

    locate-path@6.0.0:
        dependencies:
            p-locate: 5.0.0

    lodash.camelcase@4.3.0: {}

    lodash.debounce@4.0.8: {}

    lodash.merge@4.6.2: {}

    lodash@4.17.21: {}

    log-symbols@4.1.0:
        dependencies:
            chalk: 4.1.2
            is-unicode-supported: 0.1.0

    longest-streak@3.1.0: {}

    loose-envify@1.4.0:
        dependencies:
            js-tokens: 4.0.0

    lru-cache@10.4.3: {}

    lru-cache@5.1.1:
        dependencies:
            yallist: 3.1.1

    lru-cache@7.18.3: {}

    lucide-react@0.469.0(react@18.3.1):
        dependencies:
            react: 18.3.1

    markdown-extensions@1.1.1: {}

    math-intrinsics@1.1.0: {}

    mdast-util-definitions@5.1.2:
        dependencies:
            "@types/mdast": 3.0.15
            "@types/unist": 2.0.11
            unist-util-visit: 4.1.2

    mdast-util-from-markdown@1.3.1:
        dependencies:
            "@types/mdast": 3.0.15
            "@types/unist": 2.0.11
            decode-named-character-reference: 1.0.2
            mdast-util-to-string: 3.2.0
            micromark: 3.2.0
            micromark-util-decode-numeric-character-reference: 1.1.0
            micromark-util-decode-string: 1.1.0
            micromark-util-normalize-identifier: 1.1.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            unist-util-stringify-position: 3.0.3
            uvu: 0.5.6
        transitivePeerDependencies:
            - supports-color

    mdast-util-frontmatter@1.0.1:
        dependencies:
            "@types/mdast": 3.0.15
            mdast-util-to-markdown: 1.5.0
            micromark-extension-frontmatter: 1.1.1

    mdast-util-mdx-expression@1.3.2:
        dependencies:
            "@types/estree-jsx": 1.0.5
            "@types/hast": 2.3.10
            "@types/mdast": 3.0.15
            mdast-util-from-markdown: 1.3.1
            mdast-util-to-markdown: 1.5.0
        transitivePeerDependencies:
            - supports-color

    mdast-util-mdx-jsx@2.1.4:
        dependencies:
            "@types/estree-jsx": 1.0.5
            "@types/hast": 2.3.10
            "@types/mdast": 3.0.15
            "@types/unist": 2.0.11
            ccount: 2.0.1
            mdast-util-from-markdown: 1.3.1
            mdast-util-to-markdown: 1.5.0
            parse-entities: 4.0.2
            stringify-entities: 4.0.4
            unist-util-remove-position: 4.0.2
            unist-util-stringify-position: 3.0.3
            vfile-message: 3.1.4
        transitivePeerDependencies:
            - supports-color

    mdast-util-mdx@2.0.1:
        dependencies:
            mdast-util-from-markdown: 1.3.1
            mdast-util-mdx-expression: 1.3.2
            mdast-util-mdx-jsx: 2.1.4
            mdast-util-mdxjs-esm: 1.3.1
            mdast-util-to-markdown: 1.5.0
        transitivePeerDependencies:
            - supports-color

    mdast-util-mdxjs-esm@1.3.1:
        dependencies:
            "@types/estree-jsx": 1.0.5
            "@types/hast": 2.3.10
            "@types/mdast": 3.0.15
            mdast-util-from-markdown: 1.3.1
            mdast-util-to-markdown: 1.5.0
        transitivePeerDependencies:
            - supports-color

    mdast-util-phrasing@3.0.1:
        dependencies:
            "@types/mdast": 3.0.15
            unist-util-is: 5.2.1

    mdast-util-to-hast@12.3.0:
        dependencies:
            "@types/hast": 2.3.10
            "@types/mdast": 3.0.15
            mdast-util-definitions: 5.1.2
            micromark-util-sanitize-uri: 1.2.0
            trim-lines: 3.0.1
            unist-util-generated: 2.0.1
            unist-util-position: 4.0.4
            unist-util-visit: 4.1.2

    mdast-util-to-markdown@1.5.0:
        dependencies:
            "@types/mdast": 3.0.15
            "@types/unist": 2.0.11
            longest-streak: 3.1.0
            mdast-util-phrasing: 3.0.1
            mdast-util-to-string: 3.2.0
            micromark-util-decode-string: 1.1.0
            unist-util-visit: 4.1.2
            zwitch: 2.0.4

    mdast-util-to-string@3.2.0:
        dependencies:
            "@types/mdast": 3.0.15

    media-query-parser@2.0.2:
        dependencies:
            "@babel/runtime": 7.26.0

    media-typer@0.3.0: {}

    merge-descriptors@1.0.3: {}

    merge-stream@2.0.0: {}

    merge2@1.4.1: {}

    methods@1.1.2: {}

    micromark-core-commonmark@1.1.0:
        dependencies:
            decode-named-character-reference: 1.0.2
            micromark-factory-destination: 1.1.0
            micromark-factory-label: 1.1.0
            micromark-factory-space: 1.1.0
            micromark-factory-title: 1.1.0
            micromark-factory-whitespace: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-chunked: 1.1.0
            micromark-util-classify-character: 1.1.0
            micromark-util-html-tag-name: 1.2.0
            micromark-util-normalize-identifier: 1.1.0
            micromark-util-resolve-all: 1.1.0
            micromark-util-subtokenize: 1.1.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6

    micromark-extension-frontmatter@1.1.1:
        dependencies:
            fault: 2.0.1
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0

    micromark-extension-mdx-expression@1.0.8:
        dependencies:
            "@types/estree": 1.0.6
            micromark-factory-mdx-expression: 1.0.9
            micromark-factory-space: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-events-to-acorn: 1.2.3
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6

    micromark-extension-mdx-jsx@1.0.5:
        dependencies:
            "@types/acorn": 4.0.6
            "@types/estree": 1.0.6
            estree-util-is-identifier-name: 2.1.0
            micromark-factory-mdx-expression: 1.0.9
            micromark-factory-space: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6
            vfile-message: 3.1.4

    micromark-extension-mdx-md@1.0.1:
        dependencies:
            micromark-util-types: 1.1.0

    micromark-extension-mdxjs-esm@1.0.5:
        dependencies:
            "@types/estree": 1.0.6
            micromark-core-commonmark: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-events-to-acorn: 1.2.3
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            unist-util-position-from-estree: 1.1.2
            uvu: 0.5.6
            vfile-message: 3.1.4

    micromark-extension-mdxjs@1.0.1:
        dependencies:
            acorn: 8.14.0
            acorn-jsx: 5.3.2(acorn@8.14.0)
            micromark-extension-mdx-expression: 1.0.8
            micromark-extension-mdx-jsx: 1.0.5
            micromark-extension-mdx-md: 1.0.1
            micromark-extension-mdxjs-esm: 1.0.5
            micromark-util-combine-extensions: 1.1.0
            micromark-util-types: 1.1.0

    micromark-factory-destination@1.1.0:
        dependencies:
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0

    micromark-factory-label@1.1.0:
        dependencies:
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6

    micromark-factory-mdx-expression@1.0.9:
        dependencies:
            "@types/estree": 1.0.6
            micromark-util-character: 1.2.0
            micromark-util-events-to-acorn: 1.2.3
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            unist-util-position-from-estree: 1.1.2
            uvu: 0.5.6
            vfile-message: 3.1.4

    micromark-factory-space@1.1.0:
        dependencies:
            micromark-util-character: 1.2.0
            micromark-util-types: 1.1.0

    micromark-factory-title@1.1.0:
        dependencies:
            micromark-factory-space: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0

    micromark-factory-whitespace@1.1.0:
        dependencies:
            micromark-factory-space: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0

    micromark-util-character@1.2.0:
        dependencies:
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0

    micromark-util-chunked@1.1.0:
        dependencies:
            micromark-util-symbol: 1.1.0

    micromark-util-classify-character@1.1.0:
        dependencies:
            micromark-util-character: 1.2.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0

    micromark-util-combine-extensions@1.1.0:
        dependencies:
            micromark-util-chunked: 1.1.0
            micromark-util-types: 1.1.0

    micromark-util-decode-numeric-character-reference@1.1.0:
        dependencies:
            micromark-util-symbol: 1.1.0

    micromark-util-decode-string@1.1.0:
        dependencies:
            decode-named-character-reference: 1.0.2
            micromark-util-character: 1.2.0
            micromark-util-decode-numeric-character-reference: 1.1.0
            micromark-util-symbol: 1.1.0

    micromark-util-encode@1.1.0: {}

    micromark-util-events-to-acorn@1.2.3:
        dependencies:
            "@types/acorn": 4.0.6
            "@types/estree": 1.0.6
            "@types/unist": 2.0.11
            estree-util-visit: 1.2.1
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6
            vfile-message: 3.1.4

    micromark-util-html-tag-name@1.2.0: {}

    micromark-util-normalize-identifier@1.1.0:
        dependencies:
            micromark-util-symbol: 1.1.0

    micromark-util-resolve-all@1.1.0:
        dependencies:
            micromark-util-types: 1.1.0

    micromark-util-sanitize-uri@1.2.0:
        dependencies:
            micromark-util-character: 1.2.0
            micromark-util-encode: 1.1.0
            micromark-util-symbol: 1.1.0

    micromark-util-subtokenize@1.1.0:
        dependencies:
            micromark-util-chunked: 1.1.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6

    micromark-util-symbol@1.1.0: {}

    micromark-util-types@1.1.0: {}

    micromark@3.2.0:
        dependencies:
            "@types/debug": 4.1.12
            debug: 4.4.0
            decode-named-character-reference: 1.0.2
            micromark-core-commonmark: 1.1.0
            micromark-factory-space: 1.1.0
            micromark-util-character: 1.2.0
            micromark-util-chunked: 1.1.0
            micromark-util-combine-extensions: 1.1.0
            micromark-util-decode-numeric-character-reference: 1.1.0
            micromark-util-encode: 1.1.0
            micromark-util-normalize-identifier: 1.1.0
            micromark-util-resolve-all: 1.1.0
            micromark-util-sanitize-uri: 1.2.0
            micromark-util-subtokenize: 1.1.0
            micromark-util-symbol: 1.1.0
            micromark-util-types: 1.1.0
            uvu: 0.5.6
        transitivePeerDependencies:
            - supports-color

    micromatch@4.0.8:
        dependencies:
            braces: 3.0.3
            picomatch: 2.3.1

    mime-db@1.52.0: {}

    mime-db@1.53.0: {}

    mime-types@2.1.35:
        dependencies:
            mime-db: 1.52.0

    mime@1.6.0: {}

    mimic-fn@2.1.0: {}

    minimatch@3.1.2:
        dependencies:
            brace-expansion: 1.1.11

    minimatch@9.0.3:
        dependencies:
            brace-expansion: 2.0.1

    minimatch@9.0.5:
        dependencies:
            brace-expansion: 2.0.1

    minimist@1.2.8: {}

    minipass-collect@1.0.2:
        dependencies:
            minipass: 3.3.6

    minipass-flush@1.0.5:
        dependencies:
            minipass: 3.3.6

    minipass-pipeline@1.2.4:
        dependencies:
            minipass: 3.3.6

    minipass@3.3.6:
        dependencies:
            yallist: 4.0.0

    minipass@5.0.0: {}

    minipass@7.1.2: {}

    minizlib@2.1.2:
        dependencies:
            minipass: 3.3.6
            yallist: 4.0.0

    mkdirp-classic@0.5.3: {}

    mkdirp@1.0.4: {}

    mlly@1.7.3:
        dependencies:
            acorn: 8.14.0
            pathe: 1.1.2
            pkg-types: 1.3.0
            ufo: 1.5.4

    modern-ahocorasick@1.1.0: {}

    morgan@1.10.0:
        dependencies:
            basic-auth: 2.0.1
            debug: 2.6.9
            depd: 2.0.0
            on-finished: 2.3.0
            on-headers: 1.0.2
        transitivePeerDependencies:
            - supports-color

    motion-dom@11.16.0:
        dependencies:
            motion-utils: 11.16.0

    motion-utils@11.16.0: {}

    mri@1.2.0: {}

    mrmime@1.0.1: {}

    ms@2.0.0: {}

    ms@2.1.3: {}

    mz@2.7.0:
        dependencies:
            any-promise: 1.3.0
            object-assign: 4.1.1
            thenify-all: 1.6.0

    nanoid@3.3.8: {}

    nanoid@5.0.9: {}

    natural-compare@1.4.0: {}

    negotiator@0.6.3: {}

    negotiator@0.6.4: {}

    node-releases@2.0.19: {}

    normalize-package-data@5.0.0:
        dependencies:
            hosted-git-info: 6.1.3
            is-core-module: 2.16.1
            semver: 7.6.3
            validate-npm-package-license: 3.0.4

    normalize-path@3.0.0: {}

    normalize-range@0.1.2: {}

    npm-install-checks@6.3.0:
        dependencies:
            semver: 7.6.3

    npm-normalize-package-bin@3.0.1: {}

    npm-package-arg@10.1.0:
        dependencies:
            hosted-git-info: 6.1.3
            proc-log: 3.0.0
            semver: 7.6.3
            validate-npm-package-name: 5.0.1

    npm-pick-manifest@8.0.2:
        dependencies:
            npm-install-checks: 6.3.0
            npm-normalize-package-bin: 3.0.1
            npm-package-arg: 10.1.0
            semver: 7.6.3

    npm-run-path@4.0.1:
        dependencies:
            path-key: 3.1.1

    object-assign@4.1.1: {}

    object-hash@3.0.0: {}

    object-inspect@1.13.3: {}

    object-keys@1.1.1: {}

    object.assign@4.1.7:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-object-atoms: 1.0.0
            has-symbols: 1.1.0
            object-keys: 1.1.1

    object.entries@1.1.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-object-atoms: 1.0.0

    object.fromentries@2.0.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-object-atoms: 1.0.0

    object.groupby@1.0.3:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9

    object.values@1.2.1:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-object-atoms: 1.0.0

    on-finished@2.3.0:
        dependencies:
            ee-first: 1.1.1

    on-finished@2.4.1:
        dependencies:
            ee-first: 1.1.1

    on-headers@1.0.2: {}

    once@1.4.0:
        dependencies:
            wrappy: 1.0.2

    onetime@5.1.2:
        dependencies:
            mimic-fn: 2.1.0

    optionator@0.9.4:
        dependencies:
            deep-is: 0.1.4
            fast-levenshtein: 2.0.6
            levn: 0.4.1
            prelude-ls: 1.2.1
            type-check: 0.4.0
            word-wrap: 1.2.5

    ora@5.4.1:
        dependencies:
            bl: 4.1.0
            chalk: 4.1.2
            cli-cursor: 3.1.0
            cli-spinners: 2.9.2
            is-interactive: 1.0.0
            is-unicode-supported: 0.1.0
            log-symbols: 4.1.0
            strip-ansi: 6.0.1
            wcwidth: 1.0.1

    outdent@0.8.0: {}

    own-keys@1.0.1:
        dependencies:
            get-intrinsic: 1.2.7
            object-keys: 1.1.1
            safe-push-apply: 1.0.0

    p-limit@3.1.0:
        dependencies:
            yocto-queue: 0.1.0

    p-locate@5.0.0:
        dependencies:
            p-limit: 3.1.0

    p-map@4.0.0:
        dependencies:
            aggregate-error: 3.1.0

    package-json-from-dist@1.0.1: {}

    pako@0.2.9: {}

    parent-module@1.0.1:
        dependencies:
            callsites: 3.1.0

    parse-entities@4.0.2:
        dependencies:
            "@types/unist": 2.0.11
            character-entities-legacy: 3.0.0
            character-reference-invalid: 2.0.1
            decode-named-character-reference: 1.0.2
            is-alphanumerical: 2.0.1
            is-decimal: 2.0.1
            is-hexadecimal: 2.0.1

    parse-ms@2.1.0: {}

    parseurl@1.3.3: {}

    path-exists@4.0.0: {}

    path-key@3.1.1: {}

    path-parse@1.0.7: {}

    path-scurry@1.11.1:
        dependencies:
            lru-cache: 10.4.3
            minipass: 7.1.2

    path-to-regexp@0.1.12: {}

    path-type@4.0.0: {}

    pathe@1.1.2: {}

    peek-stream@1.1.3:
        dependencies:
            buffer-from: 1.1.2
            duplexify: 3.7.1
            through2: 2.0.5

    periscopic@3.1.0:
        dependencies:
            "@types/estree": 1.0.6
            estree-walker: 3.0.3
            is-reference: 3.0.3

    picocolors@1.1.1: {}

    picomatch@2.3.1: {}

    pidtree@0.6.0: {}

    pify@2.3.0: {}

    pirates@4.0.6: {}

    pkg-types@1.3.0:
        dependencies:
            confbox: 0.1.8
            mlly: 1.7.3
            pathe: 1.1.2

    possible-typed-array-names@1.0.0: {}

    postcss-discard-duplicates@5.1.0(postcss@8.4.49):
        dependencies:
            postcss: 8.4.49

    postcss-import@15.1.0(postcss@8.4.49):
        dependencies:
            postcss: 8.4.49
            postcss-value-parser: 4.2.0
            read-cache: 1.0.0
            resolve: 1.22.10

    postcss-js@4.0.1(postcss@8.4.49):
        dependencies:
            camelcase-css: 2.0.1
            postcss: 8.4.49

    postcss-load-config@4.0.2(postcss@8.4.49):
        dependencies:
            lilconfig: 3.1.3
            yaml: 2.7.0
        optionalDependencies:
            postcss: 8.4.49

    postcss-modules-extract-imports@3.1.0(postcss@8.4.49):
        dependencies:
            postcss: 8.4.49

    postcss-modules-local-by-default@4.2.0(postcss@8.4.49):
        dependencies:
            icss-utils: 5.1.0(postcss@8.4.49)
            postcss: 8.4.49
            postcss-selector-parser: 7.0.0
            postcss-value-parser: 4.2.0

    postcss-modules-scope@3.2.1(postcss@8.4.49):
        dependencies:
            postcss: 8.4.49
            postcss-selector-parser: 7.0.0

    postcss-modules-values@4.0.0(postcss@8.4.49):
        dependencies:
            icss-utils: 5.1.0(postcss@8.4.49)
            postcss: 8.4.49

    postcss-modules@6.0.1(postcss@8.4.49):
        dependencies:
            generic-names: 4.0.0
            icss-utils: 5.1.0(postcss@8.4.49)
            lodash.camelcase: 4.3.0
            postcss: 8.4.49
            postcss-modules-extract-imports: 3.1.0(postcss@8.4.49)
            postcss-modules-local-by-default: 4.2.0(postcss@8.4.49)
            postcss-modules-scope: 3.2.1(postcss@8.4.49)
            postcss-modules-values: 4.0.0(postcss@8.4.49)
            string-hash: 1.1.3

    postcss-nested@6.2.0(postcss@8.4.49):
        dependencies:
            postcss: 8.4.49
            postcss-selector-parser: 6.1.2

    postcss-selector-parser@6.1.2:
        dependencies:
            cssesc: 3.0.0
            util-deprecate: 1.0.2

    postcss-selector-parser@7.0.0:
        dependencies:
            cssesc: 3.0.0
            util-deprecate: 1.0.2

    postcss-value-parser@4.2.0: {}

    postcss@8.4.49:
        dependencies:
            nanoid: 3.3.8
            picocolors: 1.1.1
            source-map-js: 1.2.1

    prelude-ls@1.2.1: {}

    prettier@2.8.8: {}

    pretty-ms@7.0.1:
        dependencies:
            parse-ms: 2.1.0

    proc-log@3.0.0: {}

    process-nextick-args@2.0.1: {}

    promise-inflight@1.0.1: {}

    promise-retry@2.0.1:
        dependencies:
            err-code: 2.0.3
            retry: 0.12.0

    prop-types@15.8.1:
        dependencies:
            loose-envify: 1.4.0
            object-assign: 4.1.1
            react-is: 16.13.1

    property-information@6.5.0: {}

    proxy-addr@2.0.7:
        dependencies:
            forwarded: 0.2.0
            ipaddr.js: 1.9.1

    pump@2.0.1:
        dependencies:
            end-of-stream: 1.4.4
            once: 1.4.0

    pump@3.0.2:
        dependencies:
            end-of-stream: 1.4.4
            once: 1.4.0

    pumpify@1.5.1:
        dependencies:
            duplexify: 3.7.1
            inherits: 2.0.4
            pump: 2.0.1

    punycode@2.3.1: {}

    qs@6.13.0:
        dependencies:
            side-channel: 1.1.0

    queue-microtask@1.2.3: {}

    range-parser@1.2.1: {}

    raw-body@2.5.2:
        dependencies:
            bytes: 3.1.2
            http-errors: 2.0.0
            iconv-lite: 0.4.24
            unpipe: 1.0.0

    react-aiwriter@1.0.0: {}

    react-dom@18.3.1(react@18.3.1):
        dependencies:
            loose-envify: 1.4.0
            react: 18.3.1
            scheduler: 0.23.2

    react-is@16.13.1: {}

    react-refresh@0.14.2: {}

    react-remove-scroll-bar@2.3.8(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            react: 18.3.1
            react-style-singleton: 2.2.3(@types/react@18.3.18)(react@18.3.1)
            tslib: 2.8.1
        optionalDependencies:
            "@types/react": 18.3.18

    react-remove-scroll@2.6.2(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            react: 18.3.1
            react-remove-scroll-bar: 2.3.8(@types/react@18.3.18)(react@18.3.1)
            react-style-singleton: 2.2.3(@types/react@18.3.18)(react@18.3.1)
            tslib: 2.8.1
            use-callback-ref: 1.3.3(@types/react@18.3.18)(react@18.3.1)
            use-sidecar: 1.1.3(@types/react@18.3.18)(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18

    react-router-dom@6.28.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
        dependencies:
            "@remix-run/router": 1.21.0
            react: 18.3.1
            react-dom: 18.3.1(react@18.3.1)
            react-router: 6.28.1(react@18.3.1)

    react-router@6.28.1(react@18.3.1):
        dependencies:
            "@remix-run/router": 1.21.0
            react: 18.3.1

    react-style-singleton@2.2.3(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            get-nonce: 1.0.1
            react: 18.3.1
            tslib: 2.8.1
        optionalDependencies:
            "@types/react": 18.3.18

    react-textarea-autosize@8.5.6(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            "@babel/runtime": 7.26.0
            react: 18.3.1
            use-composed-ref: 1.4.0(@types/react@18.3.18)(react@18.3.1)
            use-latest: 1.3.0(@types/react@18.3.18)(react@18.3.1)
        transitivePeerDependencies:
            - "@types/react"

    react@18.3.1:
        dependencies:
            loose-envify: 1.4.0

    read-cache@1.0.0:
        dependencies:
            pify: 2.3.0

    readable-stream@2.3.8:
        dependencies:
            core-util-is: 1.0.3
            inherits: 2.0.4
            isarray: 1.0.0
            process-nextick-args: 2.0.1
            safe-buffer: 5.1.2
            string_decoder: 1.1.1
            util-deprecate: 1.0.2

    readable-stream@3.6.2:
        dependencies:
            inherits: 2.0.4
            string_decoder: 1.3.0
            util-deprecate: 1.0.2

    readdirp@3.6.0:
        dependencies:
            picomatch: 2.3.1

    reflect.getprototypeof@1.0.10:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.0.0
            get-intrinsic: 1.2.7
            get-proto: 1.0.1
            which-builtin-type: 1.2.1

    regenerator-runtime@0.14.1: {}

    regexp.prototype.flags@1.5.4:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-errors: 1.3.0
            get-proto: 1.0.1
            gopd: 1.2.0
            set-function-name: 2.0.2

    remark-frontmatter@4.0.1:
        dependencies:
            "@types/mdast": 3.0.15
            mdast-util-frontmatter: 1.0.1
            micromark-extension-frontmatter: 1.1.1
            unified: 10.1.2

    remark-mdx-frontmatter@1.1.1:
        dependencies:
            estree-util-is-identifier-name: 1.1.0
            estree-util-value-to-estree: 1.3.0
            js-yaml: 4.1.0
            toml: 3.0.0

    remark-mdx@2.3.0:
        dependencies:
            mdast-util-mdx: 2.0.1
            micromark-extension-mdxjs: 1.0.1
        transitivePeerDependencies:
            - supports-color

    remark-parse@10.0.2:
        dependencies:
            "@types/mdast": 3.0.15
            mdast-util-from-markdown: 1.3.1
            unified: 10.1.2
        transitivePeerDependencies:
            - supports-color

    remark-rehype@10.1.0:
        dependencies:
            "@types/hast": 2.3.10
            "@types/mdast": 3.0.15
            mdast-util-to-hast: 12.3.0
            unified: 10.1.2

    require-like@0.1.2: {}

    resolve-from@4.0.0: {}

    resolve-pkg-maps@1.0.0: {}

    resolve.exports@2.0.3: {}

    resolve@1.22.10:
        dependencies:
            is-core-module: 2.16.1
            path-parse: 1.0.7
            supports-preserve-symlinks-flag: 1.0.0

    resolve@2.0.0-next.5:
        dependencies:
            is-core-module: 2.16.1
            path-parse: 1.0.7
            supports-preserve-symlinks-flag: 1.0.0

    restore-cursor@3.1.0:
        dependencies:
            onetime: 5.1.2
            signal-exit: 3.0.7

    retry@0.12.0: {}

    reusify@1.0.4: {}

    rollup@4.30.1:
        dependencies:
            "@types/estree": 1.0.6
        optionalDependencies:
            "@rollup/rollup-android-arm-eabi": 4.30.1
            "@rollup/rollup-android-arm64": 4.30.1
            "@rollup/rollup-darwin-arm64": 4.30.1
            "@rollup/rollup-darwin-x64": 4.30.1
            "@rollup/rollup-freebsd-arm64": 4.30.1
            "@rollup/rollup-freebsd-x64": 4.30.1
            "@rollup/rollup-linux-arm-gnueabihf": 4.30.1
            "@rollup/rollup-linux-arm-musleabihf": 4.30.1
            "@rollup/rollup-linux-arm64-gnu": 4.30.1
            "@rollup/rollup-linux-arm64-musl": 4.30.1
            "@rollup/rollup-linux-loongarch64-gnu": 4.30.1
            "@rollup/rollup-linux-powerpc64le-gnu": 4.30.1
            "@rollup/rollup-linux-riscv64-gnu": 4.30.1
            "@rollup/rollup-linux-s390x-gnu": 4.30.1
            "@rollup/rollup-linux-x64-gnu": 4.30.1
            "@rollup/rollup-linux-x64-musl": 4.30.1
            "@rollup/rollup-win32-arm64-msvc": 4.30.1
            "@rollup/rollup-win32-ia32-msvc": 4.30.1
            "@rollup/rollup-win32-x64-msvc": 4.30.1
            fsevents: 2.3.3

    run-parallel@1.2.0:
        dependencies:
            queue-microtask: 1.2.3

    sade@1.8.1:
        dependencies:
            mri: 1.2.0

    safe-array-concat@1.1.3:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            get-intrinsic: 1.2.7
            has-symbols: 1.1.0
            isarray: 2.0.5

    safe-buffer@5.1.2: {}

    safe-buffer@5.2.1: {}

    safe-push-apply@1.0.0:
        dependencies:
            es-errors: 1.3.0
            isarray: 2.0.5

    safe-regex-test@1.1.0:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-regex: 1.2.1

    safer-buffer@2.1.2: {}

    scheduler@0.23.2:
        dependencies:
            loose-envify: 1.4.0

    secure-json-parse@3.0.2: {}

    semver@6.3.1: {}

    semver@7.6.3: {}

    send@0.19.0:
        dependencies:
            debug: 2.6.9
            depd: 2.0.0
            destroy: 1.2.0
            encodeurl: 1.0.2
            escape-html: 1.0.3
            etag: 1.8.1
            fresh: 0.5.2
            http-errors: 2.0.0
            mime: 1.6.0
            ms: 2.1.3
            on-finished: 2.4.1
            range-parser: 1.2.1
            statuses: 2.0.1
        transitivePeerDependencies:
            - supports-color

    serve-static@1.16.2:
        dependencies:
            encodeurl: 2.0.0
            escape-html: 1.0.3
            parseurl: 1.3.3
            send: 0.19.0
        transitivePeerDependencies:
            - supports-color

    set-cookie-parser@2.7.1: {}

    set-function-length@1.2.2:
        dependencies:
            define-data-property: 1.1.4
            es-errors: 1.3.0
            function-bind: 1.1.2
            get-intrinsic: 1.2.7
            gopd: 1.2.0
            has-property-descriptors: 1.0.2

    set-function-name@2.0.2:
        dependencies:
            define-data-property: 1.1.4
            es-errors: 1.3.0
            functions-have-names: 1.2.3
            has-property-descriptors: 1.0.2

    set-proto@1.0.0:
        dependencies:
            dunder-proto: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.0.0

    setprototypeof@1.2.0: {}

    shebang-command@2.0.0:
        dependencies:
            shebang-regex: 3.0.0

    shebang-regex@3.0.0: {}

    side-channel-list@1.0.0:
        dependencies:
            es-errors: 1.3.0
            object-inspect: 1.13.3

    side-channel-map@1.0.1:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            object-inspect: 1.13.3

    side-channel-weakmap@1.0.2:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            get-intrinsic: 1.2.7
            object-inspect: 1.13.3
            side-channel-map: 1.0.1

    side-channel@1.1.0:
        dependencies:
            es-errors: 1.3.0
            object-inspect: 1.13.3
            side-channel-list: 1.0.0
            side-channel-map: 1.0.1
            side-channel-weakmap: 1.0.2

    signal-exit@3.0.7: {}

    signal-exit@4.1.0: {}

    slash@3.0.0: {}

    source-map-js@1.2.1: {}

    source-map-support@0.5.21:
        dependencies:
            buffer-from: 1.1.2
            source-map: 0.6.1

    source-map@0.6.1: {}

    source-map@0.7.4: {}

    space-separated-tokens@2.0.2: {}

    spdx-correct@3.2.0:
        dependencies:
            spdx-expression-parse: 3.0.1
            spdx-license-ids: 3.0.20

    spdx-exceptions@2.5.0: {}

    spdx-expression-parse@3.0.1:
        dependencies:
            spdx-exceptions: 2.5.0
            spdx-license-ids: 3.0.20

    spdx-license-ids@3.0.20: {}

    ssri@10.0.6:
        dependencies:
            minipass: 7.1.2

    stable-hash@0.0.4: {}

    statuses@2.0.1: {}

    stream-shift@1.0.3: {}

    stream-slice@0.1.2: {}

    string-hash@1.1.3: {}

    string-width@4.2.3:
        dependencies:
            emoji-regex: 8.0.0
            is-fullwidth-code-point: 3.0.0
            strip-ansi: 6.0.1

    string-width@5.1.2:
        dependencies:
            eastasianwidth: 0.2.0
            emoji-regex: 9.2.2
            strip-ansi: 7.1.0

    string.prototype.includes@2.0.1:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-abstract: 1.23.9

    string.prototype.matchall@4.0.12:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-errors: 1.3.0
            es-object-atoms: 1.0.0
            get-intrinsic: 1.2.7
            gopd: 1.2.0
            has-symbols: 1.1.0
            internal-slot: 1.1.0
            regexp.prototype.flags: 1.5.4
            set-function-name: 2.0.2
            side-channel: 1.1.0

    string.prototype.repeat@1.0.0:
        dependencies:
            define-properties: 1.2.1
            es-abstract: 1.23.9

    string.prototype.trim@1.2.10:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-data-property: 1.1.4
            define-properties: 1.2.1
            es-abstract: 1.23.9
            es-object-atoms: 1.0.0
            has-property-descriptors: 1.0.2

    string.prototype.trimend@1.0.9:
        dependencies:
            call-bind: 1.0.8
            call-bound: 1.0.3
            define-properties: 1.2.1
            es-object-atoms: 1.0.0

    string.prototype.trimstart@1.0.8:
        dependencies:
            call-bind: 1.0.8
            define-properties: 1.2.1
            es-object-atoms: 1.0.0

    string_decoder@1.1.1:
        dependencies:
            safe-buffer: 5.1.2

    string_decoder@1.3.0:
        dependencies:
            safe-buffer: 5.2.1

    stringify-entities@4.0.4:
        dependencies:
            character-entities-html4: 2.1.0
            character-entities-legacy: 3.0.0

    strip-ansi@6.0.1:
        dependencies:
            ansi-regex: 5.0.1

    strip-ansi@7.1.0:
        dependencies:
            ansi-regex: 6.1.0

    strip-bom@3.0.0: {}

    strip-final-newline@2.0.0: {}

    strip-json-comments@3.1.1: {}

    style-to-object@0.4.4:
        dependencies:
            inline-style-parser: 0.1.1

    sucrase@3.35.0:
        dependencies:
            "@jridgewell/gen-mapping": 0.3.8
            commander: 4.1.1
            glob: 10.4.5
            lines-and-columns: 1.2.4
            mz: 2.7.0
            pirates: 4.0.6
            ts-interface-checker: 0.1.13

    supports-color@7.2.0:
        dependencies:
            has-flag: 4.0.0

    supports-preserve-symlinks-flag@1.0.0: {}

    tailwind-merge@2.6.0: {}

    tailwindcss-animate@1.0.7(tailwindcss@3.4.17):
        dependencies:
            tailwindcss: 3.4.17

    tailwindcss@3.4.17:
        dependencies:
            "@alloc/quick-lru": 5.2.0
            arg: 5.0.2
            chokidar: 3.6.0
            didyoumean: 1.2.2
            dlv: 1.1.3
            fast-glob: 3.3.3
            glob-parent: 6.0.2
            is-glob: 4.0.3
            jiti: 1.21.7
            lilconfig: 3.1.3
            micromatch: 4.0.8
            normalize-path: 3.0.0
            object-hash: 3.0.0
            picocolors: 1.1.1
            postcss: 8.4.49
            postcss-import: 15.1.0(postcss@8.4.49)
            postcss-js: 4.0.1(postcss@8.4.49)
            postcss-load-config: 4.0.2(postcss@8.4.49)
            postcss-nested: 6.2.0(postcss@8.4.49)
            postcss-selector-parser: 6.1.2
            resolve: 1.22.10
            sucrase: 3.35.0
        transitivePeerDependencies:
            - ts-node

    tapable@2.2.1: {}

    tar-fs@2.1.1:
        dependencies:
            chownr: 1.1.4
            mkdirp-classic: 0.5.3
            pump: 3.0.2
            tar-stream: 2.2.0

    tar-stream@2.2.0:
        dependencies:
            bl: 4.1.0
            end-of-stream: 1.4.4
            fs-constants: 1.0.0
            inherits: 2.0.4
            readable-stream: 3.6.2

    tar@6.2.1:
        dependencies:
            chownr: 2.0.0
            fs-minipass: 2.1.0
            minipass: 5.0.0
            minizlib: 2.1.2
            mkdirp: 1.0.4
            yallist: 4.0.0

    thenify-all@1.6.0:
        dependencies:
            thenify: 3.3.1

    thenify@3.3.1:
        dependencies:
            any-promise: 1.3.0

    through2@2.0.5:
        dependencies:
            readable-stream: 2.3.8
            xtend: 4.0.2

    to-regex-range@5.0.1:
        dependencies:
            is-number: 7.0.0

    toidentifier@1.0.1: {}

    toml@3.0.0: {}

    trim-lines@3.0.1: {}

    trough@2.2.0: {}

    ts-api-utils@1.4.3(typescript@5.6.3):
        dependencies:
            typescript: 5.6.3

    ts-api-utils@2.0.0(typescript@5.6.3):
        dependencies:
            typescript: 5.6.3

    ts-interface-checker@0.1.13: {}

    tsconfck@3.1.4(typescript@5.6.3):
        optionalDependencies:
            typescript: 5.6.3

    tsconfig-paths@3.15.0:
        dependencies:
            "@types/json5": 0.0.29
            json5: 1.0.2
            minimist: 1.2.8
            strip-bom: 3.0.0

    tsconfig-paths@4.2.0:
        dependencies:
            json5: 2.2.3
            minimist: 1.2.8
            strip-bom: 3.0.0

    tslib@2.8.1: {}

    turbo-stream@2.4.0: {}

    type-check@0.4.0:
        dependencies:
            prelude-ls: 1.2.1

    type-is@1.6.18:
        dependencies:
            media-typer: 0.3.0
            mime-types: 2.1.35

    typed-array-buffer@1.0.3:
        dependencies:
            call-bound: 1.0.3
            es-errors: 1.3.0
            is-typed-array: 1.1.15

    typed-array-byte-length@1.0.3:
        dependencies:
            call-bind: 1.0.8
            for-each: 0.3.3
            gopd: 1.2.0
            has-proto: 1.2.0
            is-typed-array: 1.1.15

    typed-array-byte-offset@1.0.4:
        dependencies:
            available-typed-arrays: 1.0.7
            call-bind: 1.0.8
            for-each: 0.3.3
            gopd: 1.2.0
            has-proto: 1.2.0
            is-typed-array: 1.1.15
            reflect.getprototypeof: 1.0.10

    typed-array-length@1.0.7:
        dependencies:
            call-bind: 1.0.8
            for-each: 0.3.3
            gopd: 1.2.0
            is-typed-array: 1.1.15
            possible-typed-array-names: 1.0.0
            reflect.getprototypeof: 1.0.10

    typescript-eslint@8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3):
        dependencies:
            "@typescript-eslint/eslint-plugin": 8.19.1(@typescript-eslint/parser@8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/parser": 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            "@typescript-eslint/utils": 8.19.1(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
            eslint: 9.17.0(jiti@1.21.7)
            typescript: 5.6.3
        transitivePeerDependencies:
            - supports-color

    typescript@5.6.3: {}

    ufo@1.5.4: {}

    unbox-primitive@1.1.0:
        dependencies:
            call-bound: 1.0.3
            has-bigints: 1.1.0
            has-symbols: 1.1.0
            which-boxed-primitive: 1.1.1

    undici-types@6.20.0: {}

    undici@6.21.0: {}

    unified@10.1.2:
        dependencies:
            "@types/unist": 2.0.11
            bail: 2.0.2
            extend: 3.0.2
            is-buffer: 2.0.5
            is-plain-obj: 4.1.0
            trough: 2.2.0
            vfile: 5.3.7

    unique-filename@3.0.0:
        dependencies:
            unique-slug: 4.0.0

    unique-slug@4.0.0:
        dependencies:
            imurmurhash: 0.1.4

    unist-util-generated@2.0.1: {}

    unist-util-is@5.2.1:
        dependencies:
            "@types/unist": 2.0.11

    unist-util-position-from-estree@1.1.2:
        dependencies:
            "@types/unist": 2.0.11

    unist-util-position@4.0.4:
        dependencies:
            "@types/unist": 2.0.11

    unist-util-remove-position@4.0.2:
        dependencies:
            "@types/unist": 2.0.11
            unist-util-visit: 4.1.2

    unist-util-stringify-position@3.0.3:
        dependencies:
            "@types/unist": 2.0.11

    unist-util-visit-parents@5.1.3:
        dependencies:
            "@types/unist": 2.0.11
            unist-util-is: 5.2.1

    unist-util-visit@4.1.2:
        dependencies:
            "@types/unist": 2.0.11
            unist-util-is: 5.2.1
            unist-util-visit-parents: 5.1.3

    universalify@2.0.1: {}

    unpipe@1.0.0: {}

    update-browserslist-db@1.1.1(browserslist@4.24.3):
        dependencies:
            browserslist: 4.24.3
            escalade: 3.2.0
            picocolors: 1.1.1

    uri-js@4.4.1:
        dependencies:
            punycode: 2.3.1

    use-callback-ref@1.3.3(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            react: 18.3.1
            tslib: 2.8.1
        optionalDependencies:
            "@types/react": 18.3.18

    use-composed-ref@1.4.0(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    use-isomorphic-layout-effect@1.2.0(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            react: 18.3.1
        optionalDependencies:
            "@types/react": 18.3.18

    use-latest@1.3.0(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            react: 18.3.1
            use-isomorphic-layout-effect: 1.2.0(@types/react@18.3.18)(react@18.3.1)
        optionalDependencies:
            "@types/react": 18.3.18

    use-sidecar@1.1.3(@types/react@18.3.18)(react@18.3.1):
        dependencies:
            detect-node-es: 1.1.0
            react: 18.3.1
            tslib: 2.8.1
        optionalDependencies:
            "@types/react": 18.3.18

    use-sound@4.0.3(react@18.3.1):
        dependencies:
            howler: 2.2.4
            react: 18.3.1

    util-deprecate@1.0.2: {}

    util@0.12.5:
        dependencies:
            inherits: 2.0.4
            is-arguments: 1.2.0
            is-generator-function: 1.1.0
            is-typed-array: 1.1.15
            which-typed-array: 1.1.18

    utils-merge@1.0.1: {}

    uvu@0.5.6:
        dependencies:
            dequal: 2.0.3
            diff: 5.2.0
            kleur: 4.1.5
            sade: 1.8.1

    valibot@0.41.0(typescript@5.6.3):
        optionalDependencies:
            typescript: 5.6.3

    validate-npm-package-license@3.0.4:
        dependencies:
            spdx-correct: 3.2.0
            spdx-expression-parse: 3.0.1

    validate-npm-package-name@5.0.1: {}

    vary@1.1.2: {}

    vfile-message@3.1.4:
        dependencies:
            "@types/unist": 2.0.11
            unist-util-stringify-position: 3.0.3

    vfile@5.3.7:
        dependencies:
            "@types/unist": 2.0.11
            is-buffer: 2.0.5
            unist-util-stringify-position: 3.0.3
            vfile-message: 3.1.4

    vite-node@1.6.0(@types/node@22.10.5):
        dependencies:
            cac: 6.7.14
            debug: 4.4.0
            pathe: 1.1.2
            picocolors: 1.1.1
            vite: 5.4.11(@types/node@22.10.5)
        transitivePeerDependencies:
            - "@types/node"
            - less
            - lightningcss
            - sass
            - sass-embedded
            - stylus
            - sugarss
            - supports-color
            - terser

    vite-tsconfig-paths@4.3.2(typescript@5.6.3)(vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0)):
        dependencies:
            debug: 4.4.0
            globrex: 0.1.2
            tsconfck: 3.1.4(typescript@5.6.3)
        optionalDependencies:
            vite: 6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0)
        transitivePeerDependencies:
            - supports-color
            - typescript

    vite@5.4.11(@types/node@22.10.5):
        dependencies:
            esbuild: 0.21.5
            postcss: 8.4.49
            rollup: 4.30.1
        optionalDependencies:
            "@types/node": 22.10.5
            fsevents: 2.3.3

    vite@6.0.7(@types/node@22.10.5)(jiti@1.21.7)(yaml@2.7.0):
        dependencies:
            esbuild: 0.24.2
            postcss: 8.4.49
            rollup: 4.30.1
        optionalDependencies:
            "@types/node": 22.10.5
            fsevents: 2.3.3
            jiti: 1.21.7
            yaml: 2.7.0

    wcwidth@1.0.1:
        dependencies:
            defaults: 1.0.4

    web-encoding@1.1.5:
        dependencies:
            util: 0.12.5
        optionalDependencies:
            "@zxing/text-encoding": 0.9.0

    web-streams-polyfill@3.3.3: {}

    which-boxed-primitive@1.1.1:
        dependencies:
            is-bigint: 1.1.0
            is-boolean-object: 1.2.1
            is-number-object: 1.1.1
            is-string: 1.1.1
            is-symbol: 1.1.1

    which-builtin-type@1.2.1:
        dependencies:
            call-bound: 1.0.3
            function.prototype.name: 1.1.8
            has-tostringtag: 1.0.2
            is-async-function: 2.1.0
            is-date-object: 1.1.0
            is-finalizationregistry: 1.1.1
            is-generator-function: 1.1.0
            is-regex: 1.2.1
            is-weakref: 1.1.0
            isarray: 2.0.5
            which-boxed-primitive: 1.1.1
            which-collection: 1.0.2
            which-typed-array: 1.1.18

    which-collection@1.0.2:
        dependencies:
            is-map: 2.0.3
            is-set: 2.0.3
            is-weakmap: 2.0.2
            is-weakset: 2.0.4

    which-typed-array@1.1.18:
        dependencies:
            available-typed-arrays: 1.0.7
            call-bind: 1.0.8
            call-bound: 1.0.3
            for-each: 0.3.3
            gopd: 1.2.0
            has-tostringtag: 1.0.2

    which@2.0.2:
        dependencies:
            isexe: 2.0.0

    which@3.0.1:
        dependencies:
            isexe: 2.0.0

    word-wrap@1.2.5: {}

    wrap-ansi@7.0.0:
        dependencies:
            ansi-styles: 4.3.0
            string-width: 4.2.3
            strip-ansi: 6.0.1

    wrap-ansi@8.1.0:
        dependencies:
            ansi-styles: 6.2.1
            string-width: 5.1.2
            strip-ansi: 7.1.0

    wrappy@1.0.2: {}

    ws@7.5.10: {}

    xtend@4.0.2: {}

    yallist@3.1.1: {}

    yallist@4.0.0: {}

    yaml@2.7.0: {}

    yocto-queue@0.1.0: {}

    zod-to-json-schema@3.24.1(zod@3.24.1):
        dependencies:
            zod: 3.24.1

    zod@3.24.1: {}

    zustand@5.0.3(@types/react@18.3.18)(react@18.3.1):
        optionalDependencies:
            "@types/react": 18.3.18
            react: 18.3.1

    zwitch@2.0.4: {}
