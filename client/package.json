{"name": "client", "private": true, "sideEffects": false, "type": "module", "scripts": {"extract-version": "bash ./version.sh", "dev": "pnpm run extract-version && vite", "build": "pnpm run extract-version && tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@elizaos/core": "workspace:*", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@react-spring/web": "^9.7.5", "@tanstack/react-query": "^5.63.0", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.1", "clsx": "2.1.1", "dayjs": "^1.11.13", "lucide-react": "^0.469.0", "react": "^19.0.0", "react-aiwriter": "^1.0.0", "react-dom": "^19.0.0", "react-router": "^7.1.1", "react-router-dom": "^7.1.1", "semver": "^7.6.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vite-plugin-compression": "^0.5.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.5", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.19", "eslint": "^9.17.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.38", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.4", "typescript": "~5.6.3", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-tsconfig-paths": "^5.1.4"}}