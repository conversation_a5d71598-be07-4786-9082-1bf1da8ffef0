// <PERSON>ript to manually initialize the transcription service
import { createNodePlugin } from './packages/plugin-node/dist/index.js';
import { ServiceType } from './packages/core/dist/index.js';

// Create a mock runtime for initialization
const mockRuntime = {
  getSetting: (key) => {
    if (key === 'TRANSCRIPTION_PROVIDER') return 'deepgram';
    if (key === 'DEEPGRAM_API_KEY') return 'lIwNHifet7liu9dfA3neFNvk4JKIiLgK';
    return null;
  },
  character: {
    settings: {
      voice: {
        transcriptionProvider: 'deepgram',
        elevenlabs: {
          voiceId: 'c6SfcYrb2t09NHXiT80T',
          model: 'eleven_multilingual_v2'
        }
      }
    }
  },
  services: new Map(),
  registerService: function(service) {
    console.log(`Registering service: ${service.constructor.name}`);
    this.services.set(service.serviceType, service);
  }
};

async function main() {
  try {
    // Create the node plugin
    const nodePlugin = createNodePlugin();
    console.log('Created node plugin:', nodePlugin.name);

    // Register services
    for (const service of nodePlugin.services) {
      mockRuntime.registerService(service);
    }

    // Initialize the transcription service
    const transcriptionService = mockRuntime.services.get(ServiceType.TRANSCRIPTION);

    if (!transcriptionService) {
      console.error('Transcription service not found after registration!');
      return;
    }

    console.log('Transcription service found:', transcriptionService.constructor.name);

    // Initialize the service
    await transcriptionService.initialize(mockRuntime);

    console.log('Transcription service initialized successfully');

    // List all registered services
    console.log('\nAll registered services:');
    for (const [type, service] of mockRuntime.services.entries()) {
      console.log(`- ${type}: ${service.constructor.name}`);
    }

  } catch (error) {
    console.error('Error testing transcription service:', error);
  }
}

main();
