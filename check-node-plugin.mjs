// Script to check if the Node plugin is properly loading and registering its services
import { createNodePlugin } from './packages/plugin-node/dist/index.js';
import { ServiceType } from './packages/core/dist/index.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a mock runtime for initialization
const mockRuntime = {
  getSetting: (key) => {
    if (key === 'TRANSCRIPTION_PROVIDER') return process.env.TRANSCRIPTION_PROVIDER;
    if (key === 'DEEPGRAM_API_KEY') return process.env.DEEPGRAM_API_KEY;
    if (key === 'ELEVENLABS_XI_API_KEY') return process.env.ELEVENLABS_XI_API_KEY;
    if (key === 'OPENAI_API_KEY') return process.env.OPENAI_API_KEY;
    return process.env[key];
  },
  character: {
    settings: {
      voice: {
        transcription: process.env.TRANSCRIPTION_PROVIDER,
        elevenlabs: {
          voiceId: process.env.ELEVENLABS_VOICE_ID,
          model: process.env.ELEVENLABS_MODEL_ID
        }
      }
    }
  },
  services: new Map(),
  registerService: function(service) {
    console.log(`Registering service: ${service.constructor.name}`);
    this.services.set(service.serviceType, service);
  },
  getService: function(serviceType) {
    return this.services.get(serviceType);
  }
};

async function main() {
  try {
    // Create the node plugin
    const nodePlugin = createNodePlugin();
    console.log('Created node plugin:', nodePlugin.name);
    
    // Log all services in the plugin
    console.log('\nServices in Node Plugin:');
    nodePlugin.services.forEach(service => {
      console.log(`- ${service.constructor.name}: ${service.serviceType}`);
    });
    
    // Register services
    console.log('\nRegistering services:');
    for (const service of nodePlugin.services) {
      mockRuntime.registerService(service);
    }
    
    // Check if services are registered
    console.log('\nChecking registered services:');
    console.log('Transcription service:', mockRuntime.getService(ServiceType.TRANSCRIPTION) ? 'Found' : 'Not found');
    console.log('Speech service:', mockRuntime.getService(ServiceType.SPEECH_GENERATION) ? 'Found' : 'Not found');
    console.log('Image description service:', mockRuntime.getService(ServiceType.IMAGE_DESCRIPTION) ? 'Found' : 'Not found');
    
    // Initialize the transcription service
    console.log('\nInitializing services:');
    const transcriptionService = mockRuntime.getService(ServiceType.TRANSCRIPTION);
    if (transcriptionService) {
      await transcriptionService.initialize(mockRuntime);
      console.log('Transcription service initialized');
    }
    
    const speechService = mockRuntime.getService(ServiceType.SPEECH_GENERATION);
    if (speechService) {
      await speechService.initialize(mockRuntime);
      console.log('Speech service initialized');
    }
    
    // List all registered services
    console.log('\nAll registered services:');
    for (const [type, service] of mockRuntime.services.entries()) {
      console.log(`- ${type}: ${service.constructor.name}`);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
