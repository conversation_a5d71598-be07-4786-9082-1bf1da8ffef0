Add more plugins
run npx elizaos plugins list to get a list of available plugins or visit https://elizaos.github.io/registry/

run npx elizaos plugins add @elizaos-plugins/plugin-NAME to install the plugin into your instance

Additional Requirements
You may need to install Sharp. If you see an error when starting up, try installing it with the following command:

pnpm install --include=optional sharp
Using Your Custom Plugins
Plugins that are not in the official registry for ElizaOS can be used as well. Here's how:

Installation
Upload the custom plugin to the packages folder:
packages/
├─plugin-example/
├── package.json
├── tsconfig.json
├── src/
│   ├── index.ts        # Main plugin entry
│   ├── actions/        # Custom actions
│   ├── providers/      # Data providers
│   ├── types.ts        # Type definitions
│   └── environment.ts  # Configuration
├── README.md
└── LICENSE
Add the custom plugin to your project's dependencies in the agent's package.json:
{
  "dependencies": {
    "@elizaos/plugin-example": "workspace:*"
  }
}
Import the custom plugin to your agent's character.json
  "plugins": [
    "@elizaos/plugin-example",
  ],
