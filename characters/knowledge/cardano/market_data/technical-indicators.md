# Technical Analysis Indicators for Crypto Markets

## Key Indicators

### Moving Averages
- **Simple Moving Average (SMA)**: Calculates the average price over a specified period
- **Exponential Moving Average (EMA)**: Places more weight on recent prices
- **Moving Average Convergence Divergence (MACD)**: Shows the relationship between two EMAs of a price

### Oscillators
- **Relative Strength Index (RSI)**: Measures the speed and change of price movements on a scale of 0-100
  - Above 70: Potentially overbought
  - Below 30: Potentially oversold
- **Stochastic Oscillator**: Compares a closing price to its price range over a specific period
- **Bollinger Bands**: Shows volatility with upper and lower bands around an SMA

### Volume Indicators
- **On-Balance Volume (OBV)**: Relates volume to price change
- **Volume Profile**: Shows trading activity at specific price levels
- **Chaikin Money Flow (CMF)**: Measures buying and selling pressure

## Patterns
- **Support and Resistance**: Price levels where trends typically reverse
- **Chart Patterns**: Formations like Head and Shoulders, Double Top/Bottom, Triangles
- **Candlestick Patterns**: Doji, Hammer, Engulfing patterns, Morning/Evening Star

## Crypto-Specific Indicators
- **MVRV Ratio (Market Value to Realized Value)**: Assesses if price is above or below "fair value"
- **NVT Ratio (Network Value to Transactions)**: Similar to P/E ratio for crypto
- **SOPR (Spent Output Profit Ratio)**: Indicates whether holders are selling at profit or loss
- **Exchange Inflow/Outflow**: Tracks movement of assets to and from exchanges

## Timeframe Considerations
- **Multiple Timeframe Analysis**: Analyzing different time intervals for confirmation
- **Crypto Market Cycles**: Typically 3-4 year cycles related to Bitcoin halving events

## Common Technical Analysis Mistakes
- Over-reliance on a single indicator
- Ignoring market fundamentals
- Not adapting to crypto market's 24/7 nature
- Neglecting risk management

## Best Practices for Cardano Analysis
- Focus on staking metrics and delegation patterns
- Monitor smart contract deployment and transaction volumes
- Track development activity through GitHub commits
- Analyze on-chain data like wallet growth and transaction sizes 