# MISTER Memory Management System

This directory contains the knowledge base and memory management system for the MISTER character. The system is designed to enhance MISTER's ability to remember information and provide more relevant and contextualized responses.

## Directory Structure

```
knowledge/
├── cardano/                  # Cardano-specific knowledge
│   └── basics.md            # Basic information about Cardano
├── market-analysis/         # Market analysis knowledge
│   └── technical-indicators.md # Information about technical analysis indicators
├── trading-strategies/      # Trading strategy knowledge
│   └── risk-management.md   # Information about risk management in trading
├── conversation-summaries/  # Automatically generated conversation summaries
├── consolidated/            # Periodically consolidated knowledge summaries
├── MASTRAMISTERX.md         # Core knowledge about MISTER X
├── memory-consolidation.js  # Script for knowledge consolidation
├── memory-test.js           # Script for testing memory retrieval
├── conversation-summarizer.js # Script for conversation summarization
└── README.md                # This file
```

## Features

### 1. RAG Knowledge Management

The Retrieval-Augmented Generation (RAG) system allows MISTER to:
- Store information in structured markdown files
- Organize knowledge by topic in separate directories
- Automatically integrate new information learned during conversations
- Retrieve relevant information based on conversation context

### 2. Memory Consolidation

The system periodically consolidates knowledge to:
- Summarize related information across different files
- Prioritize the most important facts
- Reduce redundancy
- Create higher-level understanding of topics

To run the consolidation process manually:
```
node memory-consolidation.js
```

### 3. Conversation Summarization

After conversations reach a certain length, the system:
- Automatically summarizes the key points
- Categorizes information by topic
- Stores summaries for future reference
- Extracts important questions and answers

To run the summarization process manually:
```
node conversation-summarizer.js
```

### 4. Memory Testing

The memory test system verifies:
- That knowledge can be accurately retrieved
- That relevant information is found based on queries
- That the memory system is functioning as expected

To run memory tests:
```
node memory-test.js
```

## Memory Settings

MISTER has the following memory settings configured:

- **messageRetention**: 100 messages retained in immediate memory
- **summarizeEvery**: Conversations are summarized every 20 messages
- **prioritizeTopics**: ["Cardano", "DeFi", "Market Analysis"]
- **knowledgeConsolidation**: true (periodic consolidation enabled)
- **topicWeights**: Importance weighting for different topics:
  - Cardano: 0.8
  - Trading: 0.7
  - Market Analysis: 0.6
  - DeFi: 0.5

## Adding New Knowledge

To add new knowledge to the system:

1. Create a markdown file in the appropriate topic directory
2. Use clear headers (## for main sections, ### for subsections)
3. Use bullet points (- ) for key facts
4. Run the memory test to verify retrieval
5. Rebuild the project to apply changes

## How It Works with Perplexity

When MISTER uses Perplexity search:

1. New information retrieved from the web is automatically stored in the knowledge base
2. Citations are captured and stored with source URLs
3. This information becomes available for future conversations
4. The memory system prioritizes this stored knowledge based on relevance

## Future Enhancements

Planned enhancements to the memory system include:

- Integration with vector databases for more efficient retrieval
- Automatic knowledge graph generation
- Temporal awareness (understanding when information was learned)
- Self-correction of outdated information
- Confidence scoring for different knowledge items 