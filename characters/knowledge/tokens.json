{"metadata": {"description": "Comprehensive database of Cardano blockchain native tokens with policy IDs and unit identifiers", "lastUpdated": "2025-03-15", "version": "2.3.1", "totalTokens": 60, "categories": ["<PERSON><PERSON><PERSON>", "Memecoins", "Stablecoins", "Utility", "Governance", "NFT", "Gaming", "Infrastructure"]}, "cardanoTokens": [{"symbol": "ADA", "name": "Cardano", "policyId": "lovelace", "category": "Infrastructure", "description": "Native cryptocurrency of the Cardano blockchain", "foundationToken": true, "tags": ["layer1", "smartContract", "proofOfStake"]}, {"symbol": "MISTER", "name": "MISTER", "policyId": "7529bed52d81a20e69c6dd447dd9cc0293daf4577f08d7ed2d8ab081", "unit": "7529bed52d81a20e69c6dd447dd9cc0293daf4577f08d7ed2d8ab0814d4953544552", "category": "Utility", "description": "Token associated with Parasite Network analytics tools", "tags": ["analytics", "community", "tools"]}, {"symbol": "SNEK", "name": "Snek", "policyId": "f43a62fdc3965df486de8a0d32fe800963589c41b38946602a0dc535", "unit": "279c909f348e533da5808898f87f9a14bb2c3dfbbacccd631d927a3f534e454b", "category": "Memecoins", "description": "First Cardano memecoin listed on Kraken exchange", "exchanges": ["<PERSON><PERSON><PERSON>", "Minswap", "SundaeSwap"], "tags": ["memecoin", "tier1Listed", "communityDriven"]}, {"symbol": "WMT", "name": "World Mobile Token", "policyId": "1d7f33bd23d85e1a25d87d86fac4f199c3197a2f7afeb662a0f34e1e", "unit": "1d7f33bd23d85e1a25d87d86fac4f199c3197a2f7afeb662a0f34e1e776f726c646d6f62696c65746f6b656e", "category": "Utility", "description": "Token supporting World Mobile's decentralized telecommunications network", "tags": ["telecommunications", "realWorldAsset", "africa"]}, {"symbol": "WMTX", "name": "World Mobile Token X", "policyId": "e5a42a1a1d3d1da71b0449663c32798725888d2eb0843c4dabeca05a", "unit": "e5a42a1a1d3d1da71b0449663c32798725888d2eb0843c4dabeca05a576f726c644d6f62696c65546f6b656e58"}, {"symbol": "IAG", "name": "<PERSON><PERSON>", "policyId": "5d16cc1a177b5d9ba9cfa9793b07e60f1fb70fea1f8aef064415d114", "unit": "5d16cc1a177b5d9ba9cfa9793b07e60f1fb70fea1f8aef064415d114494147"}, {"symbol": "AGIX", "name": "SingularityNET", "policyId": "f43a62fdc3965df486de8a0d32fe800963589c41b38946602a0dc535", "unit": "f43a62fdc3965df486de8a0d32fe800963589c41b38946602a0dc53541474958", "category": "Utility", "description": "Token for AI marketplace and decentralized AI services", "tags": ["artificialIntelligence", "marketplace", "multichain"]}, {"symbol": "LQ", "name": "Liqwid Finance", "policyId": "da8c30857834c6ae7203935b89278c532b3995245295456f993e1d24", "unit": "da8c30857834c6ae7203935b89278c532b3995245295456f993e1d244c51"}, {"symbol": "MIN", "name": "Minswap", "policyId": "29d222ce763455e3d7a09a665ce554f00ac89d2e99a1a83d267170c6", "unit": "29d222ce763455e3d7a09a665ce554f00ac89d2e99a1a83d267170c64d494e", "category": "<PERSON><PERSON><PERSON>", "description": "Governance token for Minswap DEX on Cardano", "tags": ["dex", "governance", "yield"]}, {"symbol": "HOSKY", "name": "Hosky Token", "policyId": "a0028f350aaabe0545fdcb56b039bfb08e4bb4d8c4d7c3c7d481c235", "unit": "a0028f350aaabe0545fdcb56b039bfb08e4bb4d8c4d7c3c7d481c235484f534b59", "category": "Memecoins", "description": "Popular community-driven meme token with no utility", "tags": ["memecoin", "communityDriven", "zeroUtility"]}, {"symbol": "SHEN", "name": "Shen MicroUSD", "policyId": "8db269c3ec630e06ae29f74bc39edd1f87c819f1056206e879a1cd61", "unit": "8db269c3ec630e06ae29f74bc39edd1f87c819f1056206e879a1cd615368656e4d6963726f555344", "category": "<PERSON><PERSON><PERSON>", "description": "Reserve coin for the Djed stablecoin ecosystem", "tags": ["reserveCoin", "djed", "stablecoinBackup"]}, {"symbol": "INDY", "name": "Indigo Protocol", "policyId": "533bb94a8850ee3ccbe483106489399112b74c905342cb1792a797a0", "unit": "533bb94a8850ee3ccbe483106489399112b74c905342cb1792a797a0494e4459", "category": "<PERSON><PERSON><PERSON>", "description": "Governance token for Indigo Protocol synthetic assets platform", "tags": ["governance", "syntheticAssets", "defi"]}, {"symbol": "COPI", "name": "Cornucopias", "policyId": "b6a7467ea1deb012808ef4e87b5ff371e85f7142d7b356a40d9b42a0", "unit": "b6a7467ea1deb012808ef4e87b5ff371e85f7142d7b356a40d9b42a0436f726e75636f70696173205b76696120436861696e506f72742e696f5d"}, {"symbol": "STUFF", "name": "BOOK", "policyId": "51a5e236c4de3af2b8020442e2a26f454fda3b04cb621c1294a0ef34", "unit": "51a5e236c4de3af2b8020442e2a26f454fda3b04cb621c1294a0ef34424f4f4b"}, {"symbol": "LENFI", "name": "<PERSON><PERSON>", "policyId": "8fef2d34078659493ce161a6c7fba4b56afefa8535296a5743f69587", "unit": "8fef2d34078659493ce161a6c7fba4b56afefa8535296a5743f6958741414441"}, {"symbol": "NTX", "name": "Nutrinex", "policyId": "edfd7a1d77bcb8b884c474bdc92a16002d1fb720e454fa6e9934447", "unit": "edfd7a1d77bcb8b884c474bdc92a16002d1fb720e454fa6e993444794e5458"}, {"symbol": "NVL", "name": "Novel", "policyId": "5b26e685cc5c9ad630bde3e3cd48c694436671f3d25df53777ca60ef", "unit": "5b26e685cc5c9ad630bde3e3cd48c694436671f3d25df53777ca60ef4e564c"}, {"symbol": "USDM", "name": "USDM Stablecoin", "policyId": "c48cbb3d5e57ed56e276bc45f99ab39abe94e6cd7ac39fb402da47ad", "unit": "c48cbb3d5e57ed56e276bc45f99ab39abe94e6cd7ac39fb402da47ad0014df105553444d"}, {"symbol": "FLDT", "name": "FLDT", "policyId": "577f0b1342f8f8f4aed3388b80a8535812950c7a892495c0ecdf0f1e", "unit": "577f0b1342f8f8f4aed3388b80a8535812950c7a892495c0ecdf0f1e0014df10464c4454"}, {"symbol": "HUNT", "name": "HUNT", "policyId": "95a427e384527065f2f8946f5e86320d0117839a5e98ea2c0b55fb00", "unit": "95a427e384527065f2f8946f5e86320d0117839a5e98ea2c0b55fb0048554e54"}, {"symbol": "SUNDAE", "name": "SundaeSwap", "policyId": "9a9693a9a37912a5097918f97918d15240c92ab729a0b7c4aa144d77", "unit": "9a9693a9a37912a5097918f97918d15240c92ab729a0b7c4aa144d7753554e444145"}, {"symbol": "NMKR", "name": "NMKR", "policyId": "5dac8536653edc12f6f5e1045d8164b9f59998d3bdc300fc9284348", "unit": "5dac8536653edc12f6f5e1045d8164b9f59998d3bdc300fc928434894e4d4b52"}, {"symbol": "iUSD", "name": "iUSD", "policyId": "f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b69880", "unit": "f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b6988069555344", "category": "Stablecoins", "description": "USD-pegged stablecoin from the Indigo Protocol", "tags": ["stablecoin", "synthetic", "indigoProtocol"]}, {"symbol": "TALOS", "name": "<PERSON><PERSON>", "policyId": "97bbb7db0baef89caefce61b8107ac74c7a7340166b39d906f174bec", "unit": "97bbb7db0baef89caefce61b8107ac74c7a7340166b39d906f174bec54616c6f73"}, {"symbol": "BTN", "name": "BTN", "policyId": "016be5325fd988fea98ad422fcfd53e5352cacfced5c106a932a35a4", "unit": "016be5325fd988fea98ad422fcfd53e5352cacfced5c106a932a35a442544e"}, {"symbol": "SPLASH", "name": "SPLASH", "policyId": "ececc92aeaaac1f5b665f567b01baec8bc2771804b4c21716a87a4e3", "unit": "ececc92aeaaac1f5b665f567b01baec8bc2771804b4c21716a87a4e353504c415348"}, {"symbol": "USDA", "name": "USDA", "policyId": "fe7c786ab321f41c654ef6c1af7b3250a613c24e4213e0425a7ae456", "unit": "fe7c786ab321f41c654ef6c1af7b3250a613c24e4213e0425a7ae45655534441"}, {"symbol": "TET", "name": "TET", "policyId": "6947eccc74ebf8c1716339b97af768bfbc70d330a743b79bbc5ccdeb", "unit": "6947eccc74ebf8c1716339b97af768bfbc70d330a743b79bbc5ccdeb544554"}, {"symbol": "DJED", "name": "<PERSON><PERSON><PERSON>", "policyId": "8db269c3ec630e06ae29f74bc39edd1f87c819f1056206e879a1cd61", "unit": "8db269c3ec630e06ae29f74bc39edd1f87c819f1056206e879a1cd61446a65644d6963726f555344", "category": "Stablecoins", "description": "Overcollateralized algorithmic stablecoin pegged to USD", "tags": ["stablecoin", "algorithmic", "usdPegged"]}, {"symbol": "CHAD", "name": "<PERSON> the <PERSON>", "policyId": "97075bf380e65f3c63fb733267adbb7d42eec574428a754d2abca55b", "unit": "97075bf380e65f3c63fb733267adbb7d42eec574428a754d2abca55b436861726c6573207468652043686164", "category": "Memecoins", "description": "<PERSON>-themed memecoin with strong community support", "tags": ["memecoin", "founderThemed", "communityDriven"]}, {"symbol": "O", "name": "Ocean Token", "policyId": "2852268cf6e2db42e20f2fd3125f541e5d6c5a3d70b4dda17c2daa82", "unit": "2852268cf6e2db42e20f2fd3125f541e5d6c5a3d70b4dda17c2daa82"}, {"symbol": "TITAN", "name": "TITAN", "policyId": "8483844875ce4d61c2aa459240f277d32081ee08fe0ad16899a0f581", "unit": "8483844875ce4d61c2aa459240f277d32081ee08fe0ad16899a0f5810014df10544954414e"}, {"symbol": "MNT", "name": "<PERSON><PERSON><PERSON>", "policyId": "43b07d4037f0d75ee10f9863097463fc02ff3c0b8b705ae61d9c75bf", "unit": "43b07d4037f0d75ee10f9863097463fc02ff3c0b8b705ae61d9c75bf4d796e746820546f6b656e"}, {"symbol": "RJV", "name": "RJV", "policyId": "8cfd6893f5f6c1cc954cec1a0a1460841b74da6e7803820dde62bb78", "unit": "8cfd6893f5f6c1cc954cec1a0a1460841b74da6e7803820dde62bb78524a56"}, {"symbol": "COCK", "name": "COCK", "policyId": "49e423161ef818adc475c783571cb479d5f15ad52a01a240eacc0d3b", "unit": "49e423161ef818adc475c783571cb479d5f15ad52a01a240eacc0d3b434f434b"}, {"symbol": "rsERG", "name": "rsERG", "policyId": "04b95368393c821f180deee8229fbd941baaf9bd748ebcdbf7adbb14", "unit": "04b95368393c821f180deee8229fbd941baaf9bd748ebcdbf7adbb147273455247"}, {"symbol": "BODEGA", "name": "BODEGA", "policyId": "5deab590a137066fef0e56f06ef1b830f21bc5d544661ba570bdd2ae", "unit": "5deab590a137066fef0e56f06ef1b830f21bc5d544661ba570bdd2ae424f44454741"}, {"symbol": "FLAC", "name": "FLAC", "policyId": "8daefa391220bd0d8d007f3748d870f7f3c106040314c8515ccc35a5", "unit": "8daefa391220bd0d8d007f3748d870f7f3c106040314c8515ccc35a5464c4143"}, {"symbol": "MILK", "name": "MILK", "policyId": "afbe91c0b44b3040e360057bf8354ead8c49c4979ae6ab7c4fbdc9eb", "unit": "afbe91c0b44b3040e360057bf8354ead8c49c4979ae6ab7c4fbdc9eb4d494c4b7632"}, {"symbol": "NEWM", "name": "NEWM", "policyId": "682fe60c9918842b3323c43b5144bc3d52a23bd2fb81345560d73f63", "unit": "682fe60c9918842b3323c43b5144bc3d52a23bd2fb81345560d73f634e45574d"}, {"symbol": "MyUSD", "name": "MyUSD", "policyId": "92776616f1f32c65a173392e4410a3d8c39dcf6ef768c73af164779c", "unit": "92776616f1f32c65a173392e4410a3d8c39dcf6ef768c73af164779c4d79555344"}, {"symbol": "VYFI", "name": "VYFI", "policyId": "804f5544c1962a40546827cab750a88404dc7108c0f588b72964754f", "unit": "804f5544c1962a40546827cab750a88404dc7108c0f588b72964754f56594649"}, {"symbol": "STRIKE", "name": "STRIKE", "policyId": "f13ac4d66b3ee19a6aa0f2a22298737bd907cc95121662fc971b5275", "unit": "f13ac4d66b3ee19a6aa0f2a22298737bd907cc95121662fc971b5275535452494b45"}, {"symbol": "C3", "name": "CHARLI3", "policyId": "8e51398904a5d3fc129fbf4f1589701de23c7824d5c90fdb9490e15a", "unit": "8e51398904a5d3fc129fbf4f1589701de23c7824d5c90fdb9490e15a434841524c4933"}, {"symbol": "iBTC", "name": "iBTC", "policyId": "f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b69880", "unit": "f66d78b4a3cb3d37afa0ec36461e51ecbde00f26c8f0a68f94b6988069425443"}, {"symbol": "rsRSN", "name": "rsRSN", "policyId": "04b95368393c821f180deee8229fbd941baaf9bd748ebcdbf7adbb14", "unit": "04b95368393c821f180deee8229fbd941baaf9bd748ebcdbf7adbb14727352534e"}, {"symbol": "BBSNEK", "name": "BabySNEK", "policyId": "7507734918533b3b896241b4704f3d4ce805256b01da6fcede430436", "unit": "7507734918533b3b896241b4704f3d4ce805256b01da6fcede43043642616279534e454b"}, {"symbol": "NIKEPIG", "name": "Nike", "policyId": "c881c20e49dbaca3ff6cef365969354150983230c39520b917f5cf7c", "unit": "c881c20e49dbaca3ff6cef365969354150983230c39520b917f5cf7c4e696b65"}, {"symbol": "DEDI", "name": "DEDI", "policyId": "64f7b108bd43f4bde344b82587655eeb821256c0c8e79ad48db15d18", "unit": "64f7b108bd43f4bde344b82587655eeb821256c0c8e79ad48db15d1844454449"}, {"symbol": "WRT", "name": "WingRiders", "policyId": "c0ee29a85b13209423b10447d3c2e6a50641a15c57770e27cb9d5073", "unit": "c0ee29a85b13209423b10447d3c2e6a50641a15c57770e27cb9d507357696e67526964657273"}, {"symbol": "PBG", "name": "PBG", "policyId": "03b6ddacd60cc1ebd9ed4041d0c298c4c6f48ab61e04fdad4d915cfa", "unit": "03b6ddacd60cc1ebd9ed4041d0c298c4c6f48ab61e04fdad4d915cfa0014df10504247"}, {"symbol": "FACT", "name": "Forcfaxtoken", "policyId": "a3931691f5c4e65d01c429e473d0dd24c51afdb6daf88e632a6c1e51", "unit": "a3931691f5c4e65d01c429e473d0dd24c51afdb6daf88e632a6c1e516f7263666178746f6b656e"}, {"symbol": "CLARITY", "name": "Clarity DAO Token", "policyId": "1e76aaec4869308ef5b61e81ebf229f2e70f75a50223defa087f807b", "unit": "1e76aaec4869308ef5b61e81ebf229f2e70f75a50223defa087f807b436c61726974792044414f20546f6b656e"}, {"symbol": "XER", "name": "XER", "policyId": "6d06570ddd778ec7c0cca09d381eca194e90c8cffa7582879735dbde", "unit": "6d06570ddd778ec7c0cca09d381eca194e90c8cffa7582879735dbde584552"}, {"symbol": "SHARDS", "name": "Shards", "policyId": "ea153b5d4864af15a1079a94a0e2486d6376fa28aafad272d15b243a", "unit": "ea153b5d4864af15a1079a94a0e2486d6376fa28aafad272d15b243a0014df10536861726473"}, {"symbol": "BANK", "name": "BANK", "policyId": "2b28c81dbba6d67e4b5a997c6be1212cba9d60d33f82444ab8b1f218", "unit": "2b28c81dbba6d67e4b5a997c6be1212cba9d60d33f82444ab8b1f21842414e4b"}, {"symbol": "ANGELS", "name": "ANGELS", "policyId": "285b65ae63d4fad36321384ec61edfd5187b8194fff89b5abe9876da", "unit": "285b65ae63d4fad36321384ec61edfd5187b8194fff89b5abe9876da414e47454c53"}, {"symbol": "GENS", "name": "GENS", "policyId": "dda5fdb1002f7389b33e036b6afee82a8189becb6cba852e8b79b4fb", "unit": "dda5fdb1002f7389b33e036b6afee82a8189becb6cba852e8b79b4fb0014df1047454e53"}, {"symbol": "ROLL", "name": "ROLL", "policyId": "9d05077f2e6dcb17915f9b99bc443726939dd2047aafd6cb4a6fe017", "unit": "9d05077f2e6dcb17915f9b99bc443726939dd2047aafd6cb4a6fe017524f4c4c"}]}