// Conversation Summarization Script for MISTER
// This script demonstrates how conversations can be periodically summarized
// and stored for future reference

const fs = require('fs');
const path = require('path');

// Configuration
const KNOWLEDGE_BASE_DIR = path.join(__dirname);
const SUMMARIES_DIR = path.join(KNOWLEDGE_BASE_DIR, 'conversation-summaries');
const SUMMARY_THRESHOLD = 20; // Number of messages before summarizing

// Ensure summaries directory exists
if (!fs.existsSync(SUMMARIES_DIR)) {
  fs.mkdirSync(SUMMARIES_DIR, { recursive: true });
}

// Sample conversation to summarize
const sampleConversation = [
  { role: 'user', timestamp: '2025-03-01T10:00:00Z', content: "Hey MISTER, what's happening with the Cardano ecosystem lately?" },
  { role: 'assistant', timestamp: '2025-03-01T10:00:15Z', content: "cardano's been quite active recently with several developments. the ecosystem is seeing increased transaction volume, particularly on dexes like minswap and sundaeswap. there's also growing interest in the native token space, especially with memecoins like snek and talos gaining traction. the hydra scaling solution is progressing well, which should address throughput as adoption grows." },
  { role: 'user', timestamp: '2025-03-01T10:02:30Z', content: "That sounds interesting. What about SNEK specifically? I heard it was listed on a major exchange?" },
  { role: 'assistant', timestamp: '2025-03-01T10:02:55Z', content: "yes, snek secured a tier 1 exchange listing on kraken recently - significant milestone for cardano memecoins. the team is actively pursuing additional tier 1 listings which could further boost liquidity and exposure. community remains highly engaged, maintaining snek's position as one of the dominant tokens by volume on cardano. the listing reinforces the growing legitimacy of cardano's native asset ecosystem." },
  { role: 'user', timestamp: '2025-03-01T10:05:10Z', content: "Cool! How's the technical analysis looking for SNEK?" },
  { role: 'assistant', timestamp: '2025-03-01T10:05:45Z', content: "snek's chart shows interesting patterns post-kraken listing. volume profile indicates strong accumulation at current levels. rsi sitting around 65 - bullish but not yet overbought. the 30-day moving average recently crossed above the 90-day ma, forming a golden cross pattern that historically signals uptrend continuation. volatility has decreased since the initial listing spike, suggesting price stabilization. key resistance levels to watch at the previous all-time high." },
  { role: 'user', timestamp: '2025-03-01T10:08:20Z', content: "Thanks for the analysis. What risk management approach would you suggest if I want to invest in SNEK?" },
  { role: 'assistant', timestamp: '2025-03-01T10:09:00Z', content: "for snek, consider these risk management approaches: 1) position sizing - limit exposure to 1-2% of your portfolio given memecoin volatility. 2) set clear stop losses at technical support levels, likely 15-20% below entry for memecoins. 3) consider a tiered entry strategy instead of all-in. 4) for profit-taking, scale out at predetermined targets rather than trying to catch the exact top. 5) maintain awareness of cardano epoch transitions when liquidity might change. remember that native tokens require extra considerations around liquidity compared to ada itself." }
];

// Function to summarize conversation
function summarizeConversation(conversation, topicFilter = null) {
  // First, filter by topic if provided
  const filteredConversation = topicFilter 
    ? conversation.filter(msg => 
        msg.content.toLowerCase().includes(topicFilter.toLowerCase()))
    : conversation;
  
  if (filteredConversation.length === 0) {
    return `No conversation found about ${topicFilter}`;
  }
  
  // Extract key information
  const startTime = new Date(filteredConversation[0].timestamp);
  const endTime = new Date(filteredConversation[filteredConversation.length - 1].timestamp);
  const duration = Math.round((endTime - startTime) / 60000); // duration in minutes
  
  // Extract main topics
  const allText = filteredConversation.map(msg => msg.content).join(' ');
  const mainTopics = extractMainTopics(allText);
  
  // Generate summary
  let summary = `# Conversation Summary\n\n`;
  summary += `**Date:** ${startTime.toLocaleDateString()}\n`;
  summary += `**Duration:** ${duration} minutes\n`;
  summary += `**Participants:** User and MISTER\n\n`;
  
  summary += `## Main Topics Discussed\n`;
  mainTopics.forEach(topic => {
    summary += `- ${topic}\n`;
  });
  
  summary += `\n## Key Points\n`;
  
  // Extract key points from assistant responses
  const assistantResponses = filteredConversation
    .filter(msg => msg.role === 'assistant');
  
  const keyPoints = extractKeyPoints(assistantResponses);
  keyPoints.forEach(point => {
    summary += `- ${point}\n`;
  });
  
  // Add user questions
  const userQuestions = filteredConversation
    .filter(msg => msg.role === 'user')
    .map(msg => msg.content);
  
  summary += `\n## User Questions\n`;
  userQuestions.forEach(question => {
    summary += `- ${question}\n`;
  });
  
  return summary;
}

// Function to extract main topics from text
function extractMainTopics(text) {
  // In a real implementation, this would use NLP
  // This is a simplified version using keyword matching
  
  const keywords = {
    'cardano': 'Cardano Ecosystem',
    'snek': 'SNEK Token',
    'kraken': 'Kraken Exchange Listing',
    'technical': 'Technical Analysis',
    'risk': 'Risk Management',
    'dex': 'Decentralized Exchanges'
  };
  
  const topics = [];
  
  Object.keys(keywords).forEach(keyword => {
    if (text.toLowerCase().includes(keyword)) {
      topics.push(keywords[keyword]);
    }
  });
  
  return [...new Set(topics)]; // Remove duplicates
}

// Function to extract key points from assistant responses
function extractKeyPoints(responses) {
  const allText = responses.map(r => r.content).join(' ');
  
  // Split into sentences (simplified)
  const sentences = allText.split(/[.!?]+/)
    .map(s => s.trim())
    .filter(s => s.length > 20); // Filter out short sentences
  
  // In a real implementation, you'd use NLP to identify important sentences
  // For this demo, we'll take sentences with keywords
  
  const importantKeywords = [
    'cardano', 'ecosystem', 'snek', 'kraken', 'listing', 'tier 1',
    'technical analysis', 'volume', 'risk management', 'position sizing',
    'rsi', 'moving average', 'golden cross', 'resistance', 'support'
  ];
  
  const keyPoints = [];
  
  sentences.forEach(sentence => {
    const sentenceLower = sentence.toLowerCase();
    const keywordMatches = importantKeywords.filter(keyword => 
      sentenceLower.includes(keyword.toLowerCase())
    );
    
    if (keywordMatches.length > 0) {
      keyPoints.push(sentence.charAt(0).toUpperCase() + sentence.slice(1));
    }
  });
  
  // Return unique points, limited to top 5
  return [...new Set(keyPoints)].slice(0, 5);
}

// Function to save summary
function saveSummary(summary, topic = 'general') {
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  const filename = `${timestamp}-${topic}-summary.md`;
  const filePath = path.join(SUMMARIES_DIR, filename);
  
  fs.writeFileSync(filePath, summary);
  
  return filePath;
}

// Function to process conversation and generate summaries
function processConversation(conversation) {
  console.log(`Processing conversation with ${conversation.length} messages...`);
  
  // Generate a general summary
  const generalSummary = summarizeConversation(conversation);
  const generalSummaryPath = saveSummary(generalSummary);
  console.log(`General summary saved to: ${generalSummaryPath}`);
  
  // Generate topic-specific summaries
  const topics = ['Cardano', 'SNEK', 'Technical Analysis', 'Risk Management'];
  
  topics.forEach(topic => {
    const topicSummary = summarizeConversation(conversation, topic);
    const topicSummaryPath = saveSummary(topicSummary, topic.toLowerCase().replace(' ', '-'));
    console.log(`${topic} summary saved to: ${topicSummaryPath}`);
  });
}

// Simulate periodic summarization
function simulatePeriodicSummarization() {
  console.log('Simulating periodic conversation summarization...');
  console.log('-------------------------------------------------');
  
  // Process conversation after reaching threshold
  if (sampleConversation.length >= SUMMARY_THRESHOLD) {
    console.log(`Conversation reached threshold of ${SUMMARY_THRESHOLD} messages.`);
    processConversation(sampleConversation);
  } else {
    console.log(`Conversation has ${sampleConversation.length} messages, below threshold of ${SUMMARY_THRESHOLD}.`);
  }
  
  // In a real implementation, this would be called periodically
  // or when a conversation reaches the threshold
  console.log('\nNote: In a production environment, this would be triggered:');
  console.log('1. After a conversation reaches a certain length');
  console.log('2. When a conversation has been inactive for a set period');
  console.log('3. On a scheduled basis (e.g., daily)');
}

// Run if directly executed
if (require.main === module) {
  // Create summaries directory if it doesn't exist
  if (!fs.existsSync(SUMMARIES_DIR)) {
    fs.mkdirSync(SUMMARIES_DIR, { recursive: true });
  }
  
  simulatePeriodicSummarization();
} 