// Memory Test Script for MISTER
// This script tests the memory retrieval capabilities

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Configuration
const KNOWLEDGE_BASE_DIR = path.join(__dirname);
const TOPICS = ['cardano', 'market-analysis', 'trading-strategies'];
const TEST_QUERIES = [
  { topic: 'cardano', query: 'What is Cardano\'s consensus protocol?', expected: 'Ouroboros' },
  { topic: 'cardano', query: 'List Cardano\'s development phases', expected: '<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>' },
  { topic: 'market-analysis', query: 'What does RSI measure?', expected: 'speed and change of price movements' },
  { topic: 'market-analysis', query: 'What are Bollinger Bands?', expected: 'volatility with upper and lower bands' },
  { topic: 'trading-strategies', query: 'What is the 1% rule?', expected: 'Never risk more than 1% of capital' },
  { topic: 'trading-strategies', query: 'What are trailing stops?', expected: 'Moving stop loss as trade moves' }
];

// Function to test knowledge retrieval
async function testMemoryRetrieval() {
  console.log('Starting memory retrieval test...');
  console.log('---------------------------------');
  
  let passedTests = 0;
  
  for (const test of TEST_QUERIES) {
    console.log(`\nTesting: "${test.query}"`);
    console.log(`Topic: ${test.topic}`);
    console.log(`Expected to contain: "${test.expected}"`);
    
    // Search for the query in the appropriate files
    const matches = await searchKnowledgeBase(test.topic, test.query);
    
    if (matches.length > 0) {
      // Check if any of the matches contain the expected text
      const containsExpected = matches.some(match => 
        match.content.toLowerCase().includes(test.expected.toLowerCase())
      );
      
      if (containsExpected) {
        console.log('✅ TEST PASSED');
        passedTests++;
      } else {
        console.log('❌ TEST FAILED - Expected text not found in matches');
        console.log('Found matches:');
        matches.forEach(match => console.log(`- ${match.content}`));
      }
    } else {
      console.log('❌ TEST FAILED - No matches found');
    }
  }
  
  console.log('\n---------------------------------');
  console.log(`Test Results: ${passedTests}/${TEST_QUERIES.length} tests passed (${Math.round(passedTests / TEST_QUERIES.length * 100)}%)`);
}

// Function to search the knowledge base for relevant information
async function searchKnowledgeBase(topic, query) {
  const matches = [];
  
  try {
    // Get all markdown files in the topic directory
    const topicDir = path.join(KNOWLEDGE_BASE_DIR, topic);
    const files = fs.readdirSync(topicDir).filter(file => file.endsWith('.md'));
    
    // Simple keyword matching (in a real system, this would be more sophisticated)
    const queryWords = query.toLowerCase().split(' ')
      .filter(word => word.length > 3) // Filter out short words
      .map(word => word.replace(/[?.,!]/g, '')); // Remove punctuation
    
    for (const file of files) {
      const filePath = path.join(topicDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Split content into paragraphs or bullet points
      const sections = content.split(/\n\n|\n- /);
      
      for (const section of sections) {
        // Count how many query words match in this section
        const matchCount = queryWords.filter(word => 
          section.toLowerCase().includes(word)
        ).length;
        
        // If more than half of the query words match, consider it relevant
        if (matchCount > queryWords.length / 2) {
          matches.push({
            source: `${topic}/${file}`,
            content: section.trim(),
            relevance: matchCount / queryWords.length
          });
        }
      }
    }
    
    // Sort matches by relevance
    matches.sort((a, b) => b.relevance - a.relevance);
    
    return matches.slice(0, 3); // Return top 3 matches
  } catch (error) {
    console.error(`Error searching knowledge base: ${error}`);
    return [];
  }
}

// Function to simulate conversation retrieval
async function simulateConversation() {
  console.log('\nSimulating conversation retrieval...');
  console.log('---------------------------------');
  
  const conversationHistory = [
    { role: 'user', content: 'What do you know about Cardano?' },
    { role: 'assistant', content: 'Cardano is a third-generation blockchain using the Ouroboros proof-of-stake protocol. It has a multi-layer architecture and supports native tokens.' },
    { role: 'user', content: 'Tell me about technical analysis' },
    { role: 'assistant', content: 'Technical analysis uses indicators like RSI and MACD to analyze price movements. For crypto, it\'s important to consider market cycles and on-chain metrics.' },
    { role: 'user', content: 'How should I manage risk?' }
  ];
  
  console.log('Current conversation:');
  conversationHistory.forEach(msg => {
    console.log(`[${msg.role}]: ${msg.content}`);
  });
  
  console.log('\nRetrieving relevant knowledge based on conversation context...');
  
  // Extract key terms from conversation
  const conversationText = conversationHistory
    .map(msg => msg.content)
    .join(' ');
  
  const keyTerms = extractKeyTerms(conversationText);
  console.log(`Extracted key terms: ${keyTerms.join(', ')}`);
  
  // For each topic, find relevant knowledge
  for (const topic of TOPICS) {
    console.log(`\nSearching in ${topic}...`);
    const relevantKnowledge = await searchByTerms(topic, keyTerms);
    
    if (relevantKnowledge.length > 0) {
      console.log(`Found ${relevantKnowledge.length} relevant items:`);
      relevantKnowledge.forEach(item => {
        console.log(`- Source: ${item.source}`);
        console.log(`  Content: ${item.content.substring(0, 100)}...`);
      });
    } else {
      console.log('No relevant knowledge found');
    }
  }
}

// Extract key terms from text
function extractKeyTerms(text) {
  const commonWords = ['the', 'and', 'that', 'for', 'you', 'about', 'what', 'how', 'tell', 'me'];
  
  return text.toLowerCase()
    .replace(/[.,?!;()]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3 && !commonWords.includes(word))
    .slice(0, 10); // Take top 10 terms
}

// Search by key terms
async function searchByTerms(topic, terms) {
  const matches = [];
  
  try {
    const topicDir = path.join(KNOWLEDGE_BASE_DIR, topic);
    const files = fs.readdirSync(topicDir).filter(file => file.endsWith('.md'));
    
    for (const file of files) {
      const filePath = path.join(topicDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Split content into paragraphs
      const paragraphs = content.split(/\n\n/);
      
      for (const paragraph of paragraphs) {
        // Count how many terms match in this paragraph
        const matchCount = terms.filter(term => 
          paragraph.toLowerCase().includes(term)
        ).length;
        
        // If any terms match, consider it relevant
        if (matchCount > 0) {
          matches.push({
            source: `${topic}/${file}`,
            content: paragraph.trim(),
            relevance: matchCount / terms.length
          });
        }
      }
    }
    
    // Sort by relevance
    matches.sort((a, b) => b.relevance - a.relevance);
    
    return matches.slice(0, 2); // Return top 2 matches per topic
  } catch (error) {
    console.error(`Error searching by terms: ${error}`);
    return [];
  }
}

// Run tests if directly executed
if (require.main === module) {
  testMemoryRetrieval()
    .then(() => simulateConversation())
    .then(() => {
      console.log('\nMemory tests completed!');
    });
} 