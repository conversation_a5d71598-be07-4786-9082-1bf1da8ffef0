// Memory Consolidation Script for MISTER
// This script can be scheduled to run periodically to summarize and organize knowledge

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { exec } = require('child_process');

// Configuration
const KN<PERSON>LEDGE_BASE_DIR = path.join(__dirname);
const CONSOLIDATION_DIR = path.join(KNOWLEDGE_BASE_DIR, 'consolidated');
const TOPICS = ['cardano', 'market-analysis', 'trading-strategies'];
const MAX_ITEMS_PER_SUMMARY = 15;

// Ensure consolidation directory exists
if (!fs.existsSync(CONSOLIDATION_DIR)) {
  fs.mkdirSync(CONSOLIDATION_DIR, { recursive: true });
}

// Function to extract knowledge from markdown files
async function extractKnowledge(directory) {
  const knowledgeItems = [];
  
  try {
    const files = fs.readdirSync(path.join(KNOWLEDGE_BASE_DIR, directory))
      .filter(file => file.endsWith('.md'));
    
    for (const file of files) {
      const filePath = path.join(KNOWLEDGE_BASE_DIR, directory, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Extract headers and key points
      const headers = content.match(/^##\s+(.+)$/gm) || [];
      const points = content.match(/^-\s+(.+)$/gm) || [];
      
      headers.forEach(header => {
        knowledgeItems.push({
          type: 'header',
          content: header.replace(/^##\s+/, ''),
          source: `${directory}/${file}`,
          importance: 0.8
        });
      });
      
      points.forEach(point => {
        knowledgeItems.push({
          type: 'point',
          content: point.replace(/^-\s+/, '').replace(/\*\*/g, ''),
          source: `${directory}/${file}`,
          importance: 0.6
        });
      });
    }
  } catch (error) {
    console.error(`Error processing ${directory}: ${error}`);
  }
  
  return knowledgeItems;
}

// Function to generate consolidated summary
async function consolidateKnowledge() {
  for (const topic of TOPICS) {
    try {
      // Extract knowledge from topic directory
      const knowledgeItems = await extractKnowledge(topic);
      
      // Sort by importance
      knowledgeItems.sort((a, b) => b.importance - a.importance);
      
      // Take the most important items
      const importantItems = knowledgeItems.slice(0, MAX_ITEMS_PER_SUMMARY);
      
      // Generate summary
      let summary = `# ${topic.charAt(0).toUpperCase() + topic.slice(1)} Knowledge Summary\n\n`;
      summary += `*Last updated: ${new Date().toISOString().split('T')[0]}*\n\n`;
      
      // Group by source
      const groupedBySource = {};
      importantItems.forEach(item => {
        if (!groupedBySource[item.source]) {
          groupedBySource[item.source] = [];
        }
        groupedBySource[item.source].push(item);
      });
      
      // Generate the summary content
      Object.keys(groupedBySource).forEach(source => {
        summary += `## From ${source}\n\n`;
        
        groupedBySource[source].forEach(item => {
          if (item.type === 'header') {
            summary += `### ${item.content}\n\n`;
          } else {
            summary += `- ${item.content}\n`;
          }
        });
        
        summary += '\n';
      });
      
      // Write the summary file
      const summaryPath = path.join(CONSOLIDATION_DIR, `${topic}-summary.md`);
      fs.writeFileSync(summaryPath, summary);
      
      console.log(`Consolidated knowledge for ${topic} at ${summaryPath}`);
    } catch (error) {
      console.error(`Error consolidating ${topic}: ${error}`);
    }
  }
}

// Function to run consolidation on schedule
function scheduleConsolidation(intervalHours = 24) {
  console.log(`Scheduling knowledge consolidation every ${intervalHours} hours`);
  
  // Initial consolidation
  consolidateKnowledge();
  
  // Schedule future consolidations
  setInterval(consolidateKnowledge, intervalHours * 60 * 60 * 1000);
}

// Export functions for use in other scripts
module.exports = {
  consolidateKnowledge,
  scheduleConsolidation
};

// If running directly, perform consolidation
if (require.main === module) {
  consolidateKnowledge().then(() => {
    console.log('Knowledge consolidation complete');
  });
} 