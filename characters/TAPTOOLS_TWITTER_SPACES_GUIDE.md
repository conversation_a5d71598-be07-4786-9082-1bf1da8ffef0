# TapTools Integration with Twitter Spaces for Cardano Data

This guide explains how to set up and use the TapTools plugin with Twitter Spaces to allow your character to speak intelligently about Cardano blockchain data during Twitter Spaces sessions.

## Overview

This integration combines two powerful capabilities:

1. **TapTools Cardano API**: Provides access to real-time Cardano blockchain data including token prices, price changes, and trading volumes.

2. **Twitter Spaces**: Allows your character to host and participate in audio conversations on Twitter.

Together, these capabilities enable your character to:
- Host Twitter Spaces focused on Cardano topics
- Retrieve real-time Cardano data during conversations
- Store and access previously retrieved data using RAG knowledge
- Generate summaries of Space conversations

## Setup Instructions

### 1. API Key Setup

First, you need to obtain a TapTools API key:

1. Visit [TapTools](https://taptools.io) and create an account
2. Navigate to your account settings
3. Request an API key for your application
4. Store this API key securely as an environment variable:

```bash
export TAPTOOLS_API_KEY='your_api_key_here'
```

### 2. Plugin Installation

1. Create the plugins directory if it doesn't exist:

```bash
mkdir -p characters/plugins
```

2. Copy the provided plugin files to this directory:
   - `taptoolsPlugin.js`: The core TapTools API plugin
   - `cardanoSpacesAgent.js`: The integration between TapTools and Twitter Spaces
   - `useTapToolsWithTwitterSpaces.js`: Example usage code

3. Ensure you have the required knowledge directories:

```bash
mkdir -p characters/knowledge/cardano/interactions
```

### 3. Character Configuration

Update your character configuration to include the TapTools plugin:

```json
{
  "name": "YourCharacter",
  "bio": "Cardano expert and Twitter Spaces host",
  "systemPrompt": "You are a knowledgeable voice on the Cardano blockchain. When hosting Twitter Spaces, actively share insights about token prices, market trends, and ecosystem updates...",
  "plugins": ["taptoolsCardano", "twitter"]
}
```

## Usage

### Basic Usage

The simplest way to use this integration is through the provided example:

```javascript
const { runCardanoTwitterSpace } = require('./characters/plugins/useTapToolsWithTwitterSpaces');

// Run a Twitter Space about Cardano
runCardanoTwitterSpace();
```

### Custom Implementation

For more control, you can use the CardanoSpacesAgent directly:

```javascript
const ElizaOS = require('eliza-os');
const TaptoolsPlugin = require('./characters/plugins/taptoolsPlugin');
const CardanoSpacesAgent = require('./characters/plugins/cardanoSpacesAgent');

// Initialize with your configuration
const elizaOS = new ElizaOS({
  plugins: [
    new TaptoolsPlugin({
      apiKey: process.env.TAPTOOLS_API_KEY
    })
  ]
});

// Create the agent
const agent = new CardanoSpacesAgent(elizaOS);

// Host a Space
async function hostCardanoSpace() {
  // Create a Space about SNEK token
  const space = await agent.hostCardanoSpace('SNEK Token Analysis');
  
  // The agent will automatically fetch relevant data about SNEK
  
  // When someone asks a question in the Space
  await agent.speakInSpace(space.id, "What's the latest on SNEK price?");
  
  // End the Space when done
  await agent.endSpace(space.id);
}

hostCardanoSpace();
```

## Key Features

### 1. Automatic Data Retrieval

Based on the Space topic, the agent automatically retrieves relevant data:
- For general market topics, it fetches top volume tokens and price changes
- For specific token topics, it fetches detailed data about those tokens
- All data is stored in the RAG knowledge system for future reference

### 2. RAG Knowledge Integration

All data retrieved from TapTools is:
- Transformed into natural language text
- Stored with appropriate metadata
- Indexed for semantic search
- Available for future conversations

### 3. Context-Aware Responses

When responding to questions in a Space, the agent:
1. First checks the RAG knowledge system for relevant information
2. If insufficient data is found, fetches fresh data from TapTools
3. Formulates a natural, conversational response
4. Saves the interaction for future reference

### 4. Automatic Summarization

After a Space ends, the agent:
- Creates a summary of all questions asked
- Records key points discussed
- Saves this to the knowledge system
- Makes it available for future reference

## Data Types Available

The TapTools plugin can access:

1. **Token Prices**
   - Current price in USD
   - Token metadata (name, ticker, policy ID)

2. **Price Changes**
   - Percentage changes over specified time periods
   - Supports periods like 24h, 7d, 30d

3. **Volume Metrics**
   - Trading volume for tokens
   - Ranked lists of top volume tokens

## Extending the Integration

### Adding Custom Token Lists

You can add custom token lists for frequent reference:

```javascript
// In your implementation
const FAVORITE_TOKENS = ['ADA', 'SNEK', 'HOSKY', 'DJED'];

// Use in your agent
const tokenData = await agent.eliza.callPlugin('taptoolsCardano', 'getTokenPrices', {
  tokens: FAVORITE_TOKENS
});
```

### Custom Analysis Logic

You can extend the agent with custom analysis functions:

```javascript
// Add to CardanoSpacesAgent class
async analyzeTokenTrend(tokenName) {
  // Get historical data
  const dayData = await this.eliza.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', {
    period: '24h',
    tokens: [tokenName]
  });
  
  const weekData = await this.eliza.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', {
    period: '7d',
    tokens: [tokenName]
  });
  
  // Your analysis logic here
  // ...
  
  return analysis;
}
```

## Troubleshooting

### API Rate Limits

TapTools may have API rate limits. To handle this:

1. Implement caching for frequently requested data
2. Add exponential backoff for failed requests
3. Consider batching requests where possible

### Twitter Spaces Limitations

Twitter Spaces may have limitations on:

1. How quickly messages can be sent
2. Content restrictions for automated accounts
3. Duration of spaces

Ensure your implementation respects these limitations.

## Example Use Cases

1. **Daily Market Update Spaces**
   - Automated daily Spaces with current market trends
   - Answering audience questions about prices and volume

2. **New Token Launch Coverage**
   - Dedicated Spaces for new token launches
   - Real-time tracking of initial trading data

3. **Technical Analysis Discussions**
   - Spaces focused on technical analysis of selected tokens
   - Pattern recognition and trend discussion

## Conclusion

This integration enables powerful Cardano data capabilities within Twitter Spaces. By combining real-time data access with knowledge storage, your character can provide valuable insights and build an engaged community around Cardano topics.

For additional help or to report issues, please contact the Eliza OS team. 