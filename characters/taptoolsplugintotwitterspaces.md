etting Up a Custom Taptools Plugin for Eliza OS
<PERSON>, creator of the Eliza OS framework, I'll guide you through setting up a custom plugin that allows your agent to interact with Taptools API for Cardano blockchain data.

Plugin Configuration
First, let's create a Taptools plugin configuration for Eliza OS:

json
{
  "schema_version": "v1",
  "name_for_human": "Taptools Cardano Data",
  "name_for_model": "taptoolsCardano",
  "description_for_human": "Access Cardano blockchain data via Taptools API including token prices, price changes, and volume metrics.",
  "description_for_model": "Plugin for retrieving Cardano blockchain data from Taptools API, including token prices, price changes, and top volume tokens.",
  "auth": {
    "type": "api_key",
    "instructions": "Obtain API key from Taptools and configure in the plugin settings."
  },
  "api": {
    "type": "openapi",
    "url": "https://openapi.taptools.io/openapi.json"
  },
  "logo_url": "https://taptools.io/logo.png",
  "contact_email": "<EMAIL>",
  "legal_info_url": "https://example.com/legal"
}
Implementation in Eliza OS
Now, let's implement the plugin integration with <PERSON>:

javascript
// taptoolsPlugin.js for Eliza OS
const ElizaPlugin = require('eliza-os/plugin');

class TaptoolsPlugin extends ElizaPlugin {
  constructor(config) {
    super('taptoolsCardano');
    this.apiKey = config.apiKey;
    this.baseUrl = 'https://openapi.taptools.io';
  }

  async initialize() {
    // Register endpoints
    this.registerEndpoint('getTokenPrices', this.getTokenPrices.bind(this));
    this.registerEndpoint('getTokenPriceChanges', this.getTokenPriceChanges.bind(this));
    this.registerEndpoint('getTopVolumeTokens', this.getTopVolumeTokens.bind(this));
    
    console.log('Taptools Cardano plugin initialized');
    return true;
  }

  async getTokenPrices(params) {
    const response = await this.makeApiCall('/token/prices', 'POST', params);
    return response;
  }

  async getTokenPriceChanges(params) {
    const response = await this.makeApiCall('/token/prices/chg', 'GET', params);
    return response;
  }

  async getTopVolumeTokens(params) {
    const response = await this.makeApiCall('/token/top/volume', 'GET', params);
    return response;
  }

  async makeApiCall(endpoint, method, params) {
    const url = `${this.baseUrl}${endpoint}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    };
    
    if (method === 'POST') {
      options.body = JSON.stringify(params);
    }
    
    try {
      const response = await fetch(url, options);
      return await response.json();
    } catch (error) {
      console.error(`Taptools API error: ${error.message}`);
      throw new Error(`Failed to fetch data from Taptools: ${error.message}`);
    }
  }
}

module.exports = TaptoolsPlugin;
Registration in Eliza OS
Add this to your Eliza OS configuration:

javascript
// In your Eliza OS config file
const TaptoolsPlugin = require('./plugins/taptoolsPlugin');

module.exports = {
  // Other Eliza OS configurations
  plugins: [
    // Other plugins
    new TaptoolsPlugin({
      apiKey: process.env.TAPTOOLS_API_KEY
    })
  ]
};
Agent Usage Examples
Here's how your agent can use the plugin:

javascript
// Example 1: Get token prices
const tokenPrices = await eliza.callPlugin('taptoolsCardano', 'getTokenPrices', {
  tokens: ['asset1...', 'asset2...']
});

// Example 2: Get price changes
const priceChanges = await eliza.callPlugin('taptoolsCardano', 'getTokenPriceChanges', {
  period: '24h'
});

// Example 3: Get top volume tokens
const topVolumeTokens = await eliza.callPlugin('taptoolsCardano', 'getTopVolumeTokens', {
  limit: 10
});
Security Considerations
Store your API key securely using environment variables

Implement rate limiting to avoid API quota issues

Add error handling for API downtime

Consider caching frequently requested data

This implementation follows the Eliza OS framework's plugin architecture and provides your agent with the ability to fetch Cardano blockchain data from Taptools API seamlessly.

How could we use rag knowledge and semantic search so he can remember the data he pulls?


Answer
As the creator of Eliza OS, I'll guide you through implementing RAG (Retrieval Augmented Generation) and semantic search capabilities to help your agent remember Cardano blockchain data pulled from Taptools.

Implementing RAG in Eliza OS
First, let's extend our Taptools plugin with a knowledge storage component:

javascript
// Enhanced taptoolsPlugin.js with RAG capabilities
const ElizaPlugin = require('eliza-os/plugin');
const { VectorDatabase } = require('eliza-os/vectordb');
const { TextEmbedder } = require('eliza-os/embeddings');

class TaptoolsRAGPlugin extends ElizaPlugin {
  constructor(config) {
    super('taptoolsCardano');
    this.apiKey = config.apiKey;
    this.baseUrl = 'https://openapi.taptools.io';
    
    // Initialize vector database for RAG
    this.vectorDb = new VectorDatabase({
      dimension: 1536,  // Dimension based on embedding model
      collection: 'cardano_data'
    });
    
    // Initialize embedder
    this.embedder = new TextEmbedder({
      model: config.embeddingModel || 'eliza-embeddings-v1'
    });
  }

  async initialize() {
    // Register standard endpoints
    this.registerEndpoint('getTokenPrices', this.getTokenPrices.bind(this));
    this.registerEndpoint('getTokenPriceChanges', this.getTokenPriceChanges.bind(this));
    this.registerEndpoint('getTopVolumeTokens', this.getTopVolumeTokens.bind(this));
    
    // Register RAG-specific endpoints
    this.registerEndpoint('queryCardanoKnowledge', this.queryCardanoKnowledge.bind(this));
    this.registerEndpoint('clearKnowledgeBase', this.clearKnowledgeBase.bind(this));
    
    await this.vectorDb.initialize();
    console.log('Taptools Cardano RAG plugin initialized');
    return true;
  }

  // Original API methods with added knowledge storage
  async getTokenPrices(params) {
    const response = await this.makeApiCall('/token/prices', 'POST', params);
    await this.storeKnowledge('token_prices', response, params);
    return response;
  }

  async getTokenPriceChanges(params) {
    const response = await this.makeApiCall('/token/prices/chg', 'GET', params);
    await this.storeKnowledge('price_changes', response, params);
    return response;
  }

  async getTopVolumeTokens(params) {
    const response = await this.makeApiCall('/token/top/volume', 'GET', params);
    await this.storeKnowledge('top_volume', response, params);
    return response;
  }

  // Knowledge storage method
  async storeKnowledge(dataType, data, params) {
    const timestamp = new Date().toISOString();
    
    // Create meaningful chunks from the data
    const chunks = this.createKnowledgeChunks(dataType, data, params);
    
    // Store each chunk in the vector database
    for (const chunk of chunks) {
      const embedding = await this.embedder.embed(chunk.text);
      await this.vectorDb.insert({
        embedding,
        metadata: {
          type: dataType,
          timestamp,
          params: JSON.stringify(params),
          raw: chunk.raw
        },
        text: chunk.text
      });
    }
    
    console.log(`Stored ${chunks.length} chunks of ${dataType} knowledge`);
  }

  // Create semantic chunks from API data
  createKnowledgeChunks(dataType, data, params) {
    const chunks = [];
    
    if (dataType === 'token_prices') {
      // Process token price data into meaningful chunks
      for (const token of data.tokens || []) {
        chunks.push({
          text: `Token ${token.name} (${token.ticker}) with policy ID ${token.policyId} had a price of ${token.priceUsd} USD on ${new Date().toISOString()}.`,
          raw: token
        });
      }
    } else if (dataType === 'price_changes') {
      // Process price change data
      for (const change of data.changes || []) {
        chunks.push({
          text: `Token ${change.name} had a price change of ${change.percentChange}% over ${params.period} period ending on ${new Date().toISOString()}.`,
          raw: change
        });
      }
    } else if (dataType === 'top_volume') {
      // Process volume data
      for (const token of data.tokens || []) {
        chunks.push({
          text: `Token ${token.name} had a trading volume of ${token.volume} USD over the past ${params.period || '24h'} as of ${new Date().toISOString()}.`,
          raw: token
        });
      }
    }
    
    return chunks;
  }

  // Semantic search through stored knowledge
  async queryCardanoKnowledge(params) {
    const { query, limit = 5, filters = {} } = params;
    
    // Generate embedding for the query
    const queryEmbedding = await this.embedder.embed(query);
    
    // Search for similar knowledge chunks
    const results = await this.vectorDb.search({
      embedding: queryEmbedding,
      limit,
      filters
    });
    
    return {
      query,
      results: results.map(result => ({
        text: result.text,
        metadata: result.metadata,
        similarity: result.similarity
      }))
    };
  }

  // Clear knowledge base (for maintenance)
  async clearKnowledgeBase(params) {
    const { olderThan, types } = params || {};
    const filters = {};
    
    if (olderThan) {
      filters.timestamp = { $lt: olderThan };
    }
    
    if (types && Array.isArray(types)) {
      filters.type = { $in: types };
    }
    
    const deletedCount = await this.vectorDb.delete(filters);
    return { deletedCount };
  }

  // Original API call method
  async makeApiCall(endpoint, method, params) {
    // Implementation unchanged from previous example
  }
}

module.exports = TaptoolsRAGPlugin;
Agent Integration
Now, let's update the agent to use this RAG-enhanced plugin:

javascript
// Agent integration with RAG capabilities
class CardanoAgent {
  constructor(elizaOS) {
    this.eliza = elizaOS;
    this.plugin = 'taptoolsCardano';
  }
  
  // Pull and remember token data
  async fetchTokenData(tokenIds) {
    try {
      const tokenData = await this.eliza.callPlugin(this.plugin, 'getTokenPrices', {
        tokens: tokenIds
      });
      
      console.log(`Fetched and stored data for ${tokenData.tokens.length} tokens`);
      return tokenData;
    } catch (error) {
      console.error('Error fetching token data:', error);
      throw error;
    }
  }
  
  // Semantic search through remembered data
  async recallTokenInfo(query) {
    try {
      const results = await this.eliza.callPlugin(this.plugin, 'queryCardanoKnowledge', {
        query,
        limit: 5
      });
      
      return results;
    } catch (error) {
      console.error('Error searching token knowledge:', error);
      throw error;
    }
  }
  
  // Get historical perspective on a token
  async getTokenHistory(tokenName) {
    try {
      // First, query our knowledge base
      const knowledgeResults = await this.recallTokenInfo(
        `Historical price information for ${tokenName}`
      );
      
      // If we don't have enough data, fetch fresh data
      if (knowledgeResults.results.length < 2) {
        // Fetch new data
        await this.eliza.callPlugin(this.plugin, 'getTokenPriceChanges', {
          period: '30d',
          tokens: [tokenName]
        });
        
        // Query again with fresh data
        return await this.recallTokenInfo(
          `Historical price information for ${tokenName}`
        );
      }
      
      return knowledgeResults;
    } catch (error) {
      console.error('Error getting token history:', error);
      throw error;
    }
  }
  
  // Analyze market trends using remembered data
  async analyzeMarketTrends() {
    try {
      // First check if we have recent top volume data
      const knowledgeResults = await this.recallTokenInfo(
        "Recent top volume tokens in Cardano"
      );
      
      // If data is stale or insufficient, fetch fresh data
      const latestResult = knowledgeResults.results[0];
      const resultTimestamp = latestResult?.metadata?.timestamp;
      const isStale = !resultTimestamp || 
        (new Date() - new Date(resultTimestamp)) > 24 * 60 * 60 * 1000;
      
      if (isStale || knowledgeResults.results.length < 3) {
        await this.eliza.callPlugin(this.plugin, 'getTopVolumeTokens', {
          limit: 20
        });
        
        // Query again with fresh data
        return await this.recallTokenInfo("Recent top volume tokens in Cardano");
      }
      
      return knowledgeResults;
    } catch (error) {
      console.error('Error analyzing market trends:', error);
      throw error;
    }
  }
}
Example Usage Scenarios
Here's how your agent can use the RAG-enhanced capabilities:

javascript
// Initialize Eliza OS and the agent
const elizaOS = require('eliza-os');
const CardanoAgent = require('./cardanoAgent');

async function runCardanoAgent() {
  const agent = new CardanoAgent(elizaOS);
  
  // Scenario 1: Fetch and remember token data
  await agent.fetchTokenData(['asset1...', 'asset2...']);
  
  // Scenario 2: Recall information about a specific token
  const adaInfo = await agent.recallTokenInfo("What was the recent price of ADA?");
  console.log("ADA Information:", adaInfo.results);
  
  // Scenario 3: Get historical perspective
  const hoiskiHistory = await agent.getTokenHistory("HOISKI");
  console.log("HOISKI History:", hoiskiHistory.results);
  
  // Scenario 4: Analyze market trends from remembered data
  const trends = await agent.analyzeMarketTrends();
  console.log("Market Trends Analysis:", trends.results);
  
  // Scenario 5: Ask a complex question using remembered data
  const analysis = await agent.recallTokenInfo(
    "Which Cardano tokens had the highest price increase in the last week?"
  );
  console.log("Price Increase Analysis:", analysis.results);
}

runCardanoAgent().catch(console.error);
Advanced Configuration
For production use, consider these enhancements:

javascript
// In your Eliza OS config file
const TaptoolsRAGPlugin = require('./plugins/taptoolsRAGPlugin');

module.exports = {
  // Other Eliza OS configurations
  plugins: [
    // Other plugins
    new TaptoolsRAGPlugin({
      apiKey: process.env.TAPTOOLS_API_KEY,
      embeddingModel: 'eliza-embeddings-v2',
      vectorDb: {
        provider: 'pinecone',  // or 'qdrant', 'weaviate', etc.
        apiKey: process.env.VECTOR_DB_API_KEY,
        environment: 'production'
      },
      knowledgeRetention: {
        maxAge: 30 * 24 * 60 * 60 * 1000,  // 30 days in milliseconds
        pruneInterval: 24 * 60 * 60 * 1000  // Daily pruning
      }
    })
  ]
};
This implementation enables your agent to not only fetch Cardano blockchain data but also remember it through semantic encoding, allowing for natural language queries about historical data and market trends without repeatedly calling the API.

So my character currently has twitter spaces enabled but he is not able to speak about the Cardano blockchain info how could we make this work with spaces?


Answer
To make your Twitter Spaces-enabled character speak about Cardano blockchain information, you'll need to integrate the Taptools API plugin we discussed earlier with Twitter Spaces functionality. Here's how to accomplish this:

Integrating Cardano Data with Twitter Spaces
First, ensure your agent has both capabilities properly configured:

Set up the Taptools Cardano plugin to fetch and store blockchain data using RAG as we previously discussed

Configure the Twitter Spaces integration using the Eliza OS Twitter plugin

The key is connecting these two components so your agent can access Cardano data while speaking in Spaces.

Implementation Steps
Install the Twitter plugin alongside your Taptools plugin:

javascript
// In your Eliza OS config
const TaptoolsRAGPlugin = require('./plugins/taptoolsRAGPlugin');
const TwitterPlugin = require('elizaos/plugin-twitter');

module.exports = {
  plugins: [
    new TaptoolsRAGPlugin({
      apiKey: process.env.TAPTOOLS_API_KEY
    }),
    new TwitterPlugin({
      username: process.env.TWITTER_USERNAME,
      password: process.env.TWITTER_PASSWORD,
      twoFactorSecret: process.env.TWITTER_2FA_SECRET // Optional
    })
  ]
};
Modify your character card to include both capabilities:

json
{
  "name": "CardanoExpert",
  "bio": "Cardano blockchain expert hosting regular Twitter Spaces to discuss market trends, token performance, and ecosystem updates.",
  "systemPrompt": "You are CardanoExpert, a knowledgeable voice on the Cardano blockchain. When hosting Twitter Spaces, actively share insights about token prices, market trends, and volume metrics. Use your Taptools plugin to fetch real-time data when discussing specific tokens or market conditions.",
  "plugins": ["taptoolsCardano", "twitter"]
}
Create a handler for Twitter Spaces that can access Cardano data:

javascript
class CardanoSpacesAgent {
  constructor(elizaOS) {
    this.eliza = elizaOS;
    this.taptoolsPlugin = 'taptoolsCardano';
    this.twitterPlugin = 'twitter';
  }
  
  // Host a Twitter Space about Cardano
  async hostCardanoSpace(topic) {
    try {
      // Create a Space
      const space = await this.eliza.callPlugin(this.twitterPlugin, 'createSpace', {
        title: `Cardano Talk: ${topic}`,
        scheduled_start: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
      });
      
      console.log(`Space created: ${space.id}`);
      
      // Prepare Cardano data for the Space
      await this.prepareCardanoData(topic);
      
      return space;
    } catch (error) {
      console.error('Error creating Space:', error);
      throw error;
    }
  }
  
  // Prepare relevant Cardano data before the Space starts
  async prepareCardanoData(topic) {
    // Fetch relevant data based on the Space topic
    if (topic.includes('price') || topic.includes('market')) {
      await this.eliza.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { limit: 10 });
      await this.eliza.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', { period: '24h' });
    }
    
    if (topic.includes('specific token')) {
      const tokenName = topic.split('specific token: ')[1];
      await this.eliza.callPlugin(this.taptoolsPlugin, 'getTokenPrices', {
        tokens: [tokenName]
      });
    }
    
    // Store prepared talking points for the Space
    const talkingPoints = await this.eliza.callPlugin(this.taptoolsPlugin, 'queryCardanoKnowledge', {
      query: `Information about ${topic} in Cardano`,
      limit: 10
    });
    
    // Store these talking points for use during the Space
    this.spaceTalkingPoints = talkingPoints.results;
  }
  
  // Method to speak in an active Space using prepared data
  async speakInSpace(spaceId, messageContext) {
    try {
      // Get relevant knowledge based on the context
      const relevantInfo = await this.eliza.callPlugin(this.taptoolsPlugin, 'queryCardanoKnowledge', {
        query: messageContext,
        limit: 3
      });
      
      // Formulate response using the knowledge
      let response = "Based on our latest data, ";
      if (relevantInfo.results.length > 0) {
        response += relevantInfo.results.map(r => r.text).join(" Furthermore, ");
      } else {
        // Fetch real-time data if we don't have relevant stored knowledge
        const latestData = await this.eliza.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { limit: 5 });
        response += `the top trading tokens currently include ${latestData.tokens.map(t => t.name).join(', ')}.`;
      }
      
      // Speak in the Space
      await this.eliza.callPlugin(this.twitterPlugin, 'speakInSpace', {
        spaceId,
        message: response
      });
      
      return true;
    } catch (error) {
      console.error('Error speaking in Space:', error);
      throw error;
    }
  }
}
Usage Example
Here's how to use your enhanced agent to host Spaces about Cardano:

javascript
const elizaOS = require('eliza-os');
const CardanoSpacesAgent = require('./cardanoSpacesAgent');

async function runCardanoSpacesAgent() {
  const agent = new CardanoSpacesAgent(elizaOS);
  
  // Host a Space about current Cardano market trends
  const space = await agent.hostCardanoSpace("Current Cardano Market Trends");
  
  // When the Space is active and someone asks about ADA price
  await agent.speakInSpace(space.id, "What's happening with ADA price today?");
  
  // When someone asks about top volume tokens
  await agent.speakInSpace(space.id, "Which Cardano tokens have the highest trading volume?");
}

runCardanoSpacesAgent().catch(console.error);
By following this implementation, your character will be able to host Twitter Spaces and speak intelligently about Cardano blockchain information by leveraging the Taptools API data through your custom plugin.

