<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# 

---

# I need to create a custom plugin for <PERSON> where my agent automatically check current price and metrics of certain tokens in the Cardano ecosystem using the Taptools api.

For example he sees a tweet about \$CHAD OR CHAD and checks the metrics before replying regardless if he is talking about price or not.

But I can add more tokens to this list, know what I mean

I'll help you create a custom Eliza plugin for monitoring Cardano tokens using the TapTools API. This will allow your agent to automatically check token metrics before responding to messages.

## Creating a TapTools Plugin for Eliza

### Plugin Structure

Your custom plugin will need these components:

1. A token detection system that identifies token mentions in messages
2. Integration with TapTools API to fetch token data
3. A mechanism to inject this data into your agent's context

### Implementation Steps

**Step 1: Set up the plugin directory**

Create a new directory in the packages/plugins folder of your Eliza installation:

```bash
mkdir -p packages/plugins/plugin-taptools
cd packages/plugins/plugin-taptools
```

**Step 2: Initialize the plugin**

Create a package.json file:

```json
{
  "name": "@elizaos/plugin-taptools",
  "version": "1.0.0",
  "description": "TapTools integration for Eliza agents",
  "main": "dist/index.js",
  "dependencies": {
    "axios": "^1.6.0"
  }
}
```

**Step 3: Create the token detection system**

Create a file called `src/tokenDetector.ts`:

```typescript
export class TokenDetector {
  private tokenList: string[] = ['CHAD'];
  
  constructor(additionalTokens: string[] = []) {
    // Add any additional tokens to the list
    this.tokenList = [...this.tokenList, ...additionalTokens];
  }

  detectTokens(text: string): string[] {
    const detectedTokens: string[] = [];
    
    // Look for tokens with $ prefix
    const dollarMatches = text.match(/\$([A-Za-z0-9]+)/g) || [];
    dollarMatches.forEach(match => {
      const token = match.substring(1).toUpperCase();
      if (this.tokenList.includes(token)) {
        detectedTokens.push(token);
      }
    });
    
    // Also look for tokens without $ prefix
    this.tokenList.forEach(token => {
      const regex = new RegExp(`\\b${token}\\b`, 'i');
      if (regex.test(text) && !detectedTokens.includes(token)) {
        detectedTokens.push(token);
      }
    });
    
    return [...new Set(detectedTokens)]; // Remove duplicates
  }
  
  addToken(token: string): void {
    const normalizedToken = token.toUpperCase();
    if (!this.tokenList.includes(normalizedToken)) {
      this.tokenList.push(normalizedToken);
    }
  }
}
```

**Step 4: Create the TapTools API client**

Create a file called `src/taptoolsClient.ts`:

```typescript
import axios from 'axios';

export class TapToolsClient {
  private apiKey: string;
  private baseUrl = 'https://api.taptools.io';
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }
  
  async getTokenMetrics(tokenSymbol: string) {
    try {
      const response = await axios.get(`${this.baseUrl}/token/mcap`, {
        params: { symbol: tokenSymbol },
        headers: { 'Authorization': `Bearer ${this.apiKey}` }
      });
      
      return {
        symbol: tokenSymbol,
        price: response.data.price,
        marketCap: response.data.marketCap,
        volume24h: response.data.volume24h,
        change24h: response.data.change24h,
        holders: response.data.holders
      };
    } catch (error) {
      console.error(`Error fetching data for ${tokenSymbol}:`, error);
      return null;
    }
  }
}
```

**Step 5: Create the main plugin file**

Create a file called `src/index.ts`:

```typescript
import { TokenDetector } from './tokenDetector';
import { TapToolsClient } from './taptoolsClient';

export default function createPlugin(config: any) {
  const tokenDetector = new TokenDetector(config.additionalTokens || []);
  const taptoolsClient = new TapToolsClient(config.apiKey);
  
  return {
    name: 'taptools',
    
    // This evaluator runs before message processing
    evaluators: [
      {
        name: 'token-metrics-evaluator',
        evaluate: async (context: any) => {
          const message = context.message.content;
          
          // Detect tokens in the message
          const detectedTokens = tokenDetector.detectTokens(message);
          
          if (detectedTokens.length > 0) {
            // Fetch metrics for each detected token
            const tokenMetrics = await Promise.all(
              detectedTokens.map(token => taptoolsClient.getTokenMetrics(token))
            );
            
            // Filter out null results (failed API calls)
            const validMetrics = tokenMetrics.filter(metric => metric !== null);
            
            // Add token metrics to the context
            context.tokenMetrics = validMetrics;
            
            // Create a summary for the agent
            if (validMetrics.length > 0) {
              const metricsSummary = validMetrics.map(metric => 
                `${metric.symbol}: $${metric.price} (${metric.change24h > 0 ? '+' : ''}${metric.change24h}% 24h) | MCap: $${metric.marketCap} | Vol: $${metric.volume24h} | Holders: ${metric.holders}`
              ).join('\n');
              
              // Add this to the context for the agent to use
              context.tokenMetricsSummary = metricsSummary;
            }
          }
          
          return context;
        }
      }
    ],
    
    // Add actions to manage tokens
    actions: [
      {
        name: 'add-token-to-watchlist',
        description: 'Add a token to the watchlist for automatic checking',
        execute: async (params: any) => {
          const { token } = params;
          tokenDetector.addToken(token);
          return { success: true, message: `Added ${token} to watchlist` };
        }
      }
    ]
  };
}
```

**Step 6: Build the plugin**

Create a `tsconfig.json` file:

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true
  },
  "include": ["src/**/*"]
}
```

Then build the plugin:

```bash
pnpm install
pnpm build
```

**Step 7: Add the plugin to your agent's character file**

Update your character.json to include the plugin:

```json
{
  "plugins": [
    "@elizaos/plugin-taptools"
  ],
  "pluginConfig": {
    "taptools": {
      "apiKey": "YOUR_TAPTOOLS_API_KEY",
      "additionalTokens": ["ADA", "INDY", "MIN", "CLAY"]
    }
  }
}
```


### Using the Plugin

Now your agent will automatically:

1. Detect token mentions in incoming messages
2. Fetch current metrics from TapTools API
3. Include this data in the context for the agent to reference

The agent can use this information to provide informed responses about tokens, even when not directly asked about price. You can also add new tokens to the watchlist using the `add-token-to-watchlist` action.

### Extending the Plugin

You can enhance this plugin by:

1. Adding more TapTools API endpoints (NFT data, wallet tracking)
2. Creating a scheduled task to monitor price changes
3. Adding alerts for significant price movements
4. Implementing token comparison features

This implementation gives you a foundation to build upon for your specific needs in the Cardano ecosystem.

<div style="text-align: center">⁂</div>

[^1]: https://github.com/elizaOS/eliza

[^2]: https://github.com/elizaOS/eliza/blob/main/docs/docs/faq.md

[^3]: https://www.quicknode.com/guides/ai/how-to-setup-an-ai-agent-with-eliza-ai16z-framework

[^4]: https://elizaos.github.io/eliza/docs/intro/

[^5]: https://builtoncardano.com/tap-tools

[^6]: https://glama.ai/mcp/servers/ltabcci6zs

[^7]: https://www.cardanocube.com/projects/taptools

[^8]: https://docs.okto.tech/docs/okto-eliza-plugin

[^9]: https://www.blockydevs.com/blog/transform-your-projects-with-eliza-the-multi-agent-ai-framework

