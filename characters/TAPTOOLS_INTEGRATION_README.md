# MISTER's Cardano TapTools Integration

This integration enables MISTER to access real-time Cardano blockchain data via the TapTools API and incorporate it into Twitter conversations, Spaces, and posts. With this integration, MISTER can analyze token prices, compare top performers, and provide market insights.

## Features

### 1. Token Data Retrieval
- Current token prices and 24-hour changes
- Trading volume data for all Cardano native assets
- Market ranking and momentum analysis

### 2. Twitter Integration
- Respond to questions about specific tokens
- Generate comprehensive market analysis
- Compare top tokens by various metrics
- Create scheduled tweets with Cardano insights

### 3. RAG Knowledge Storage
- All retrieved data is stored in the knowledge base
- Historical data is available for future reference
- Analysis results are saved for long-term learning

## Setup

The integration is already configured with your TapTools API key:
```
WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO
```

This key is hardcoded in the `taptoolsPlugin.js` file for immediate use.

### Files
1. `characters/plugins/taptoolsPlugin.js` - Core API interaction
2. `characters/plugins/taptoolsTwitterIntegration.js` - Twitter functionality
3. `characters/plugins/cardanoSpacesAgent.js` - Twitter Spaces integration

### Character Configuration

Add the TapTools plugin to MISTER's configuration by including it in the plugins array:

```json
{
  "name": "MISTER",
  "plugins": [
    // ... other plugins
    {
      "name": "taptoolsCardano",
      "description": "Access Cardano blockchain data via TapTools API"
    }
  ]
}
```

## Using the Integration

### In Direct Conversations

When someone asks MISTER about Cardano tokens, the integration will automatically:
1. Detect if the question is about a specific token, market trends, or comparison
2. Retrieve the appropriate data from TapTools
3. Format a response with relevant information

Example questions MISTER can answer:
- "What's the current price of SNEK?"
- "How is ADA performing today?"
- "Can you compare the top volume tokens in Cardano today?"
- "What are the market trends in Cardano?"

### In Twitter Spaces

During Twitter Spaces, MISTER can:
1. Host discussions about Cardano market trends
2. Respond to questions with real-time data
3. Generate summaries of token performance
4. Store Spaces conversation data for future reference

To use in Spaces, reference the example in `useTapToolsWithTwitterSpaces.js`.

### For Tweets and Replies

MISTER can create engaging tweets about Cardano, including:
1. Market overview tweets with key statistics
2. Spotlight posts on specific high-performing tokens
3. Comparative analysis of top tokens
4. Trend analysis with price movement indicators

The `generateCardanoTweet()` function can be used to create varied tweet content.

## Token Unit Mapping

TapTools API uses token "units" (policy ID + hex name) instead of symbols. The integration handles this by:

1. Building a comprehensive mapping of symbols to units
2. Automatically updating this mapping with fresh data
3. Special handling for common tokens like ADA
4. Fallback mechanisms when tokens aren't found

The mapping is initialized on startup and updated regularly.

## Performance Considerations

The integration includes several optimizations:

### 1. Caching
- Token prices are cached for 5 minutes
- Top volume data is cached for 15 minutes
- Analysis results are cached and reused

### 2. Rate Limiting
- Requests are spaced at least 500ms apart
- Batch requests are used when possible
- Error handling with exponential backoff

### 3. Data Storage
- All retrieved data is stored in RAG knowledge
- Reduces need for repeated API calls
- Knowledge grows more comprehensive over time

## Example Usage in Code

```javascript
// Initialize the integration
const ElizaOS = require('eliza-os');
const TaptoolsTwitterIntegration = require('./characters/plugins/taptoolsTwitterIntegration');

const elizaOS = new ElizaOS();
const taptoolsTwitter = new TaptoolsTwitterIntegration(elizaOS);

// Handle a user question about Cardano
async function handleUserQuestion(question) {
  if (question.toLowerCase().includes('cardano') || 
      question.toLowerCase().includes('ada') ||
      question.toLowerCase().includes('token')) {
    
    const response = await taptoolsTwitter.handleCardanoQuestion(question);
    return response;
  }
  
  // Handle other types of questions...
}

// Generate a Cardano-related tweet
async function generateScheduledTweet() {
  const tweetText = await taptoolsTwitter.generateCardanoTweet();
  // Post the tweet using your Twitter client
}
```

## Available Analysis Types

1. **Investment Analysis** - Overall score based on volume, price action, and liquidity
2. **Momentum Analysis** - Focus on price movement trends and velocity
3. **Volatility Analysis** - Comparison of price stability/volatility

Each analysis type has a different focus and presentation style, useful for different contexts.

## Troubleshooting

If you encounter issues:

1. **API Connection Problems**
   - Check that the API key is valid
   - Verify network connectivity
   - Look for rate limit messages in logs

2. **Token Not Found**
   - Check if you're using the correct symbol
   - Try using the policy ID if available
   - The token might not be tracked by TapTools

3. **Data Formatting Issues**
   - The integration handles null/undefined values
   - Check for changes in the TapTools API response format
   - Response formatting is designed for Twitter's character limits

## Future Enhancements

1. Support for more detailed technical analysis
2. Historical price chart generation
3. Integration with Cardano on-chain data
4. Sentiment analysis combined with price data
5. Whale movement tracking and alerting

---

This integration makes MISTER a powerful resource for Cardano information, providing real-time data and analysis across all social channels. 