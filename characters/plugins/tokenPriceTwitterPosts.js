/**
 * TokenPriceTwitterPosts Plugin
 *
 * This plugin generates random token price posts for Twitter
 * It focuses on the approved tokens: SNEK, HOSKY, MIN, ADA, BTC, IAG, INDY, COPI, NTX,
 * STUFF, BBSNEK, SUGR, NIKEPIG, FLDT, LQ, WRT, AGIX, WMT, WMTX, LENFI, FREN, BANK
 */

class TokenPriceTwitterPosts {
  constructor(runtime) {
    this.runtime = runtime;
    this.taptoolsPlugin = 'plugin-taptools';
    this.coinmarketcapPlugin = 'plugin-coinmarketcap';
    this.approvedTokens = [
      'SNEK', 'HOSKY', 'MIN', 'ADA', 'BTC', 'IAG', 'INDY', 'COPI', 'NTX',
      'STUFF', 'BBSNEK', 'SUGR', 'NIKEPIG', 'FLDT', 'LQ', 'WRT', 'AGIX',
      'WMT', 'WMTX', 'LENFI', 'FREN', 'BANK'
    ];

    // Top 20 layer one cryptos (by market cap, subject to change)
    this.topLayerOneTokens = [
      'BTC', 'ETH', 'SOL', 'BNB', 'ADA', 'AVAX', 'DOT', 'NEAR', 'ATOM',
      'ALGO', 'FTM', 'MATIC', 'ONE', 'EGLD', 'HBAR', 'FLOW', 'XTZ', 'EOS', 'ZIL', 'KAVA'
    ];

    // Combine all approved tokens
    this.allApprovedTokens = [...new Set([...this.approvedTokens, ...this.topLayerOneTokens])];

    // Last posted token to avoid repetition
    this.lastPostedTokens = [];
  }

  /**
   * Generate a random token price post
   */
  async generateRandomTokenPricePost() {
    try {
      // Select a random token from the approved list
      const randomToken = this.getRandomToken();

      // Get price data for the token
      const priceData = await this.getTokenPriceData(randomToken);

      if (!priceData) {
        return this.generateGenericTokenPost(randomToken);
      }

      // Format the price data
      return this.formatPricePost(randomToken, priceData);
    } catch (error) {
      console.error('Error generating random token price post:', error);
      return this.generateGenericTokenPost(this.getRandomToken());
    }
  }

  /**
   * Get a random token from the approved list
   */
  getRandomToken() {
    const randomIndex = Math.floor(Math.random() * this.allApprovedTokens.length);
    return this.allApprovedTokens[randomIndex];
  }

  /**
   * Get price data for a token using the CoinMarketCap plugin
   */
  async getTokenPriceData(token) {
    try {
      // Call the CoinMarketCap plugin to get price data
      const priceData = await this.runtime.callPlugin(this.coinmarketcapPlugin, 'GET_PRICE', {
        symbol: token
      });

      // If we got a valid response with price data
      if (priceData && priceData.content && priceData.content.tokenData) {
        return priceData.content.tokenData;
      }

      // Parse the text response if structured data isn't available
      if (priceData && priceData.text) {
        const priceMatch = priceData.text.match(/Price: \$([\d,.]+)/);
        const changeMatch = priceData.text.match(/24h Change: ([\d,.+-]+)%/);
        const volumeMatch = priceData.text.match(/24h Volume: \$([\d,.]+)/);

        if (priceMatch) {
          return {
            symbol: token,
            name: token,
            price: parseFloat(priceMatch[1].replace(/,/g, '')),
            priceChange24h: changeMatch ? parseFloat(changeMatch[1]) : 0,
            volume24h: volumeMatch ? parseFloat(volumeMatch[1].replace(/,/g, '')) : 0
          };
        }
      }

      return null;
    } catch (error) {
      console.error(`Error getting price data for ${token}:`, error);
      return null;
    }
  }

  /**
   * Format a price post with the token data
   */
  formatPricePost(token, priceData) {
    // Format price with appropriate precision
    const formatPrice = (price) => {
      if (typeof price !== 'number') return price;
      if (price < 0.001) return price.toFixed(8);
      if (price < 0.01) return price.toFixed(6);
      if (price < 1) return price.toFixed(4);
      if (price < 100) return price.toFixed(2);
      return price.toFixed(2);
    };

    // Format percentage change
    const formatPercentage = (change) => {
      if (typeof change !== 'number') return change;
      const prefix = change >= 0 ? '+' : '';
      return `${prefix}${change.toFixed(2)}%`;
    };

    // Format large numbers with commas
    const formatNumber = (num) => {
      if (typeof num !== 'number') return num;
      return new Intl.NumberFormat('en-US').format(num);
    };

    // Get price and change values
    const price = typeof priceData.price === 'number' ? priceData.price :
                 (priceData.quote && priceData.quote.USD ? priceData.quote.USD.price : 0);

    const priceChange = typeof priceData.priceChange24h === 'number' ? priceData.priceChange24h :
                       (priceData.quote && priceData.quote.USD ? priceData.quote.USD.percent_change_24h : 0);

    const volume = typeof priceData.volume24h === 'number' ? priceData.volume24h :
                  (priceData.quote && priceData.quote.USD ? priceData.quote.USD.volume_24h : 0);

    // Create different post templates with more variety and depth
    const templates = [
      `$${token} update: Currently trading at $${formatPrice(price)}. ${formatPercentage(priceChange)} in the last 24h with ${formatNumber(volume)} volume. What's your take on this movement? #Cardano ${token === 'ADA' ? '#ADA' : ''}`,

      `Just checked $${token}: $${formatPrice(price)} (${formatPercentage(priceChange)}). ${priceChange >= 0 ? 'Bullish momentum building' : 'Temporary pullback creating opportunities'}. How does this fit into your ${token === 'ADA' ? 'Cardano' : 'crypto'} strategy?`,

      `$${token} price action: $${formatPrice(price)} with ${formatPercentage(priceChange)} change. Volume: $${formatNumber(volume)}. This is where $MISTER's tools can help identify patterns that most traders miss.`,

      `Market update: $${token} at $${formatPrice(price)}. ${priceChange >= 0 ? 'Upward trend continues' : 'Consolidating before next move'}. Imagine connecting this data with other Cardano projects to create even more powerful insights.`,

      `$${token} showing ${priceChange >= 0 ? 'strength' : 'resilience'} at $${formatPrice(price)}. ${formatPercentage(priceChange)} in 24h. Which Cardano projects would you like to see $MISTER connect with to enhance the ecosystem?`,

      `$${token} trading at $${formatPrice(price)} (${formatPercentage(priceChange)}). The depth of Cardano's ecosystem continues to grow, with projects like $MISTER adding new layers of utility and innovation.`,

      `Looking at $${token}: $${formatPrice(price)} with ${formatPercentage(priceChange)} change. This is the kind of data that helps us understand how Cardano's ecosystem is evolving and maturing.`,

      `$${token} price: $${formatPrice(price)} | Change: ${formatPercentage(priceChange)} | Volume: $${formatNumber(volume)}. How could this token benefit from integration with Cardano's growing infrastructure?`
    ];

    // Select a random template
    const randomTemplate = templates[Math.floor(Math.random() * templates.length)];

    return randomTemplate;
  }

  /**
   * Generate a generic post about a token when price data isn't available
   */
  generateGenericTokenPost(token) {
    const templates = [
      `Keeping an eye on $${token} today. How do you see this token fitting into the broader Cardano ecosystem? What connections could enhance its utility? #Cardano #CryptoDiscussion`,

      `$${token} is on my radar. What other Cardano projects would complement its functionality? Looking for interesting integration ideas that could benefit the entire ecosystem.`,

      `Watching $${token} movements closely. The strength of Cardano lies in how its projects can interconnect and enhance each other. Which connections would you like to see?`,

      `$${token} showing interesting patterns today. The future of Cardano depends on projects that can work together to create more value than they could alone. What's your vision?`,

      `$${token} worth watching right now. As Cardano's ecosystem matures, the projects that find ways to connect and enhance each other will thrive. How could $MISTER help?`,

      `Thinking about $${token} and its place in the Cardano ecosystem. The most successful projects will be those that add value to the entire network. What's your take?`,

      `$${token} has caught my attention. The real power of Cardano comes from how its projects can work together. Which collaborations would you like to see happen?`,

      `Analyzing $${token} today. The depth of Cardano's ecosystem continues to impress me. Which projects are you most excited about and why?`
    ];

    // Select a random template
    const randomTemplate = templates[Math.floor(Math.random() * templates.length)];

    return randomTemplate;
  }

  /**
   * Generate a tweet about a random token price
   */
  async generateTweet() {
    return {
      content: await this.generateRandomTokenPricePost()
    };
  }
}

module.exports = TokenPriceTwitterPosts;
