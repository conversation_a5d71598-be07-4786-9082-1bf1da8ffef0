// CardanoSpacesAgent.js - Integration of TapTools with Twitter Spaces
const path = require('path');
const fs = require('fs');

class CardanoSpacesAgent {
  constructor(runtime) {
    this.runtime = runtime;
    this.taptoolsPlugin = runtime.plugins.find(p => p.name === 'taptoolsCardano');
    this.twitterPlugin = runtime.plugins.find(p => p.name === 'twitter');
    this.spacesData = {};
    
    // Ensure the knowledge directory exists
    const knowledgeDir = path.join(__dirname, '../knowledge/cardano');
    if (!fs.existsSync(knowledgeDir)) {
      fs.mkdirSync(knowledgeDir, { recursive: true });
    }
  }
  
  // Host a Twitter Space about Cardano
  async hostCardanoSpace(topic) {
    try {
      console.log(`Preparing to host a Twitter Space about: ${topic}`);
      
      // Create a Space
      const space = await this.runtime.callPlugin(this.twitterPlugin, 'createSpace', {
        title: `Cardano Talk: ${topic}`,
        scheduled_start: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
      });
      
      console.log(`Space created with ID: ${space.id}`);
      
      // Initialize space data tracking
      this.spacesData[space.id] = {
        topic,
        createdAt: new Date().toISOString(),
        talkingPoints: [],
        askedQuestions: []
      };
      
      // Prepare Cardano data for the Space
      await this.prepareCardanoData(space.id, topic);
      
      return space;
    } catch (error) {
      console.error('Error creating Cardano Space:', error);
      throw error;
    }
  }
  
  // Prepare relevant Cardano data before the Space starts
  async prepareCardanoData(spaceId, topic) {
    try {
      console.log(`Preparing Cardano data for Space ${spaceId} on topic: ${topic}`);
      
      // Determine relevant data to fetch based on topic
      if (topic.toLowerCase().includes('price') || 
          topic.toLowerCase().includes('market') || 
          topic.toLowerCase().includes('trend')) {
          
        // Get top volume tokens for market overview
        const topVolume = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { 
          limit: 10,
          period: '24h'
        });
        console.log(`Fetched ${topVolume.tokens?.length || 0} top volume tokens`);
        
        // Get recent price changes
        const priceChanges = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', {
          period: '24h'
        });
        console.log(`Fetched ${priceChanges.changes?.length || 0} token price changes`);
      }
      
      // If topic is about a specific token, get detailed data
      const tokenMatches = topic.match(/\b([A-Za-z0-9]+)\b/g);
      if (tokenMatches) {
        const possibleTokens = tokenMatches.filter(token => token.length >= 3);
        if (possibleTokens.length > 0) {
          // Get specific token data if tokens are mentioned in the topic
          const tokenPrices = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPrices', {
            tokens: possibleTokens
          });
          console.log(`Fetched data for specific tokens: ${possibleTokens.join(', ')}`);
        }
      }
      
      // Query our knowledge base to prepare talking points
      const talkingPoints = await this.runtime.callPlugin(this.taptoolsPlugin, 'queryCardanoKnowledge', {
        query: `Information about ${topic} in Cardano`,
        limit: 10
      });
      
      // Store these talking points for use during the Space
      if (talkingPoints.results && talkingPoints.results.length > 0) {
        this.spacesData[spaceId].talkingPoints = talkingPoints.results;
        console.log(`Prepared ${talkingPoints.results.length} talking points for the Space`);
      } else {
        console.log('No existing knowledge found, will rely on freshly fetched data');
      }
      
      // Save a summary of prepared data for reference
      this.saveSpacePreparationSummary(spaceId, topic);
    } catch (error) {
      console.error(`Error preparing Cardano data: ${error.message}`);
    }
  }
  
  // Save a summary of the prepared Space data
  saveSpacePreparationSummary(spaceId, topic) {
    try {
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filename = path.join(
        __dirname, 
        '../knowledge/cardano', 
        `space-${timestamp.substring(0, 10)}-${topic.replace(/\s+/g, '-').toLowerCase()}.md`
      );
      
      const spaceData = this.spacesData[spaceId];
      let summaryContent = `# Twitter Space: ${topic}\n\n`;
      summaryContent += `**Date:** ${new Date().toISOString()}\n`;
      summaryContent += `**Space ID:** ${spaceId}\n\n`;
      
      summaryContent += `## Prepared Talking Points\n\n`;
      if (spaceData.talkingPoints.length > 0) {
        spaceData.talkingPoints.forEach((point, index) => {
          summaryContent += `${index + 1}. ${point.content || point.text}\n`;
        });
      } else {
        summaryContent += `No pre-existing talking points found. Using fresh data.\n`;
      }
      
      fs.writeFileSync(filename, summaryContent);
      console.log(`Saved Space preparation summary to ${filename}`);
    } catch (error) {
      console.error(`Error saving Space preparation summary: ${error.message}`);
    }
  }
  
  // Method to speak in an active Space using prepared data
  async speakInSpace(spaceId, messageContext) {
    try {
      console.log(`Preparing to speak in Space ${spaceId} about: ${messageContext}`);
      
      if (!this.spacesData[spaceId]) {
        console.warn(`No data for Space ${spaceId}, initializing empty data`);
        this.spacesData[spaceId] = {
          topic: 'Cardano Discussion',
          createdAt: new Date().toISOString(),
          talkingPoints: [],
          askedQuestions: []
        };
      }
      
      // Track this question
      this.spacesData[spaceId].askedQuestions.push({
        question: messageContext,
        timestamp: new Date().toISOString()
      });
      
      // Get relevant knowledge based on the message context
      const relevantInfo = await this.runtime.callPlugin(this.taptoolsPlugin, 'queryCardanoKnowledge', {
        query: messageContext,
        limit: 3
      });
      
      // Formulate response using the knowledge
      let response = '';
      
      if (relevantInfo.results && relevantInfo.results.length > 0) {
        // We have relevant stored knowledge to use
        response = this.formulateResponseFromKnowledge(relevantInfo.results, messageContext);
      } else {
        // We don't have relevant stored knowledge, fetch fresh data
        response = await this.fetchFreshDataForResponse(messageContext);
      }
      
      console.log(`Prepared response: ${response.substring(0, 100)}...`);
      
      // Speak in the Space
      await this.runtime.callPlugin(this.twitterPlugin, 'speakInSpace', {
        spaceId,
        message: response
      });
      
      // Save this interaction
      this.saveSpaceInteraction(spaceId, messageContext, response);
      
      return true;
    } catch (error) {
      console.error(`Error speaking in Space: ${error.message}`);
      throw error;
    }
  }
  
  // Formulate a response based on stored knowledge
  formulateResponseFromKnowledge(knowledgeResults, context) {
    // Start with a friendly intro
    let response = "Based on our latest data, ";
    
    // Add the knowledge content
    response += knowledgeResults.map(r => r.content || r.text).join(" Additionally, ");
    
    // Add a conclusion based on the context
    if (context.toLowerCase().includes('price')) {
      response += " Remember that prices can be volatile, so always do your own research before making investment decisions.";
    } else if (context.toLowerCase().includes('project') || context.toLowerCase().includes('development')) {
      response += " The Cardano ecosystem continues to evolve rapidly, with new projects and updates regularly.";
    } else if (context.toLowerCase().includes('trend') || context.toLowerCase().includes('market')) {
      response += " Market trends can shift quickly, but the overall growth trajectory of the Cardano ecosystem has been positive.";
    }
    
    return response;
  }
  
  // Fetch fresh data when no relevant knowledge is found
  async fetchFreshDataForResponse(context) {
    let response = "Let me share the latest information about ";
    
    try {
      if (context.toLowerCase().includes('price') || context.toLowerCase().includes('value')) {
        // Context is about prices
        const tokenMatch = context.match(/\b([A-Za-z0-9]{3,})\b/g);
        const token = tokenMatch ? tokenMatch.find(t => t.toLowerCase() !== 'price' && t.toLowerCase() !== 'what' && t.toLowerCase() !== 'the') : null;
        
        if (token) {
          // Get specific token price
          const tokenData = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPrices', {
            tokens: [token]
          });
          
          if (tokenData.tokens && tokenData.tokens.length > 0) {
            const tokenInfo = tokenData.tokens[0];
            response += `${token}. The current price of ${tokenInfo.name} (${tokenInfo.ticker}) is $${tokenInfo.priceUsd} USD. `;
            
            // Get price change for context
            const priceChanges = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', {
              period: '24h',
              tokens: [token]
            });
            
            if (priceChanges.changes && priceChanges.changes.length > 0) {
              const change = priceChanges.changes[0];
              response += `In the last 24 hours, it has changed by ${change.percentChange}%. `;
            }
          } else {
            response += `${token}, but I couldn't find specific data for this token. Let me share some general market information instead. `;
          }
        } else {
          response += "Cardano market prices. ";
        }
        
        // Include general market info
        const topTokens = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { limit: 5 });
        if (topTokens.tokens && topTokens.tokens.length > 0) {
          response += `The top trading tokens on Cardano currently are ${topTokens.tokens.map(t => t.name).join(', ')}. `;
        }
        
      } else if (context.toLowerCase().includes('volume') || context.toLowerCase().includes('trading')) {
        // Context is about trading volume
        const volumeData = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { limit: 10 });
        
        if (volumeData.tokens && volumeData.tokens.length > 0) {
          response += `trading volumes on Cardano. `;
          response += `The highest volume tokens in the last 24 hours are: `;
          
          volumeData.tokens.slice(0, 5).forEach((token, idx) => {
            response += `${idx + 1}) ${token.name} with $${token.volume} USD in volume, `;
          });
          
          response += `showing strong market activity. `;
        } else {
          response += `trading volumes, but I couldn't retrieve the latest data. `;
        }
      } else {
        // General Cardano information
        response += `the Cardano ecosystem. `;
        response += `Cardano continues to develop its ecosystem with a focus on security, scalability, and sustainability. `;
        response += `The latest developments include progress on Hydra for scaling, increasing DeFi activity, and growing adoption of native tokens. `;
        
        // Include some price metrics
        const topTokens = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { limit: 3 });
        if (topTokens.tokens && topTokens.tokens.length > 0) {
          response += `Some of the most active tokens include ${topTokens.tokens.map(t => t.name).join(', ')}. `;
        }
      }
    } catch (error) {
      console.error(`Error fetching fresh data: ${error.message}`);
      response += "Cardano, though I'm having trouble retrieving the latest data. The ecosystem continues to grow with developments in DeFi, NFTs, and scalability solutions like Hydra.";
    }
    
    return response;
  }
  
  // Save Space interaction for future reference
  saveSpaceInteraction(spaceId, question, answer) {
    try {
      const spaceData = this.spacesData[spaceId];
      const topic = spaceData.topic;
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      
      const filename = path.join(
        __dirname, 
        '../knowledge/cardano/interactions', 
        `space-interaction-${timestamp.substring(0, 10)}.md`
      );
      
      // Create interactions directory if it doesn't exist
      const interactionsDir = path.join(__dirname, '../knowledge/cardano/interactions');
      if (!fs.existsSync(interactionsDir)) {
        fs.mkdirSync(interactionsDir, { recursive: true });
      }
      
      // Prepare content
      let content = '';
      
      // Check if file exists, append if it does
      if (fs.existsSync(filename)) {
        content = fs.readFileSync(filename, 'utf8');
        content += '\n\n---\n\n';
      } else {
        content = `# Twitter Space Interactions: ${topic}\n\n`;
        content += `**Date:** ${new Date().toISOString().substring(0, 10)}\n`;
        content += `**Space ID:** ${spaceId}\n\n`;
      }
      
      // Add this interaction
      content += `## Question at ${new Date().toISOString()}\n\n`;
      content += `**Q:** ${question}\n\n`;
      content += `**A:** ${answer}\n\n`;
      
      fs.writeFileSync(filename, content);
      console.log(`Saved Space interaction to ${filename}`);
    } catch (error) {
      console.error(`Error saving Space interaction: ${error.message}`);
    }
  }
  
  // End a Twitter Space session
  async endSpace(spaceId) {
    try {
      // End the Space through Twitter plugin
      await this.runtime.callPlugin(this.twitterPlugin, 'endSpace', {
        spaceId
      });
      
      // Generate a summary of the Space
      this.generateSpaceSummary(spaceId);
      
      console.log(`Ended Space ${spaceId} successfully`);
      return true;
    } catch (error) {
      console.error(`Error ending Space: ${error.message}`);
      throw error;
    }
  }
  
  // Generate a summary of the Space session
  generateSpaceSummary(spaceId) {
    try {
      const spaceData = this.spacesData[spaceId];
      if (!spaceData) {
        console.warn(`No data found for Space ${spaceId}`);
        return;
      }
      
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filename = path.join(
        __dirname, 
        '../knowledge/cardano', 
        `space-summary-${timestamp.substring(0, 10)}-${spaceData.topic.replace(/\s+/g, '-').toLowerCase()}.md`
      );
      
      let summaryContent = `# Twitter Space Summary: ${spaceData.topic}\n\n`;
      summaryContent += `**Date:** ${spaceData.createdAt}\n`;
      summaryContent += `**Space ID:** ${spaceId}\n`;
      summaryContent += `**Duration:** ${this.calculateDuration(spaceData.createdAt)}\n\n`;
      
      summaryContent += `## Questions and Topics Discussed\n\n`;
      if (spaceData.askedQuestions.length > 0) {
        spaceData.askedQuestions.forEach((q, index) => {
          summaryContent += `${index + 1}. **${q.question}** (asked at ${q.timestamp})\n`;
        });
      } else {
        summaryContent += `No questions were recorded for this Space session.\n`;
      }
      
      summaryContent += `\n## Key Points\n\n`;
      summaryContent += `- Discussed latest Cardano ecosystem developments\n`;
      summaryContent += `- Shared market data and token price information\n`;
      summaryContent += `- Addressed community questions about Cardano projects\n`;
      
      fs.writeFileSync(filename, summaryContent);
      console.log(`Generated Space summary at ${filename}`);
      
      // Clear the space data
      delete this.spacesData[spaceId];
    } catch (error) {
      console.error(`Error generating Space summary: ${error.message}`);
    }
  }
  
  // Calculate duration between space creation and now
  calculateDuration(startTime) {
    const start = new Date(startTime);
    const end = new Date();
    const durationMs = end - start;
    
    const minutes = Math.floor(durationMs / 60000);
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return `${hours} hours, ${remainingMinutes} minutes`;
    } else {
      return `${minutes} minutes`;
    }
  }
}

// Define actions for Space functionality
const hostCardanoSpaceAction = {
  name: "HOST_CARDANO_SPACE",
  similes: ["CREATE_CARDANO_SPACE", "START_CARDANO_SPACE"],
  description: "Creates and hosts a Twitter Space focused on Cardano discussion",
  
  validate: async (runtime, message) => {
    // Check if message contains Space hosting intent
    const spaceKeywords = [
      'host space', 'start space', 'create space', 'twitter space', 
      'talk about cardano', 'cardano space', 'voice chat'
    ];
    
    const text = message.content.text.toLowerCase();
    return spaceKeywords.some(keyword => text.includes(keyword));
  },
  
  handler: async (runtime, message, state) => {
    try {
      // Extract topic from message
      const text = message.content.text;
      let topic = "Cardano Discussion";
      
      // Try to identify a specific topic
      const topicMatches = text.match(/about\s+([^,.?!]+)/i) || 
                           text.match(/on\s+([^,.?!]+)/i) ||
                           text.match(/discussing\s+([^,.?!]+)/i);
      
      if (topicMatches && topicMatches[1]) {
        topic = topicMatches[1].trim();
      }
      
      // Find the plugin instance
      const plugin = runtime.plugins.find(p => p.name === 'cardanoSpacesAgent');
      
      if (!plugin || !plugin.instance) {
        return {
          text: "I'm having trouble setting up a Twitter Space right now. Please try again later."
        };
      }
      
      // Host the Space
      const space = await plugin.instance.hostCardanoSpace(topic);
      
      return {
        text: `I've created a Twitter Space called "Cardano Talk: ${topic}" scheduled to start in 10 minutes. Join me for a discussion about the latest in Cardano!`
      };
    } catch (error) {
      console.error("Error hosting Cardano Space:", error);
      return {
        text: "I encountered an issue while trying to create a Twitter Space. Please try again later."
      };
    }
  },
  
  examples: [
    [
      {
        user: "{{user1}}",
        content: {
          text: "Can you host a Twitter Space about Cardano development?"
        }
      },
      {
        user: "MISTER",
        content: {
          text: "I'll set up a Twitter Space for us to discuss Cardano development.",
          action: "HOST_CARDANO_SPACE"
        }
      }
    ]
  ]
};

const speakInCardanoSpaceAction = {
  name: "SPEAK_IN_CARDANO_SPACE",
  similes: ["ANSWER_IN_SPACE", "RESPOND_IN_CARDANO_SPACE"],
  description: "Responds to questions in a Twitter Space with Cardano data",
  
  validate: async (runtime, message) => {
    // This is typically triggered by the Twitter client, not direct messages
    // Always return false for direct validation
    return false;
  },
  
  handler: async (runtime, message, state) => {
    try {
      // Extract space ID and context from message
      const spaceId = message.content.spaceId;
      const messageContext = message.content.text;
      
      if (!spaceId) {
        return {
          text: "Missing Space ID in request"
        };
      }
      
      // Find the plugin instance
      const plugin = runtime.plugins.find(p => p.name === 'cardanoSpacesAgent');
      
      if (!plugin || !plugin.instance) {
        return {
          text: "Cardano Spaces agent is not available"
        };
      }
      
      // Speak in the Space
      await plugin.instance.speakInSpace(spaceId, messageContext);
      
      // No direct response needed as this is spoken in the Space
      return {
        text: "Responded in Space"
      };
    } catch (error) {
      console.error("Error speaking in Cardano Space:", error);
      return {
        text: "Error responding in Space"
      };
    }
  },
  
  examples: [
    [
      {
        user: "system",
        content: {
          text: "What's the latest on Cardano's smart contract capabilities?",
          spaceId: "1234567890"
        }
      },
      {
        user: "MISTER",
        content: {
          text: "Responding to question about Cardano's smart contracts...",
          action: "SPEAK_IN_CARDANO_SPACE"
        }
      }
    ]
  ]
};

// Export the plugin according to ElizaOS expected format
module.exports = {
  name: 'cardanoSpacesAgent',
  npmName: '@elizaos-plugins/cardano-spaces-agent',
  description: 'Enables hosting and participation in Twitter Spaces about Cardano',
  
  initialize: async function(runtime) {
    console.log('Initializing Cardano Spaces Agent plugin');
    
    try {
      // Create plugin instance
      this.instance = new CardanoSpacesAgent(runtime);
      
      // Register actions
      runtime.registerAction(hostCardanoSpaceAction);
      runtime.registerAction(speakInCardanoSpaceAction);
      
      return { success: true };
    } catch (error) {
      console.error('Error initializing Cardano Spaces Agent plugin:', error);
      return { success: false, error: error.message };
    }
  },
  
  // Expose methods directly through the plugin interface
  hostCardanoSpace: async function(topic) {
    return this.instance ? this.instance.hostCardanoSpace(topic) : null;
  },
  
  speakInSpace: async function(spaceId, messageContext) {
    return this.instance ? this.instance.speakInSpace(spaceId, messageContext) : null;
  },
  
  endSpace: async function(spaceId) {
    return this.instance ? this.instance.endSpace(spaceId) : null;
  },
  
  // Add actions
  actions: [hostCardanoSpaceAction, speakInCardanoSpaceAction]
}; 