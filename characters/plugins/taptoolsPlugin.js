// taptoolsPlugin.js for Eliza OS
const fetch = require('node-fetch');

class TaptoolsPlugin {
  constructor(config) {
    this.name = 'taptoolsCardano';
    // Store the provided API key
    this.apiKey = config.apiKey || 'WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO';
    this.baseUrl = 'https://api.taptools.io';
    
    // Token mapping (symbol -> unit)
    this.tokenUnitMap = new Map();
    
    // Cache for API responses
    this.cache = {
      topVolume: {
        data: null,
        timestamp: 0,
        expiryMs: 15 * 60 * 1000 // 15 minutes
      },
      priceChanges: {
        data: null,
        timestamp: 0,
        expiryMs: 5 * 60 * 1000 // 5 minutes
      },
      tokenPrices: {
        data: new Map(),
        timestamp: 0,
        expiryMs: 5 * 60 * 1000 // 5 minutes
      }
    };
    
    // Rate limiting
    this.rateLimiting = {
      lastRequestTime: 0,
      minTimeBetweenRequests: 500 // 500ms minimum between requests
    };
  }

  // Initialize the plugin
  async initialize() {
    console.log('Initializing TapTools Cardano Plugin');
    
    // Initialize token mapping
    try {
      await this.updateTokenMapping();
      console.log('Token mapping initialized successfully');
      return { success: true };
    } catch (error) {
      console.error(`Failed to initialize token mapping: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  // Get token prices with caching
  async getTokenPrices(params) {
    try {
      const { tokens, force = false } = params;
      
      if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
        throw new Error('Invalid tokens parameter. Must provide an array of token symbols or units.');
      }
      
      // Convert symbols to units if needed
      const tokenUnits = await Promise.all(tokens.map(token => this.resolveTokenUnit(token)));
      
      // Check cache for each token
      const uncachedTokens = [];
      const results = { tokens: [] };
      const now = Date.now();
      
      for (const unit of tokenUnits) {
        if (unit) {
          // Check if we have this token in cache and if it's still valid
          const cached = this.cache.tokenPrices.data.get(unit);
          if (!force && cached && (now - this.cache.tokenPrices.timestamp < this.cache.tokenPrices.expiryMs)) {
            results.tokens.push(cached);
          } else {
            uncachedTokens.push(unit);
          }
        }
      }
      
      // If we have tokens that need fetching
      if (uncachedTokens.length > 0) {
        const response = await this.makeApiCall('/token/prices', 'POST', {
          tokens: uncachedTokens
        });
        
        // Update cache
        if (response && response.tokens) {
          for (const token of response.tokens) {
            this.cache.tokenPrices.data.set(token.unit, token);
          }
          this.cache.tokenPrices.timestamp = now;
          
          // Add to results
          results.tokens.push(...response.tokens);
        }
      }
      
      return results;
    } catch (error) {
      console.error(`Error fetching token prices: ${error.message}`);
      throw error;
    }
  }

  // Get token price changes with caching
  async getTokenPriceChanges(params) {
    try {
      const { period = '24h', tokens, force = false } = params;
      const cacheKey = `${period}_${tokens ? tokens.join(',') : 'all'}`;
      const now = Date.now();
      
      // Check cache
      if (!force && 
          this.cache.priceChanges.data && 
          this.cache.priceChanges.data[cacheKey] && 
          (now - this.cache.priceChanges.timestamp < this.cache.priceChanges.expiryMs)) {
        return this.cache.priceChanges.data[cacheKey];
      }
      
      // Resolve token units if specific tokens are requested
      let requestParams = { period };
      if (tokens && tokens.length > 0) {
        const tokenUnits = await Promise.all(tokens.map(token => this.resolveTokenUnit(token)));
        requestParams.tokens = tokenUnits.filter(Boolean);
      }
      
      const response = await this.makeApiCall('/token/prices/chg', 'GET', requestParams);
      
      // Update cache
      if (!this.cache.priceChanges.data) {
        this.cache.priceChanges.data = {};
      }
      this.cache.priceChanges.data[cacheKey] = response;
      this.cache.priceChanges.timestamp = now;
      
      return response;
    } catch (error) {
      console.error(`Error fetching price changes: ${error.message}`);
      throw error;
    }
  }

  // Get top volume tokens with caching
  async getTopVolumeTokens(params) {
    try {
      const { limit = 10, period = '24h', force = false } = params;
      const cacheKey = `${period}_${limit}`;
      const now = Date.now();
      
      // Check cache
      if (!force && 
          this.cache.topVolume.data && 
          this.cache.topVolume.data[cacheKey] && 
          (now - this.cache.topVolume.timestamp < this.cache.topVolume.expiryMs)) {
        return this.cache.topVolume.data[cacheKey];
      }
      
      const response = await this.makeApiCall('/token/top/volume', 'GET', {
        limit,
        period
      });
      
      // Update cache
      if (!this.cache.topVolume.data) {
        this.cache.topVolume.data = {};
      }
      this.cache.topVolume.data[cacheKey] = response;
      this.cache.topVolume.timestamp = now;
      
      // Update token mapping with this data
      if (response && response.tokens) {
        for (const token of response.tokens) {
          if (token.ticker && token.unit) {
            this.tokenUnitMap.set(token.ticker.toLowerCase(), token.unit);
          }
        }
      }
      
      return response;
    } catch (error) {
      console.error(`Error fetching top volume tokens: ${error.message}`);
      throw error;
    }
  }

  // Update token symbol -> unit mapping
  async updateTokenMapping() {
    try {
      console.log('Updating token mapping...');
      
      // Fetch top 200 tokens by volume to build a comprehensive mapping
      const response = await this.makeApiCall('/token/top/volume', 'GET', {
        limit: 200,
        period: '30d'
      });
      
      if (response && response.tokens) {
        console.log(`Received ${response.tokens.length} tokens for mapping`);
        
        // Build the mapping
        for (const token of response.tokens) {
          if (token.ticker && token.unit) {
            this.tokenUnitMap.set(token.ticker.toLowerCase(), token.unit);
            // Also map by name for tokens without tickers
            if (token.name) {
              this.tokenUnitMap.set(token.name.toLowerCase(), token.unit);
            }
          }
        }
        
        console.log(`Token mapping updated with ${this.tokenUnitMap.size} entries`);
        return true;
      } else {
        console.warn('No token data received for mapping');
        return false;
      }
    } catch (error) {
      console.error(`Error updating token mapping: ${error.message}`);
      throw error;
    }
  }

  // Get unit for a token symbol
  async getTokenUnit(params) {
    const { symbol } = params;
    
    if (!symbol) {
      throw new Error('Symbol parameter is required');
    }
    
    return {
      symbol,
      unit: await this.resolveTokenUnit(symbol)
    };
  }

  // Resolve a token symbol to its unit
  async resolveTokenUnit(token) {
    // If it's already a unit (contains policy ID pattern), return it
    if (token.includes('policy') || (token.length > 40 && token.match(/^[0-9a-f]+$/i))) {
      return token;
    }
    
    const symbol = token.toLowerCase();
    
    // Check if we have it in our mapping
    if (this.tokenUnitMap.has(symbol)) {
      return this.tokenUnitMap.get(symbol);
    }
    
    // Special case for ADA
    if (symbol === 'ada' || symbol === 'cardano') {
      return 'lovelace';
    }
    
    // We don't have the mapping, try to update it
    await this.updateTokenMapping();
    
    // Check again after update
    if (this.tokenUnitMap.has(symbol)) {
      return this.tokenUnitMap.get(symbol);
    }
    
    console.warn(`Could not resolve unit for token: ${token}`);
    return null;
  }

  // Compare top volume tokens and provide analysis
  async compareTopTokens(params) {
    try {
      const { limit = 5, period = '24h', analysisType = 'investment' } = params;
      
      // Get top volume tokens
      const topVolume = await this.getTopVolumeTokens({
        limit: Math.max(limit * 2, 10), // Get more than we need for better analysis
        period
      });
      
      if (!topVolume || !topVolume.tokens || topVolume.tokens.length === 0) {
        throw new Error('Failed to fetch top volume tokens');
      }
      
      // Get price changes for these tokens
      const tokenSymbols = topVolume.tokens.map(t => t.ticker || t.name);
      const priceChanges = await this.getTokenPriceChanges({
        period,
        tokens: tokenSymbols
      });
      
      // Combine data for analysis
      const tokenData = topVolume.tokens.map(token => {
        const priceChange = priceChanges.changes?.find(c => 
          c.ticker === token.ticker || c.unit === token.unit
        );
        
        return {
          name: token.name,
          ticker: token.ticker,
          unit: token.unit,
          volume: token.volume,
          price: token.priceUsd,
          priceChange: priceChange ? priceChange.percentChange : null,
          liquidity: token.liquidity || 'unknown',
          marketCap: token.marketCap || (token.priceUsd * token.supply)
        };
      });
      
      // Perform analysis based on requested type
      let analysis;
      switch (analysisType.toLowerCase()) {
        case 'investment':
          analysis = this.analyzeForInvestment(tokenData);
          break;
        case 'momentum':
          analysis = this.analyzeMomentum(tokenData);
          break;
        case 'volatility':
          analysis = this.analyzeVolatility(tokenData);
          break;
        default:
          analysis = this.analyzeForInvestment(tokenData);
      }
      
      return analysis;
    } catch (error) {
      console.error(`Error comparing top tokens: ${error.message}`);
      throw error;
    }
  }

  // Analyze tokens for investment potential
  analyzeForInvestment(tokenData) {
    // Sort by a combination of factors
    const scoredTokens = tokenData.map(token => {
      // Simple scoring system - can be made more sophisticated
      let score = 0;
      
      // Volume is important
      score += Math.log10(token.volume + 1) * 2;
      
      // Price stability or growth is good
      if (token.priceChange > 0) {
        score += Math.min(token.priceChange, 30) * 0.5;
      } else if (token.priceChange < -30) {
        // Big drops might be concerning
        score -= Math.abs(token.priceChange) * 0.2;
      }
      
      // Liquidity is important
      if (typeof token.liquidity === 'number') {
        score += Math.log10(token.liquidity + 1);
      }
      
      return {
        ...token,
        score,
        recommendation: ''
      };
    });
    
    // Sort by score
    scoredTokens.sort((a, b) => b.score - a.score);
    
    // Take top tokens and add recommendations
    const topTokens = scoredTokens.slice(0, 5);
    
    // Add specific recommendations
    topTokens.forEach(token => {
      if (token.priceChange > 20) {
        token.recommendation = 'Strong momentum but watch for potential pullback';
      } else if (token.priceChange > 5) {
        token.recommendation = 'Positive trend with good volume support';
      } else if (token.priceChange < -20) {
        token.recommendation = 'Potential value opportunity if project fundamentals are strong';
      } else if (token.priceChange < -5) {
        token.recommendation = 'Monitor for stabilization before considering entry';
      } else {
        token.recommendation = 'Relatively stable with good trading volume';
      }
    });
    
    // Generate summary
    const summary = `Analysis of top volume Cardano tokens shows ${topTokens[0].ticker || topTokens[0].name} as the strongest performer based on volume, price action, and liquidity. ${topTokens[1].ticker || topTokens[1].name} and ${topTokens[2].ticker || topTokens[2].name} also show promising metrics.`;
    
    return {
      timestamp: new Date().toISOString(),
      topTokens,
      summary
    };
  }

  // Analyze tokens for momentum trading
  analyzeMomentum(tokenData) {
    // Sort primarily by recent price change
    const momentumTokens = tokenData
      .filter(token => token.priceChange !== null)
      .map(token => {
        // Momentum score combines price change with volume
        const momentumScore = (token.priceChange * 2) + Math.log10(token.volume + 1);
        
        return {
          ...token,
          momentumScore,
          trend: token.priceChange > 10 ? 'strong bullish' : 
                 token.priceChange > 5 ? 'bullish' :
                 token.priceChange > 0 ? 'slightly bullish' :
                 token.priceChange > -5 ? 'slightly bearish' :
                 token.priceChange > -10 ? 'bearish' : 'strong bearish'
        };
      });
    
    // Sort by momentum score
    momentumTokens.sort((a, b) => b.momentumScore - a.momentumScore);
    
    // Take top tokens
    const topMomentum = momentumTokens.slice(0, 5);
    
    // Generate summary
    const summary = `Momentum analysis shows ${topMomentum[0].ticker || topMomentum[0].name} leading with a ${topMomentum[0].trend} trend (${topMomentum[0].priceChange}% change) on high volume. ${topMomentum[1].ticker || topMomentum[1].name} and ${topMomentum[2].ticker || topMomentum[2].name} are also showing notable momentum.`;
    
    return {
      timestamp: new Date().toISOString(),
      topMomentum,
      summary
    };
  }

  // Analyze tokens for volatility
  analyzeVolatility(tokenData) {
    // We need price change data for this
    const volatilityTokens = tokenData
      .filter(token => token.priceChange !== null)
      .map(token => {
        // Use absolute price change as a simple volatility indicator
        const volatility = Math.abs(token.priceChange);
        
        return {
          ...token,
          volatility,
          volatilityCategory: volatility > 20 ? 'very high' :
                             volatility > 10 ? 'high' :
                             volatility > 5 ? 'moderate' :
                             'low'
        };
      });
    
    // Sort by volatility (highest first)
    volatilityTokens.sort((a, b) => b.volatility - a.volatility);
    
    // Take most volatile tokens
    const highVolatility = volatilityTokens.slice(0, 5);
    
    // And least volatile tokens
    const lowVolatility = [...volatilityTokens].sort((a, b) => a.volatility - b.volatility).slice(0, 3);
    
    // Generate summary
    const summary = `Volatility analysis shows ${highVolatility[0].ticker || highVolatility[0].name} as the most volatile token (${highVolatility[0].volatility}% change). For investors seeking stability, ${lowVolatility[0].ticker || lowVolatility[0].name} shows the lowest price volatility while maintaining good volume.`;
    
    return {
      timestamp: new Date().toISOString(),
      highVolatility,
      lowVolatility,
      summary
    };
  }

  // Query stored Cardano knowledge
  async queryCardanoKnowledge(params) {
    // Simple mock implementation that returns hardcoded data
    return {
      success: true,
      results: [
        {
          content: "SNEK is one of the top Cardano memecoins by volume and recognition.",
          metadata: { type: "token_info", ticker: "SNEK" }
        },
        {
          content: "Cardano's native tokens inherit the security of the main chain.",
          metadata: { type: "general_info" }
        }
      ]
    };
  }

  // Helper method to make API calls with rate limiting
  async makeApiCall(endpoint, method, params) {
    // Implement rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - this.rateLimiting.lastRequestTime;
    
    if (timeSinceLastRequest < this.rateLimiting.minTimeBetweenRequests) {
      const delay = this.rateLimiting.minTimeBetweenRequests - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.rateLimiting.lastRequestTime = Date.now();
    
    const url = `${this.baseUrl}${endpoint}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    };
    
    if (method === 'POST') {
      options.body = JSON.stringify(params);
    } else if (method === 'GET' && params) {
      // Convert params to query string
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => queryParams.append(key, v));
        } else {
          queryParams.append(key, value);
        }
      });
      
      const queryString = queryParams.toString();
      if (queryString) {
        url = `${url}?${queryString}`;
      }
    }
    
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Taptools API error: ${error.message}`);
      throw new Error(`Failed to fetch data from Taptools: ${error.message}`);
    }
  }
}

// Create provider implementation
const taptoolsProvider = {
  get: async (runtime, message, state) => {
    try {
      // Extract any Cardano-related queries from the message
      const content = message.content.text;
      const plugin = runtime.plugins.find(p => p.name === 'taptoolsCardano');
      
      if (!plugin || !plugin.instance) {
        return "TapTools data currently unavailable.";
      }
      
      // Simple keyword detection to determine what information to fetch
      let result = "";
      
      if (content.toLowerCase().includes('top') && content.toLowerCase().includes('volume')) {
        // Get top volume tokens
        const topTokens = await plugin.instance.getTopVolumeTokens({ limit: 5 });
        
        result = "Top Cardano tokens by volume:\n";
        if (topTokens && topTokens.tokens) {
          topTokens.tokens.forEach((token, index) => {
            result += `${index + 1}. ${token.name || token.ticker} - Volume: $${formatNumber(token.volume)}\n`;
          });
        }
      } else if (content.toLowerCase().includes('price') && content.toLowerCase().includes('change')) {
        // Get price changes
        const priceChanges = await plugin.instance.getTokenPriceChanges({ period: '24h' });
        
        result = "Recent Cardano token price changes:\n";
        if (priceChanges && priceChanges.tokens) {
          priceChanges.tokens.slice(0, 5).forEach((token, index) => {
            result += `${index + 1}. ${token.name || token.ticker} - ${token.change}%\n`;
          });
        }
      } else {
        // Default to general Cardano info
        result = await plugin.instance.queryCardanoKnowledge({ query: content });
      }
      
      return result;
    } catch (error) {
      console.error("Error in TapTools provider:", error);
      return "Unable to retrieve Cardano data at this time.";
    }
  }
};

// Helper function for number formatting
function formatNumber(num) {
  return new Intl.NumberFormat('en-US', { 
    maximumFractionDigits: 2,
    minimumFractionDigits: 0
  }).format(num);
}

// Export the plugin as expected by ElizaOS
module.exports = {
  name: 'taptoolsCardano',
  npmName: '@elizaos-plugins/taptoolsCardano',
  description: 'TapTools integration for Cardano blockchain data',
  
  // Configuration (will receive secret value from character file)
  config: {
    apiKey: process.env.TAPTOOLS_API_KEY
  },
  
  // Initialize method - called when the plugin is loaded
  initialize: async function(runtime) {
    console.log('Initializing TapTools Cardano plugin with ElizaOS runtime');
    
    try {
      // Get API key from character secrets or default
      const apiKey = runtime.getSetting('TAPTOOLS_API_KEY') || this.config.apiKey;
      
      // Create plugin instance
      this.instance = new TaptoolsPlugin({ apiKey });
      
      // Initialize the instance
      const initResult = await this.instance.initialize();
      
      // Register the provider
      runtime.providers.push(taptoolsProvider);
      
      return initResult;
    } catch (error) {
      console.error('Error initializing TapTools plugin:', error);
      return { success: false, error: error.message };
    }
  },
  
  // Provide methods that match the class methods
  getTokenPrices: async function(params) {
    return this.instance ? this.instance.getTokenPrices(params) : null;
  },
  
  getTokenPriceChanges: async function(params) {
    return this.instance ? this.instance.getTokenPriceChanges(params) : null;
  },
  
  getTopVolumeTokens: async function(params) {
    return this.instance ? this.instance.getTopVolumeTokens(params) : null;
  },
  
  compareTopTokens: async function(params) {
    return this.instance ? this.instance.compareTopTokens(params) : null;
  },
  
  queryCardanoKnowledge: async function(params) {
    return this.instance ? this.instance.queryCardanoKnowledge(params) : null;
  },
  
  // Add provider to make it accessible via standard ElizaOS pattern
  providers: [taptoolsProvider]
}; 