// taptoolsTwitterIntegration.js
// This plugin extends MISTER's Twitter capabilities to include TapTools Cardano data

/**
 * This integration enables MISTER to:
 * 1. Generate tweets about Cardano market data
 * 2. Respond to Cardano-related questions with TapTools data
 * 3. Include token analysis in Twitter posts
 * 4. Compare top tokens and share investment insights
 */

class TaptoolsTwitterIntegration {
  constructor(runtime) {
    this.runtime = runtime;
    this.taptoolsPlugin = runtime.plugins.find(p => p.name === 'taptoolsCardano');
  }

  /**
   * Handle direct mentions or questions about Cardano tokens
   * @param {string} question - The user's question
   * @returns {Promise<string>} A formatted response with TapTools data
   */
  async handleCardanoQuestion(question) {
    console.log(`Handling Cardano question: ${question}`);
    
    try {
      // Check if it's asking for token comparison
      if (this.isComparisonQuestion(question)) {
        return await this.generateTokenComparison(question);
      }
      
      // Check if it's asking about a specific token
      const tokenName = this.extractTokenName(question);
      if (tokenName) {
        return await this.generateTokenInfo(tokenName);
      }
      
      // Check if it's asking about market trends
      if (this.isMarketTrendQuestion(question)) {
        return await this.generateMarketTrends();
      }
      
      // Default to general Cardano market info
      return await this.generateGeneralCardanoInfo();
    } catch (error) {
      console.error('Error handling Cardano question:', error);
      return "I'm having trouble accessing the latest Cardano data right now. Let me get back to you on this soon!";
    }
  }

  /**
   * Check if the question is asking for token comparison
   * @param {string} question - The user's question
   * @returns {boolean} True if it's a comparison question
   */
  isComparisonQuestion(question) {
    const comparisonKeywords = [
      'compare', 'comparison', 'versus', 'vs', 'best', 'top', 'rank', 'ranking',
      'strongest', 'weakest', 'most promising', 'highest volume', 'analyze', 'analysis'
    ];
    
    return comparisonKeywords.some(keyword => 
      question.toLowerCase().includes(keyword)
    );
  }

  /**
   * Check if the question is about market trends
   * @param {string} question - The user's question
   * @returns {boolean} True if it's a market trend question
   */
  isMarketTrendQuestion(question) {
    const trendKeywords = [
      'trend', 'market', 'volume', 'moving', 'momentum', 'bullish', 'bearish',
      'trading', 'performance', 'growing', 'declining', 'up', 'down'
    ];
    
    return trendKeywords.some(keyword => 
      question.toLowerCase().includes(keyword)
    );
  }

  /**
   * Extract token name from question
   * @param {string} question - The user's question
   * @returns {string|null} The extracted token name or null
   */
  extractTokenName(question) {
    // List of common Cardano tokens to look for
    const commonTokens = [
      'ADA', 'SNEK', 'HOSKY', 'SUNDAE', 'WMT', 'MIN', 'LQ', 'MELD',
      'AGIX', 'DJED', 'MILK', 'INDY', 'BOOK', 'NMKR', 'NTX', 'LENFI'
    ];
    
    // First look for common token names in the question
    for (const token of commonTokens) {
      const regex = new RegExp(`\\b${token}\\b`, 'i');
      if (regex.test(question)) {
        return token;
      }
    }
    
    // Then try to extract any capitalized word that might be a token ticker
    const tickerRegex = /\b[A-Z]{2,6}\b/g;
    const potentialTickers = question.match(tickerRegex);
    
    if (potentialTickers && potentialTickers.length > 0) {
      // Filter out common words that might be all caps
      const filteredTickers = potentialTickers.filter(ticker => 
        !['USD', 'FOR', 'THE', 'API', 'IM', 'IS', 'ARE', 'NFT', 'AT'].includes(ticker)
      );
      
      if (filteredTickers.length > 0) {
        return filteredTickers[0];
      }
    }
    
    return null;
  }

  /**
   * Generate information about a specific token
   * @param {string} tokenName - The token name/ticker
   * @returns {Promise<string>} Formatted token information
   */
  async generateTokenInfo(tokenName) {
    console.log(`Generating info for token: ${tokenName}`);
    
    try {
      // Get current price
      const priceData = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPrices', {
        tokens: [tokenName]
      });
      
      // Get price changes
      const priceChanges = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', {
        period: '24h',
        tokens: [tokenName]
      });
      
      // Format the response
      if (priceData?.tokens && priceData.tokens.length > 0) {
        const token = priceData.tokens[0];
        const change = priceChanges?.changes?.find(c => c.ticker === token.ticker) || { percentChange: 'unknown' };
        
        let response = `📊 ${token.name} (${token.ticker}) Analysis:\n\n`;
        response += `Current Price: $${parseFloat(token.priceUsd).toFixed(6)} USD\n`;
        
        if (typeof change.percentChange === 'number') {
          const changeEmoji = change.percentChange >= 0 ? '📈' : '📉';
          response += `24h Change: ${changeEmoji} ${change.percentChange.toFixed(2)}%\n`;
        }
        
        if (token.volume) {
          response += `Volume: $${this.formatNumber(token.volume)} USD\n`;
        }
        
        // Add some market context
        const topVolume = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', { limit: 20 });
        
        if (topVolume?.tokens) {
          const rankByVolume = topVolume.tokens.findIndex(t => t.ticker === token.ticker) + 1;
          if (rankByVolume > 0) {
            response += `Volume Rank: #${rankByVolume} on Cardano\n`;
          }
        }
        
        response += `\nData from TapTools API | ${new Date().toLocaleString()}`;
        return response;
      } else {
        return `I couldn't find current data for ${tokenName}. It may not be tracked by TapTools or might be misspelled.`;
      }
    } catch (error) {
      console.error(`Error generating token info for ${tokenName}:`, error);
      return `I tried looking up ${tokenName}, but ran into an issue with the data source. Please try again later.`;
    }
  }

  /**
   * Generate comparison of top tokens
   * @param {string} question - The user's question to determine comparison type
   * @returns {Promise<string>} Formatted token comparison
   */
  async generateTokenComparison(question) {
    console.log('Generating token comparison');
    
    // Determine the type of analysis to perform
    let analysisType = 'investment';
    
    if (question.toLowerCase().includes('momentum') || 
        question.toLowerCase().includes('trend') || 
        question.toLowerCase().includes('moving')) {
      analysisType = 'momentum';
    } else if (question.toLowerCase().includes('volatil') || 
               question.toLowerCase().includes('stable') || 
               question.toLowerCase().includes('risk')) {
      analysisType = 'volatility';
    }
    
    try {
      // Get the comparison data
      const comparison = await this.runtime.callPlugin(this.taptoolsPlugin, 'compareTopTokens', {
        limit: 5,
        period: '24h',
        analysisType
      });
      
      // Format the response based on analysis type
      let response = '';
      
      if (analysisType === 'investment') {
        response = this.formatInvestmentComparison(comparison);
      } else if (analysisType === 'momentum') {
        response = this.formatMomentumComparison(comparison);
      } else if (analysisType === 'volatility') {
        response = this.formatVolatilityComparison(comparison);
      }
      
      return response;
    } catch (error) {
      console.error('Error generating token comparison:', error);
      return "I'm having trouble comparing the tokens right now. Please try again later.";
    }
  }

  /**
   * Format investment comparison for Twitter
   * @param {Object} comparison - The comparison data
   * @returns {string} Formatted comparison
   */
  formatInvestmentComparison(comparison) {
    let response = `🔍 Top Cardano Tokens Analysis\n\n`;
    
    if (comparison?.topTokens && comparison.topTokens.length > 0) {
      response += `Here are the top ${comparison.topTokens.length} tokens by overall score:\n\n`;
      
      comparison.topTokens.forEach((token, index) => {
        const changeEmoji = token.priceChange >= 0 ? '📈' : '📉';
        response += `${index + 1}. ${token.ticker || token.name} - $${parseFloat(token.price).toFixed(6)}\n`;
        response += `   ${changeEmoji} ${token.priceChange?.toFixed(2) || '0'}% | Vol: $${this.formatNumber(token.volume)}\n`;
        response += `   ${token.recommendation}\n\n`;
      });
      
      response += `Data from TapTools API | ${new Date().toLocaleString()}`;
    } else {
      response += "I couldn't retrieve the token ranking data at this time. Please try again later.";
    }
    
    return response;
  }

  /**
   * Format momentum comparison for Twitter
   * @param {Object} comparison - The comparison data
   * @returns {string} Formatted comparison
   */
  formatMomentumComparison(comparison) {
    let response = `🚀 Cardano Momentum Leaders\n\n`;
    
    if (comparison?.topMomentum && comparison.topMomentum.length > 0) {
      response += `Top ${comparison.topMomentum.length} tokens by price momentum:\n\n`;
      
      comparison.topMomentum.forEach((token, index) => {
        const trendEmoji = token.priceChange > 10 ? '🔥' : 
                         token.priceChange > 0 ? '📈' : '📉';
        
        response += `${index + 1}. ${token.ticker || token.name} - ${trendEmoji} ${token.trend}\n`;
        response += `   ${token.priceChange?.toFixed(2) || '0'}% | Vol: $${this.formatNumber(token.volume)}\n\n`;
      });
      
      response += `Data from TapTools API | ${new Date().toLocaleString()}`;
    } else {
      response += "I couldn't retrieve the momentum data at this time. Please try again later.";
    }
    
    return response;
  }

  /**
   * Format volatility comparison for Twitter
   * @param {Object} comparison - The comparison data
   * @returns {string} Formatted comparison
   */
  formatVolatilityComparison(comparison) {
    let response = `📊 Cardano Volatility Analysis\n\n`;
    
    if (comparison?.highVolatility && comparison.highVolatility.length > 0) {
      response += `Highest Volatility:\n`;
      
      comparison.highVolatility.slice(0, 3).forEach((token, index) => {
        response += `${index + 1}. ${token.ticker || token.name} - ${token.volatility.toFixed(2)}% (${token.volatilityCategory})\n`;
      });
      
      response += `\nLowest Volatility:\n`;
      
      comparison.lowVolatility.slice(0, 3).forEach((token, index) => {
        response += `${index + 1}. ${token.ticker || token.name} - ${token.volatility.toFixed(2)}% (${token.volatilityCategory})\n`;
      });
      
      response += `\nData from TapTools API | ${new Date().toLocaleString()}`;
    } else {
      response += "I couldn't retrieve the volatility data at this time. Please try again later.";
    }
    
    return response;
  }

  /**
   * Generate market trends report
   * @returns {Promise<string>} Formatted market trends
   */
  async generateMarketTrends() {
    console.log('Generating market trends');
    
    try {
      // Get top volume tokens
      const topVolume = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', {
        limit: 10,
        period: '24h'
      });
      
      // Get price changes for perspective
      const priceChanges = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTokenPriceChanges', {
        period: '24h'
      });
      
      // Generate the response
      let response = `📈 Cardano Market Trends\n\n`;
      
      if (topVolume?.tokens && topVolume.tokens.length > 0) {
        // Calculate some aggregate stats
        const totalVolume = topVolume.tokens.reduce((sum, token) => sum + token.volume, 0);
        const positiveChanges = priceChanges?.changes?.filter(c => c.percentChange > 0) || [];
        const positivePercentage = priceChanges?.changes ? 
          (positiveChanges.length / priceChanges.changes.length * 100).toFixed(0) : '?';
        
        response += `24h Overview:\n`;
        response += `Total Volume: $${this.formatNumber(totalVolume)} USD\n`;
        response += `Bullish Tokens: ${positivePercentage}% (${positiveChanges.length || '?'}/${priceChanges?.changes?.length || '?'})\n\n`;
        
        response += `Top Volume Tokens:\n`;
        topVolume.tokens.slice(0, 5).forEach((token, index) => {
          const change = priceChanges?.changes?.find(c => c.ticker === token.ticker);
          const changeText = change ? `${change.percentChange > 0 ? '+' : ''}${change.percentChange.toFixed(2)}%` : 'n/a';
          const changeEmoji = change && change.percentChange >= 0 ? '📈' : '📉';
          
          response += `${index + 1}. ${token.ticker || token.name}: $${this.formatNumber(token.volume)} ${changeEmoji} ${changeText}\n`;
        });
        
        response += `\nData from TapTools API | ${new Date().toLocaleString()}`;
      } else {
        response += "I couldn't retrieve market trend data at this time. Please try again later.";
      }
      
      return response;
    } catch (error) {
      console.error('Error generating market trends:', error);
      return "I'm having trouble accessing market trend data right now. Please try again later.";
    }
  }

  /**
   * Generate general Cardano information
   * @returns {Promise<string>} Formatted general information
   */
  async generateGeneralCardanoInfo() {
    console.log('Generating general Cardano info');
    
    try {
      // Get top volume tokens for context
      const topVolume = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', {
        limit: 5,
        period: '24h'
      });
      
      // Get ADA price data from CoinMarketCap plugin
      let adaPrice = null;
      let adaChange24h = null;
      try {
        const coinmarketcapPlugin = this.runtime.plugins.find(p => p.name === 'coinmarketcap');
        if (coinmarketcapPlugin) {
          const cmcData = await this.runtime.callPlugin(coinmarketcapPlugin, 'getCoinData', { symbol: 'ADA' });
          if (cmcData && cmcData.data && cmcData.data.ADA) {
            adaPrice = cmcData.data.ADA.quote.USD.price;
            adaChange24h = cmcData.data.ADA.quote.USD.percent_change_24h;
          }
        }
      } catch (cmcError) {
        console.error('Error fetching ADA price from CoinMarketCap:', cmcError);
      }
      
      let response = `🌊 Cardano Ecosystem Update\n\n`;
      
      // Add ADA price info if available
      if (adaPrice !== null) {
        const priceFormatted = adaPrice.toFixed(3);
        const changeSymbol = adaChange24h >= 0 ? '↗️' : '↘️';
        const changeFormatted = Math.abs(adaChange24h).toFixed(2);
        response += `$ADA: $${priceFormatted} ${changeSymbol} ${changeFormatted}% (24h)\n\n`;
      }
      
      if (topVolume?.tokens && topVolume.tokens.length > 0) {
        response += `Current Top Tokens by Volume:\n`;
        
        topVolume.tokens.forEach((token, index) => {
          response += `${index + 1}. ${token.ticker || token.name}: $${this.formatNumber(token.volume)}\n`;
        });
        
        // Add some general context about the ecosystem
        response += `\nThe Cardano ecosystem continues to grow with active development on scaling solutions like Hydra, expanding DeFi applications, and a vibrant native asset ecosystem.\n\n`;
        response += `Data from TapTools API | ${new Date().toLocaleString()}`;
      } else {
        response += "I couldn't retrieve current Cardano market data at this time. The ecosystem continues to develop with a focus on security, scalability, and sustainability.";
      }
      
      return response;
    } catch (error) {
      console.error('Error generating general Cardano info:', error);
      return "I'm having trouble accessing Cardano data right now. Please try again later.";
    }
  }

  /**
   * Generate a scheduled tweet about Cardano
   * @returns {Promise<string>} Formatted tweet text
   */
  async generateCardanoTweet() {
    console.log('Generating Cardano tweet');
    
    // Choose a random tweet type for variety
    const tweetTypes = ['marketOverview', 'topPerformers', 'tokenSpotlight', 'trendAnalysis', 'adaPrice'];
    const tweetType = tweetTypes[Math.floor(Math.random() * tweetTypes.length)];
    
    try {
      switch (tweetType) {
        case 'marketOverview':
          return await this.generateMarketTrends();
          
        case 'topPerformers':
          return await this.generateTokenComparison('Compare top performers');
          
        case 'tokenSpotlight':
          // Get a random top 10 token to spotlight
          const topTokens = await this.runtime.callPlugin(this.taptoolsPlugin, 'getTopVolumeTokens', {
            limit: 10,
            period: '24h'
          });
          
          if (topTokens?.tokens && topTokens.tokens.length > 0) {
            const randomToken = topTokens.tokens[Math.floor(Math.random() * Math.min(topTokens.tokens.length, 5))];
            return await this.generateTokenInfo(randomToken.ticker || randomToken.name);
          } else {
            return await this.generateGeneralCardanoInfo();
          }
        
        case 'adaPrice':
          // Generate a focused tweet about ADA price and market performance
          try {
            const coinmarketcapPlugin = this.runtime.plugins.find(p => p.name === 'coinmarketcap');
            if (coinmarketcapPlugin) {
              const cmcData = await this.runtime.callPlugin(coinmarketcapPlugin, 'getCoinData', { symbol: 'ADA' });
              if (cmcData && cmcData.data && cmcData.data.ADA) {
                const adaData = cmcData.data.ADA;
                const price = adaData.quote.USD.price.toFixed(3);
                const change24h = adaData.quote.USD.percent_change_24h.toFixed(2);
                const change7d = adaData.quote.USD.percent_change_7d.toFixed(2);
                const marketCap = (adaData.quote.USD.market_cap / 1000000000).toFixed(2);
                const volume24h = (adaData.quote.USD.volume_24h / 1000000).toFixed(2);
                
                const changeSymbol24h = adaData.quote.USD.percent_change_24h >= 0 ? '↗️' : '↘️';
                const changeSymbol7d = adaData.quote.USD.percent_change_7d >= 0 ? '↗️' : '↘️';
                
                let mood = '';
                if (adaData.quote.USD.percent_change_24h > 5) mood = 'Cardano surging! ';
                else if (adaData.quote.USD.percent_change_24h > 2) mood = 'Cardano showing strength. ';
                else if (adaData.quote.USD.percent_change_24h < -5) mood = 'Cardano seeing pressure. ';
                else if (adaData.quote.USD.percent_change_24h < -2) mood = 'Cardano facing headwinds. ';
                else mood = 'Cardano market update: ';
                
                return `${mood}$ADA at $${price} ${changeSymbol24h} ${Math.abs(change24h)}% (24h) ${changeSymbol7d} ${Math.abs(change7d)}% (7d)\n\nMarket Cap: $${marketCap}B\nVolume: $${volume24h}M\n\n#Cardano #ADA #Crypto`;
              }
            }
          } catch (error) {
            console.error('Error generating ADA price tweet:', error);
          }
          return await this.generateGeneralCardanoInfo();
          
        case 'trendAnalysis':
          return await this.generateTokenComparison('Analyze momentum trends');
          
        default:
          return await this.generateGeneralCardanoInfo();
      }
    } catch (error) {
      console.error('Error generating Cardano tweet:', error);
      return "Cardano ecosystem continues to grow with exciting projects and development. What tokens are you currently watching? #Cardano #ADA";
    }
  }

  /**
   * Format large numbers for display
   * @param {number} num - The number to format
   * @returns {string} Formatted number
   */
  formatNumber(num) {
    if (typeof num !== 'number') return 'n/a';
    
    if (num >= 1_000_000_000) {
      return `${(num / 1_000_000_000).toFixed(2)}B`;
    } else if (num >= 1_000_000) {
      return `${(num / 1_000_000).toFixed(2)}M`;
    } else if (num >= 1_000) {
      return `${(num / 1_000).toFixed(2)}K`;
    } else {
      return num.toFixed(2);
    }
  }
}

// Define actions according to ElizaOS Action interface
const handleCardanoQuestionAction = {
  name: "HANDLE_CARDANO_QUESTION",
  similes: ["ANALYZE_CARDANO", "PROCESS_CARDANO_QUERY"],
  description: "Handles questions about Cardano blockchain data using TapTools API",
  
  validate: async (runtime, message) => {
    // Check if the message contains Cardano-related keywords
    const cardanoKeywords = [
      'cardano', 'ada', 'snek', 'hosky', 'djed', 'sundae', 'meld', 'indy',
      'token', 'blockchain', 'cryptocurrency', 'crypto', 'defi'
    ];
    
    const text = message.content.text.toLowerCase();
    return cardanoKeywords.some(keyword => text.includes(keyword));
  },
  
  handler: async (runtime, message, state) => {
    try {
      // Find the plugin instance
      const plugin = runtime.plugins.find(p => p.name === 'taptoolsTwitterIntegration');
      
      if (!plugin || !plugin.instance) {
        return {
          text: "I'm having trouble accessing the Cardano data right now. Let me get back to you on this soon!"
        };
      }
      
      // Process the question
      const answer = await plugin.instance.handleCardanoQuestion(message.content.text);
      
      // Store the retrieved data in RAG knowledge if enabled
      if (runtime.character.settings?.ragKnowledge === true) {
        try {
          const knowledgeId = `cardano-data-${Date.now()}`;
          await runtime.ragKnowledgeManager.createKnowledge({
            id: knowledgeId,
            agentId: runtime.agentId,
            content: {
              text: answer,
              metadata: {
                source: "TapTools API",
                type: "cardano-data",
                retrievedAt: Date.now(),
                query: message.content.text
              }
            },
            createdAt: Date.now()
          });
        } catch (error) {
          console.error("Error storing Cardano data in RAG knowledge:", error);
        }
      }
      
      return {
        text: answer
      };
    } catch (error) {
      console.error("Error handling Cardano question:", error);
      return {
        text: "I encountered an issue while processing Cardano data. Please try again later."
      };
    }
  },
  
  examples: [
    [
      {
        user: "{{user1}}",
        content: {
          text: "What's the current price of ADA?"
        }
      },
      {
        user: "MISTER",
        content: {
          text: "Let me check the latest data for you...",
          action: "HANDLE_CARDANO_QUESTION"
        }
      }
    ],
    [
      {
        user: "{{user1}}",
        content: {
          text: "How is SNEK performing today?"
        }
      },
      {
        user: "MISTER",
        content: {
          text: "Analyzing SNEK performance data...",
          action: "HANDLE_CARDANO_QUESTION"
        }
      }
    ]
  ]
};

const generateCardanoTweetAction = {
  name: "GENERATE_CARDANO_TWEET",
  similes: ["CREATE_CARDANO_POST", "PUBLISH_CARDANO_UPDATE"],
  description: "Generates a Twitter post with current Cardano market data",
  
  validate: async (runtime, message) => {
    // This action is not triggered by user messages but by templates
    // Always return false for direct validation
    return false;
  },
  
  handler: async (runtime, message, state) => {
    try {
      // Find the plugin instance
      const plugin = runtime.plugins.find(p => p.name === 'taptoolsTwitterIntegration');
      
      if (!plugin || !plugin.instance) {
        return {
          text: "Unable to generate Cardano market update at this time."
        };
      }
      
      // Generate the tweet content
      const tweetContent = await plugin.instance.generateCardanoTweet();
      
      return {
        text: tweetContent
      };
    } catch (error) {
      console.error("Error generating Cardano tweet:", error);
      return {
        text: "I encountered an issue while generating a Cardano update. Will try again later."
      };
    }
  },
  
  examples: [
    [
      {
        user: "system",
        content: {
          text: "Generate a Cardano market update for Twitter."
        }
      },
      {
        user: "MISTER",
        content: {
          text: "Generating Cardano market update...",
          action: "GENERATE_CARDANO_TWEET"
        }
      }
    ]
  ]
};

// Export the plugin according to ElizaOS expected format
module.exports = {
  name: 'cardanoTwitterIntegration',
  npmName: '@elizaos-plugins/cardano-twitter-integration',
  description: 'Integrates TapTools Cardano data with Twitter functionality',
  
  initialize: async function(runtime) {
    console.log('Initializing Cardano Twitter Integration plugin');
    
    try {
      // Create plugin instance
      this.instance = new TaptoolsTwitterIntegration(runtime);
      
      // Register actions
      runtime.registerAction(handleCardanoQuestionAction);
      runtime.registerAction(generateCardanoTweetAction);
      
      return { success: true };
    } catch (error) {
      console.error('Error initializing Cardano Twitter Integration plugin:', error);
      return { success: false, error: error.message };
    }
  },
  
  // Expose methods directly through the plugin interface
  handleCardanoQuestion: async function(question) {
    return this.instance ? this.instance.handleCardanoQuestion(question) : null;
  },
  
  generateCardanoTweet: async function() {
    return this.instance ? this.instance.generateCardanoTweet() : null;
  },
  
  // Add actions
  actions: [handleCardanoQuestionAction, generateCardanoTweetAction]
}; 