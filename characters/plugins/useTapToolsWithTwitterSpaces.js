// Example of using TapTools with Twitter and Twitter Spaces
const ElizaOS = require('eliza-os');
const TaptoolsPlugin = require('./taptoolsPlugin');
const CardanoSpacesAgent = require('./cardanoSpacesAgent');
const TaptoolsTwitterIntegration = require('./taptoolsTwitterIntegration');

/**
 * This example demonstrates how to use the TapTools plugin with Twitter functionality
 * It shows how to:
 * 1. Initialize the TapTools plugin
 * 2. Create a CardanoSpacesAgent for Twitter Spaces
 * 3. Use the TaptoolsTwitterIntegration for regular tweets and replies
 * 4. Host a Twitter Space about Cardano
 * 5. Respond to questions in the Space
 * 6. Generate Cardano-related tweets
 * 7. <PERSON><PERSON> questions in direct messages or replies
 */

async function runCardanoTwitterExample() {
  try {
    console.log('Initializing Eliza OS...');
    
    // Initialize Eliza OS with the TapTools plugin
    const elizaOS = new ElizaOS({
      plugins: [
        new TaptoolsPlugin({
          apiKey: "WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO" // API key provided by the user
        })
      ]
    });
    
    // Create the Cardano Spaces Agent
    const spacesAgent = new CardanoSpacesAgent(elizaOS);
    
    // Create the Twitter Integration
    const twitterIntegration = new TaptoolsTwitterIntegration(elizaOS);
    
    // PART 1: Twitter Spaces Example
    console.log('\n===== TWITTER SPACES EXAMPLE =====\n');
    
    // Host a Twitter Space about Cardano
    console.log('Creating a Twitter Space about Cardano market trends...');
    const space = await spacesAgent.hostCardanoSpace('Current Cardano Market Trends');
    console.log(`Space created with ID: ${space.id}`);
    
    // Simulate responding to questions in the Space
    console.log('\nSimulating questions in the Space...\n');
    
    // Question 1: ADA price
    console.log('Question 1: What\'s happening with ADA price today?');
    await spacesAgent.speakInSpace(space.id, "What's happening with ADA price today?");
    
    // Question 2: Top volume tokens
    console.log('\nQuestion 2: Which Cardano tokens have the highest trading volume?');
    await spacesAgent.speakInSpace(space.id, "Which Cardano tokens have the highest trading volume?");
    
    // Question 3: Specific token - SNEK
    console.log('\nQuestion 3: Tell me about SNEK token\'s performance');
    await spacesAgent.speakInSpace(space.id, "Tell me about SNEK token's performance");
    
    // End the Space
    console.log('\nEnding the Space...');
    await spacesAgent.endSpace(space.id);
    
    // PART 2: Regular Twitter Functionality Example
    console.log('\n===== REGULAR TWITTER FUNCTIONALITY EXAMPLE =====\n');
    
    // Example 1: Generate a tweet about Cardano
    console.log('Example 1: Generating a tweet about Cardano');
    const tweetText = await twitterIntegration.generateCardanoTweet();
    console.log('\nGenerated Tweet:');
    console.log('----------------------------------------');
    console.log(tweetText);
    console.log('----------------------------------------');
    
    // Example 2: Handle a specific token question
    console.log('\nExample 2: Handling a question about SNEK token');
    const snekResponse = await twitterIntegration.handleCardanoQuestion("What's the current price of SNEK token?");
    console.log('\nResponse to SNEK question:');
    console.log('----------------------------------------');
    console.log(snekResponse);
    console.log('----------------------------------------');
    
    // Example 3: Handle a comparison question
    console.log('\nExample 3: Handling a comparison question');
    const comparisonResponse = await twitterIntegration.handleCardanoQuestion("Can you compare the top Cardano tokens by volume?");
    console.log('\nResponse to comparison question:');
    console.log('----------------------------------------');
    console.log(comparisonResponse);
    console.log('----------------------------------------');
    
    // Example 4: Handle a market trends question
    console.log('\nExample 4: Handling a market trends question');
    const trendsResponse = await twitterIntegration.handleCardanoQuestion("What are the market trends in Cardano today?");
    console.log('\nResponse to market trends question:');
    console.log('----------------------------------------');
    console.log(trendsResponse);
    console.log('----------------------------------------');
    
    console.log('\nAll examples completed successfully!');
    console.log('Check the knowledge directory for saved data and interactions.');
    
  } catch (error) {
    console.error('Error running Cardano Twitter example:', error);
  }
}

/**
 * Simulate how MISTER would handle tweets and replies about Cardano
 */
async function simulateMisterTwitterFlow() {
  try {
    console.log('\n===== SIMULATING MISTER TWITTER FLOW =====\n');
    
    // Initialize Eliza OS with the TapTools plugin
    const elizaOS = new ElizaOS({
      plugins: [
        new TaptoolsPlugin({
          apiKey: "WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO"
        })
      ]
    });
    
    // Create the Twitter Integration
    const twitterIntegration = new TaptoolsTwitterIntegration(elizaOS);
    
    // Simulate MISTER's tweet generation flow
    console.log('Simulating MISTER generating scheduled tweets...');
    
    // Generate a few different types of tweets
    const tweetTypes = ['Market Overview', 'Token Spotlight', 'Token Comparison'];
    
    for (const type of tweetTypes) {
      console.log(`\nGenerating a "${type}" tweet:`);
      const tweetText = await twitterIntegration.generateCardanoTweet();
      
      console.log('----------------------------------------');
      console.log(tweetText);
      console.log('----------------------------------------');
      
      // In a real implementation, this would be posted to Twitter
      console.log('Tweet would be posted to MISTER\'s Twitter feed');
    }
    
    // Simulate MISTER responding to user questions
    console.log('\nSimulating MISTER responding to user questions...');
    
    const userQuestions = [
      "Hey MISTER, what's your take on ADA right now?",
      "MISTER can you tell me which Cardano tokens have the best momentum today?",
      "Hey @MISTER I'm thinking of buying some HOSKY, what's it looking like?"
    ];
    
    for (const question of userQuestions) {
      console.log(`\nUser asked: "${question}"`);
      const response = await twitterIntegration.handleCardanoQuestion(question);
      
      console.log('MISTER\'s response:');
      console.log('----------------------------------------');
      console.log(response);
      console.log('----------------------------------------');
      
      // In a real implementation, this would be sent as a Twitter reply
      console.log('Response would be posted as a Twitter reply');
    }
    
    console.log('\nMISTER Twitter Flow simulation completed!');
    
  } catch (error) {
    console.error('Error simulating MISTER Twitter flow:', error);
  }
}

// Run the examples if this file is executed directly
if (require.main === module) {
  console.log('Starting TapTools with Twitter/Spaces example...');
  
  // Run both examples
  (async () => {
    await runCardanoTwitterExample();
    await simulateMisterTwitterFlow();
    console.log('\nAll examples completed. MISTER is now ready to use TapTools data in Twitter!');
  })().catch(console.error);
} else {
  // Export for use in other files
  module.exports = {
    runCardanoTwitterExample,
    simulateMisterTwitterFlow
  };
}

/**
 * To run this example:
 * 1. The TapTools API key is already set
 * 2. Ensure Twitter credentials are configured in your Twitter plugin
 * 3. Run with: node useTapToolsWithTwitterSpaces.js
 * 
 * Expected output:
 * - The script will demonstrate Twitter Spaces functionality
 * - It will show how to generate tweets about Cardano
 * - It will show how MISTER responds to Cardano questions
 * - Information will be saved to the knowledge directory
 */ 