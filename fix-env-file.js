// <PERSON>ript to fix the environment variables
const fs = require('fs');
const path = require('path');

// Path to the .env file
const envFilePath = path.join(__dirname, '.env');

// Read the .env file
let envFile = '';
if (fs.existsSync(envFilePath)) {
  envFile = fs.readFileSync(envFilePath, 'utf8');
}

// Check if the .env file has the required environment variables
console.log('Checking .env file...');
const hasDeepgramApiKey = envFile.includes('DEEPGRAM_API_KEY=');
const hasElevenLabsApiKey = envFile.includes('ELEVENLABS_API_KEY=');
const hasDiscordApiToken = envFile.includes('DISCORD_API_TOKEN=');
const hasDiscordVoiceChannelId = envFile.includes('DISCORD_VOICE_CHANNEL_ID=');

console.log('Has DEEPGRAM_API_KEY:', hasDeepgramApiKey);
console.log('Has ELEVENLABS_API_KEY:', hasElevenLabsApiKey);
console.log('Has DISCORD_API_TOKEN:', hasDiscordApiToken);
console.log('Has DISCORD_VOICE_CHANNEL_ID:', hasDiscordVoiceChannelId);

// Add the missing environment variables
let envFileUpdated = false;

if (!hasElevenLabsApiKey) {
  console.log('Adding ELEVENLABS_API_KEY...');
  envFile += '\nELEVENLABS_API_KEY=your_elevenlabs_api_key\n';
  envFileUpdated = true;
}

// Write the updated .env file
if (envFileUpdated) {
  fs.writeFileSync(envFilePath, envFile);
  console.log('.env file updated successfully!');
} else {
  console.log('No changes needed for .env file.');
}

// Create a README file with instructions
const readmeFilePath = path.join(__dirname, 'DISCORD_VOICE_README.md');
const readmeContent = `# Discord Voice Integration

This README provides instructions for setting up the Discord voice integration for your ElizaOS agent.

## Prerequisites

1. A Discord bot token (get one from the [Discord Developer Portal](https://discord.com/developers/applications))
2. A Discord server where you have admin permissions
3. A voice channel ID from your Discord server
4. A Deepgram API key (get one from [Deepgram](https://deepgram.com/))
5. An ElevenLabs API key (get one from [ElevenLabs](https://elevenlabs.io/))

## Setup Instructions

1. Make sure your \`.env\` file has the following environment variables:

\`\`\`
DEEPGRAM_API_KEY=your_deepgram_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
DISCORD_API_TOKEN=your_discord_bot_token
DISCORD_VOICE_CHANNEL_ID=your_discord_voice_channel_id
\`\`\`

2. Make sure your character file has the following plugins:

\`\`\`json
"plugins": [
  "@elizaos/plugin-node",
  "@elizaos-plugins/client-discord",
  "@elizaos-plugins/plugin-giphy"
]
\`\`\`

3. Make sure your character file has the following voice settings:

\`\`\`json
"settings": {
  "voice": {
    "transcription": "deepgram",
    "elevenlabs": {
      "voiceId": "your_elevenlabs_voice_id",
      "model": "eleven_multilingual_v2"
    }
  }
}
\`\`\`

4. Restart your ElizaOS agent:

\`\`\`
pnpm restart
\`\`\`

## Troubleshooting

If you encounter any issues, check the following:

1. Make sure your Discord bot is added to your server with the correct permissions
2. Make sure your Discord bot has access to the voice channel
3. Make sure your API keys are correct
4. Check the logs for any error messages

For more information, see the [ElizaOS documentation](https://docs.elizaos.com).
`;

fs.writeFileSync(readmeFilePath, readmeContent);
console.log('README file created successfully!');

console.log('\nPlease follow the instructions in the DISCORD_VOICE_README.md file to complete the setup.');
