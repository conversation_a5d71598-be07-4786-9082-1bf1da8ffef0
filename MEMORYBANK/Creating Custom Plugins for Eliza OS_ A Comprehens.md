<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# 

---

# Creating Custom Plugins for Eliza OS: A Comprehensive Guide

The Eliza OS framework provides a powerful platform for building AI agents with extended functionality through plugins. This guide offers a detailed breakdown of how to create custom plugins for your Eliza OS agents, ensuring you follow all necessary steps for successful implementation.

## Understanding Eliza OS Plugin Architecture

Eliza OS uses a unified plugin architecture where modular extensions enhance agent capabilities across different platforms. Plugins serve as the fundamental building blocks for adding new functionality to your AI agents, integrating external services, and customizing agent behavior[^4].

### Core Plugin Components

A plugin in Eliza OS can include several component types:

- **Actions**: Define specific tasks and behaviors your agent can perform
- **Providers**: Supply data or functionality to your agent
- **Evaluators**: Analyze responses and support decision-making
- **Services**: Run background processes and integrate with external systems
- **Clients**: Handle platform connections (Discord, Twitter, etc.)
- **Adapters**: Manage storage solutions and caching systems[^4]

At minimum, a plugin implements the core Plugin interface which requires a name, description, and at least one component type:

```typescript
type Plugin = {
  name: string;
  description: string;
  config?: { [key: string]: any };
  actions?: Action[];
  providers?: Provider[];
  evaluators?: Evaluator[];
  services?: Service[];
  clients?: Client[];
  adapters?: Adapter[];
};
```


## Setting Up Your Plugin Project

### Project Structure

Follow this recommended structure for your Eliza OS plugin:

```
plugin-name/
├── assets/            # Plugin images and branding
│   ├── logo.png
│   ├── banner.png
│   └── screenshots/
├── src/               # Source code
│   ├── index.ts       # Main plugin entry point
│   ├── actions/       # Plugin-specific actions
│   ├── providers/     # Data providers
│   ├── evaluators/    # Analysis components
│   ├── services/      # Background services (if needed)
│   ├── types.ts       # Type definitions
│   └── environment.ts # Configuration and settings
├── package.json       # Plugin dependencies
├── tsconfig.json      # TypeScript configuration
└── README.md          # Plugin documentation
```


### Package.json Configuration

Your `package.json` should include basic information and dependencies:

```json
{
  "name": "eliza-plugin-starter",
  "version": "0.1.0",
  "description": "Starter template for creating Eliza plugins",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "type": "module",
  "scripts": {
    "build": "tsc && cp -R src/plugins dist/plugins",
    "dev": "tsc -w",
    "test": "jest",
    "lint": "eslint src --ext .ts",
    "format": "prettier --write src",
    "mock-eliza": "node --loader ts-node/esm ./src/scripts/load-with-plugin.ts"
  },
  "keywords": [
    "eliza",
    "ai",
    "plugin"
  ],
  "license": "MIT",
  "dependencies": {
    "@ai16z/client-direct": "0.1.6-alpha.4",
    "@ai16z/eliza": "0.1.6-alpha.4"
  }
}
```

Additionally, for official plugins, include an `agentConfig` section:

```json
{
  "name": "@elizaos/plugin-example",
  "version": "1.0.0",
  "agentConfig": {
    "pluginType": "elizaos:plugin:1.0.0",
    "pluginParameters": {
      "API_KEY": {
        "type": "string",
        "description": "API key for the service"
      }
    }
  }
}
```


## Implementing Core Plugin Components

### Main Plugin Entry Point (index.ts)

The main file exports your plugin with all its components:

```typescript
import { Plugin } from "@elizaos/core";
import { exampleEvaluator } from "./evaluator";
import { exampleProvider } from "./provider";
import { exampleAction } from "./action";

export const examplePlugin: Plugin = {
  name: "EXAMPLE_PLUGIN",
  description: "A plugin that demonstrates basic functionality",
  actions: [exampleAction],
  evaluators: [exampleEvaluator],
  providers: [exampleProvider],
};
```


### Creating Actions

Actions handle specific tasks or behaviors. Here's an example structure:

```typescript
import { Action, IAgentRuntime, Memory } from "@elizaos/core";
import { getCachedData } from "./helper";

export const exampleAction: Action = {
  name: "performTask",
  description: "Performs a specific task based on user input",
  execute: async (_runtime: IAgentRuntime, _message: Memory): Promise<string> => {
    try {
      // Implement your action logic here
      const cachedData = await getCachedData<YourDataType>(_runtime, _message);
      
      // Process data and return a response
      return "Task completed successfully";
    } catch (error) {
      return "Error performing task";
    }
  },
};
```


### Creating Providers

Providers supply data or functionality:

```typescript
import { Provider, IAgentRuntime, Memory } from "@elizaos/core";
import { getCachedData, getCacheKey } from "./helper";

export const exampleProvider: Provider = {
  name: "dataProvider",
  description: "Provides data for the agent",
  get: async (_runtime: IAgentRuntime, _message: Memory): Promise<string> => {
    try {
      // Fetch or generate data
      const cachedData = await getCachedData<YourDataType>(_runtime, _message);
      
      // Format and return the data
      return JSON.stringify(cachedData);
    } catch (error) {
      return "Error retrieving data";
    }
  },
};
```


### Creating Evaluators

Evaluators analyze responses and assist with decision-making:

```typescript
import { Evaluator, IAgentRuntime, Memory } from "@elizaos/core";

export const exampleEvaluator: Evaluator = {
  name: "responseEvaluator",
  description: "Evaluates responses for specific patterns",
  evaluate: async (_runtime: IAgentRuntime, _message: Memory): Promise<boolean> => {
    // Implement your evaluation logic
    const messageContent = _message.content.toLowerCase();
    
    // Return true if the message meets certain criteria
    return messageContent.includes("specific keyword");
  },
};
```


### Helper Functions

Common helper functions can be used across your plugin:

```typescript
import { IAgentRuntime, Memory } from "@elizaos/core";

export const getCacheKey = (
  _runtime: IAgentRuntime,
  _message: Memory
): string => `${_runtime.character.name}/${_message.userId}/data`;

export const getCachedData = async <T>(
  _runtime: IAgentRuntime,
  _message: Memory,
  defaultValue: Partial<T> = {}
): Promise<T> => {
  const cacheKey = getCacheKey(_runtime, _message);
  const cachedData = await _runtime.cacheManager.get<T>(cacheKey);
  return cachedData ?? ({ ...defaultValue } as T);
};
```


## Configuration and Environment Variables

### Accessing Settings and Secrets

Plugins can access configuration and secrets through the runtime:

```typescript
class MyPlugin implements Plugin {
  async initialize(runtime: AgentRuntime) {
    // Access configuration values
    const apiKey = runtime.getSetting("PLUGIN_API_KEY");
    const secret = runtime.getSetting("PLUGIN_SECRET");
    
    // Use the values in your plugin
  }
}
```


### Setting Up in Character Configuration

Configure plugin settings in your agent's character file:

```json
{
  "name": "MyAgent",
  "plugins": [
    "@elizaos/your-plugin"
  ],
  "settings": {
    "your-plugin": {
      "API_KEY": "your-api-key",
      "SETTING_NAME": "setting-value"
    },
    "secrets": {
      "YOUR_PLUGIN_SECRET": "sensitive-value"
    }
  }
}
```


## Testing Your Plugin

### Using the Mock Client

Eliza provides a mock client for testing your plugin:

```bash
pnpm mock-eliza --characters=./characters/test.character.json
```

This allows you to interact with your agent and test your plugin's functionality in isolation before deploying it to production platforms[^4].

### Debugging Techniques

When debugging your plugin:

1. Enable debug logging
2. Check runtime logs for initialization and execution errors
3. Test plugin components individually
4. Verify that all dependencies are properly installed and imported[^4]

## Integrating with Eliza OS

### Installing Your Plugin

There are several ways to install plugins in Eliza OS:

#### 1. Add to project dependencies

In your agent's `package.json`:

```json
{
  "dependencies": {
    "@elizaos/plugin-solana": "github:elizaos-plugins/plugin-solana",
    "your-custom-plugin": "file:../path/to/your/plugin"
  }
}
```


#### 2. Configure in character file

In your agent's character configuration:

```json
{
  "name": "MyAgent",
  "plugins": [
    "@elizaos/plugin-twitter",
    "your-custom-plugin"
  ]
}
```


#### 3. Use the CLI tool

Eliza provides a CLI tool for managing plugins:

```bash
npx elizaos plugins add your-plugin
```


### For Local/Custom Plugins

For plugins not in the official registry:

1. Upload the custom plugin to the packages folder:
```
packages/
├─plugin-example/
├── package.json
├── tsconfig.json
├── src/
│ ├── index.ts
│ ├── actions/
│ ├── providers/
│ ├── types.ts
│ └── environment.ts
```

2. Add the custom plugin to your agent's dependencies:
```json
{
  "dependencies": {
    "@elizaos/plugin-example": "workspace:*"
  }
}
```

3. Import the custom plugin in your agent's character.json:
```json
"plugins": [
  "@elizaos/plugin-example"
]
```


## Best Practices and Examples

### Real-World Example: Building a NASA API Plugin

The NASA API plugin integration provides a practical example of implementing a custom plugin:

1. Set up project structure
2. Implement TypeScript code for API integration
3. Test the plugin across web and Twitter interfaces
4. Design the plugin to fetch space photos from NASA's API[^2]

### Plugin Branding Guidelines

For a professional appearance, include these assets:

- `logo.png` (400x400px) - Square logo for your plugin
- `banner.png` (1280x640px) - Banner image
- Screenshots demonstrating functionality

Keep images high-resolution but optimized (<500KB for logos, <1MB for banners)[^4]

## Conclusion

Creating a custom plugin for Eliza OS involves understanding the plugin architecture, setting up the proper project structure, implementing the core components (Actions, Providers, Evaluators), configuring environment variables, and testing thoroughly. By following this guide, you should be able to develop and integrate your own plugins that extend the capabilities of your Eliza OS agents.

Remember that plugins provide a modular way to add new functionality, integrate external services, and customize agent behavior across different platforms. Whether you're building a simple utility or a complex integration, the plugin system offers a flexible framework for extending your AI agents' capabilities.

<div style="text-align: center">⁂</div>

[^1]: https://github.com/elizaOS/eliza-plugin-starter/blob/master/package.json

[^2]: https://www.youtube.com/watch?v=25FxjscBHuo

[^3]: https://blog.toktokhan.dev/creating-autonomous-ai-agents-with-elizaos-toktokhandev-eng3-e1f34e04c410

[^4]: https://elizaos.github.io/eliza/docs/core/plugins/

