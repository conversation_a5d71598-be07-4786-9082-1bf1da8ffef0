# MISTER Agent Integration with <PERSON>

## Overview
This document provides the technical specifications for integrating MISTER Agent as a provider in an Eliza project. MISTER is a specialized Cardano crypto analysis agent with a unique personality and comprehensive market analysis capabilities.

## Provider Configuration

### Provider Name
- **Recommended Name**: `MISTER` or `MISTERAGENT`

### Base URL and Endpoints
- **Base URL**: `https://misterexc6.ngrok.io`
- **Primary Endpoint**: `https://misterexc6.ngrok.io/api/agents/MISTERAgent/generate`
- **Alternative Network Endpoint**: `https://misterexc6.ngrok.io/api/networks/EnhancedAgentNetwork/generate`

### OpenAI API Compatibility
**❌ NOT OpenAI API Compatible**
- Uses Mastra's custom request/response format
- Requires custom provider implementation in Eliza

## API Specification

### Request Format
```json
{
  "message": "your message here",
  "metadata": {
    "source": "eliza",
    "userId": "optional-user-id",
    "conversationId": "optional-conversation-id"
  }
}
```

### Response Format
```json
{
  "response": "MISTER's response text here",
  "success": true,
  "metadata": {
    "timestamp": "2025-01-27T12:34:56.789Z",
    "source": "MISTERAgent",
    "model": "gpt-4.1-mini",
    "tokensUsed": 150
  }
}
```

## Key Differences from OpenAI API

| Feature | OpenAI API | MISTER API |
|---------|------------|------------|
| **Request Format** | `messages` array with role/content | Single `message` string |
| **Response Path** | `choices[0].message.content` | `response` |
| **Message History** | Client manages in `messages` array | Agent manages internally |
| **Streaming** | Supports SSE streaming | No streaming support |
| **Model Selection** | `model` parameter | Fixed model (gpt-4.1-mini) |
| **System Messages** | `role: "system"` in messages | Built into agent personality |
| **Function Calling** | OpenAI function format | Internal tool system |
| **Error Format** | OpenAI error structure | Custom error format |

## Example Implementation

### Basic Provider Implementation
```javascript
class MISTERProvider {
  constructor() {
    this.baseURL = 'https://misterexc6.ngrok.io';
    this.endpoint = '/api/agents/MISTERAgent/generate';
  }

  async generateResponse(message, options = {}) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          message: message,
          metadata: {
            source: "eliza",
            userId: options.userId || null,
            conversationId: options.conversationId || null
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'MISTER API returned error');
      }

      return {
        content: data.response,
        metadata: data.metadata
      };
    } catch (error) {
      console.error('MISTER Provider Error:', error);
      throw error;
    }
  }
}
```

### Usage Example
```javascript
const mister = new MISTERProvider();

// Simple message
const response = await mister.generateResponse("What's the price of ADA?");
console.log(response.content); // MISTER's response

// With user context
const response2 = await mister.generateResponse(
  "Analyze my portfolio risk", 
  { userId: "user123", conversationId: "conv456" }
);
```

## Error Handling

### Common Error Responses
```json
{
  "success": false,
  "error": "Error message here",
  "code": "ERROR_CODE",
  "metadata": {
    "timestamp": "2025-01-27T12:34:56.789Z"
  }
}
```

### Error Handling Implementation
```javascript
try {
  const response = await mister.generateResponse(message);
  return response.content;
} catch (error) {
  if (error.message.includes('HTTP 503')) {
    return "MISTER is temporarily unavailable. Please try again later.";
  } else if (error.message.includes('HTTP 429')) {
    return "Rate limit exceeded. Please wait a moment.";
  } else {
    return "Sorry, I'm having trouble connecting to MISTER right now.";
  }
}
```

## MISTER Agent Capabilities

### Specialized Features
- **Cardano Ecosystem Expert**: Deep knowledge of ADA and Cardano tokens
- **Real-time Market Data**: Live price feeds and volume analysis
- **Risk Assessment**: Token safety analysis and cabal detection
- **Technical Analysis**: Chart patterns and trading indicators
- **Social Media Integration**: Twitter feeds and community sentiment
- **Dual Personality**: Professional for data, laid-back for casual chat

### Sample Interactions
```
User: "What's the ticker?"
MISTER: [Returns high-speculative play from top 20-25 Cardano volume tokens]

User: "Analyze SNEK token risk"
MISTER: [Comprehensive risk analysis with holder distribution, cabal detection]

User: "Show me a meme"
MISTER: [Laid-back, unrestricted response with humor/memes]
```

## Integration Checklist

- [ ] Implement custom provider class (not OpenAI compatible)
- [ ] Handle single message format (not messages array)
- [ ] Parse response from `response` field
- [ ] Implement error handling for custom error format
- [ ] Test with both data queries and casual chat
- [ ] Configure appropriate timeouts (responses can take 5-15 seconds)
- [ ] Handle metadata for user tracking if needed

## Testing Endpoints

### Health Check
```bash
curl https://misterexc6.ngrok.io/health
```

### Test Message
```bash
curl -X POST https://misterexc6.ngrok.io/api/agents/MISTERAgent/generate \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello MISTER", "metadata": {"source": "test"}}'
```

## Notes for Eliza Integration

1. **No Message History Management**: MISTER manages conversation context internally
2. **Response Time**: Expect 5-15 second response times for complex queries
3. **Rate Limiting**: No specific limits, but be reasonable with request frequency
4. **Content Filtering**: MISTER has dual personality - professional for data, unrestricted for casual chat
5. **Specialization**: Best for crypto/Cardano topics, but handles general conversation too

## Support

For integration issues or questions:
- **Creator**: @CASHCOLDGAME
- **Agent Endpoint**: https://misterexc6.ngrok.io
- **Documentation**: This document

---

**Ready to integrate MISTER's crypto expertise into your Eliza project! 🚀**
