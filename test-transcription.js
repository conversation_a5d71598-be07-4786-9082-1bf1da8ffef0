// Script to manually initialize the transcription service
const { createNodePlugin } = require('./packages/plugin-node/dist/index.js');
const { ServiceType } = require('./packages/core/dist/index.js');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create a mock runtime for initialization
const mockRuntime = {
  getSetting: (key) => process.env[key],
  character: {
    settings: {
      voice: {
        transcriptionProvider: 'deepgram',
        elevenlabs: {
          voiceId: process.env.ELEVENLABS_VOICE_ID,
          model: 'eleven_multilingual_v2'
        }
      }
    }
  },
  services: new Map(),
  registerService: function(service) {
    console.log(`Registering service: ${service.constructor.name}`);
    this.services.set(service.serviceType, service);
  }
};

async function main() {
  try {
    // Create the node plugin
    const nodePlugin = createNodePlugin();
    console.log('Created node plugin:', nodePlugin.name);
    
    // Register services
    for (const service of nodePlugin.services) {
      mockRuntime.registerService(service);
    }
    
    // Initialize the transcription service
    const transcriptionService = mockRuntime.services.get(ServiceType.TRANSCRIPTION);
    
    if (!transcriptionService) {
      console.error('Transcription service not found after registration!');
      return;
    }
    
    console.log('Transcription service found:', transcriptionService.constructor.name);
    
    // Initialize the service
    await transcriptionService.initialize(mockRuntime);
    
    console.log('Transcription service initialized successfully');
    console.log('Transcription provider:', transcriptionService.transcriptionProvider);
    
    // Check environment variables
    console.log('\nEnvironment variables:');
    console.log('TRANSCRIPTION_PROVIDER:', process.env.TRANSCRIPTION_PROVIDER);
    console.log('DEEPGRAM_API_KEY:', process.env.DEEPGRAM_API_KEY ? 'Set' : 'Not set');
    console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');
    
    // List all registered services
    console.log('\nAll registered services:');
    for (const [type, service] of mockRuntime.services.entries()) {
      console.log(`- ${type}: ${service.constructor.name}`);
    }
    
  } catch (error) {
    console.error('Error testing transcription service:', error);
  }
}

main();
