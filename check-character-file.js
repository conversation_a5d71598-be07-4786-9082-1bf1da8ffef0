// <PERSON>ript to check the character file and make sure it has the correct plugin configuration
const fs = require('fs');
const path = require('path');

// Path to the character file
const characterFilePath = path.join(__dirname, 'characters', 'MxSTER.character.json');

// Read the character file
const characterFile = JSON.parse(fs.readFileSync(characterFilePath, 'utf8'));

// Check if the character file has the correct plugin configuration
console.log('Checking character file...');
console.log('Character name:', characterFile.name);

// Check if the character file has the plugin-node plugin
const hasPluginNode = characterFile.plugins.some(plugin => plugin === '@elizaos/plugin-node');
console.log('Has @elizaos/plugin-node plugin:', hasPluginNode);

// Check if the character file has the plugin-giphy plugin
const hasPluginGiphy = characterFile.plugins.some(plugin => plugin === '@elizaos-plugins/plugin-giphy');
console.log('Has @elizaos-plugins/plugin-giphy plugin:', hasPluginGiphy);

// Check if the character file has the client-discord plugin
const hasClientDiscord = characterFile.plugins.some(plugin => plugin === '@elizaos-plugins/client-discord');
console.log('Has @elizaos-plugins/client-discord plugin:', hasClientDiscord);

// Check if the character file has the voice settings
console.log('Voice settings:', characterFile.settings?.voice);

// Check if the character file has the DEEPGRAM_API_KEY setting
console.log('DEEPGRAM_API_KEY setting:', characterFile.settings?.DEEPGRAM_API_KEY ? 'Yes' : 'No');

// Check if the character file has the DISCORD_VOICE_CHANNEL_ID setting
console.log('DISCORD_VOICE_CHANNEL_ID setting:', characterFile.settings?.DISCORD_VOICE_CHANNEL_ID ? 'Yes' : 'No');

// Check if the character file has the DISCORD_API_TOKEN setting
console.log('DISCORD_API_TOKEN setting:', characterFile.settings?.DISCORD_API_TOKEN ? 'Yes' : 'No');

// Check if the character file has the ELEVENLABS_API_KEY setting
console.log('ELEVENLABS_API_KEY setting:', characterFile.settings?.ELEVENLABS_API_KEY ? 'Yes' : 'No');

// If the character file doesn't have the plugin-node plugin, add it
if (!hasPluginNode) {
  console.log('Adding @elizaos/plugin-node plugin...');
  characterFile.plugins.push('@elizaos/plugin-node');
}

// If the character file doesn't have the voice settings, add them
if (!characterFile.settings?.voice) {
  console.log('Adding voice settings...');
  characterFile.settings = characterFile.settings || {};
  characterFile.settings.voice = {
    transcription: 'deepgram',
    elevenlabs: {
      voiceId: 'c6SfcYrb2t09NHXiT80T',
      model: 'eleven_multilingual_v2'
    }
  };
}

// Write the updated character file
fs.writeFileSync(characterFilePath, JSON.stringify(characterFile, null, 2));
console.log('Character file updated successfully!');

// Check the .env file for the required environment variables
const envFilePath = path.join(__dirname, '.env');
if (fs.existsSync(envFilePath)) {
  const envFile = fs.readFileSync(envFilePath, 'utf8');
  console.log('\nChecking .env file...');
  console.log('Has DEEPGRAM_API_KEY:', envFile.includes('DEEPGRAM_API_KEY='));
  console.log('Has ELEVENLABS_API_KEY:', envFile.includes('ELEVENLABS_API_KEY='));
  console.log('Has DISCORD_API_TOKEN:', envFile.includes('DISCORD_API_TOKEN='));
  console.log('Has DISCORD_VOICE_CHANNEL_ID:', envFile.includes('DISCORD_VOICE_CHANNEL_ID='));
} else {
  console.log('\n.env file not found!');
}
