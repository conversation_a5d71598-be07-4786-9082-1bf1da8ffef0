// See https://aka.ms/vscode-remote/devcontainer.json for format details.
{
    "name": "elizaos-dev",
    "dockerFile": "Dockerfile",
    "build": {
        "args": {
            "NODE_VER": "23.5.0",
            "PNPM_VER": "9.15.2"
        }
    },
    "privileged": true,
    "runArgs": [
        "-p 3000:3000", // Add port for server api
        "-p 5173:5173", // Add port for client
        //"--volume=/usr/lib/wsl:/usr/lib/wsl", // uncomment for WSL
        //"--volume=/mnt/wslg:/mnt/wslg", // uncomment for WSL
        "--gpus=all", // ! uncomment for vGPU
        //"--device=/dev/dxg", // uncomment this for vGPU under WSL
        "--device=/dev/dri"
    ],
    "containerEnv": {
        //"MESA_D3D12_DEFAULT_ADAPTER_NAME": "NVIDIA", // uncomment for WSL
        //"LD_LIBRARY_PATH": "/usr/lib/wsl/lib" // uncomment for WSL
    },
    "customizations": {
        "vscode": {
            "extensions": [
                "vscode.json-language-features",
                "vscode.css-language-features",
                // "foxundermoon.shell-format",
                // "dbaeumer.vscode-eslint",
                // "esbenp.prettier-vscode"
                "ms-python.python"
            ]
        }
    },
    "features": {}
}
