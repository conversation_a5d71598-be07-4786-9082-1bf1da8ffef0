// <PERSON><PERSON><PERSON> to manually register the transcription and speech services
import { createNodePlugin } from '@elizaos/plugin-node';
import { ServiceType } from '@elizaos/core';

// Create a mock runtime for testing
const mockRuntime = {
  getSetting: (key) => {
    if (key === 'TRANSCRIPTION_PROVIDER') return 'local';
    if (key === 'DEEPGRAM_API_KEY') return process.env.DEEPGRAM_API_KEY;
    if (key === 'OPENAI_API_KEY') return process.env.OPENAI_API_KEY;
    if (key === 'ELEVENLABS_XI_API_KEY') return process.env.ELEVENLABS_XI_API_KEY;
    return null;
  },
  character: {
    settings: {
      voice: {
        transcription: 'local',
        elevenlabs: {
          voiceId: 'c6SfcYrb2t09NHXiT80T',
          model: 'eleven_multilingual_v2'
        }
      }
    }
  },
  services: new Map(),
  registerService: function(service) {
    console.log(`Registering service: ${service.constructor.name} with type: ${service.serviceType}`);
    this.services.set(service.serviceType, service);
  },
  getService: function(type) {
    return this.services.get(type);
  }
};

async function main() {
  try {
    // Create the node plugin
    const nodePlugin = createNodePlugin();
    console.log('Created node plugin:', nodePlugin.name);

    // Register services
    for (const service of nodePlugin.services) {
      mockRuntime.registerService(service);
    }

    // Check if services are registered
    console.log('\nChecking registered services:');
    console.log('Transcription service:', mockRuntime.getService(ServiceType.TRANSCRIPTION) ? 'Found' : 'Not found');
    console.log('Speech service:', mockRuntime.getService(ServiceType.SPEECH_GENERATION) ? 'Found' : 'Not found');
    console.log('Image description service:', mockRuntime.getService(ServiceType.IMAGE_DESCRIPTION) ? 'Found' : 'Not found');

    // Initialize the transcription service
    console.log('\nInitializing services:');
    const transcriptionService = mockRuntime.getService(ServiceType.TRANSCRIPTION);
    if (transcriptionService) {
      await transcriptionService.initialize(mockRuntime);
      console.log('Transcription service initialized');
    }

    const speechService = mockRuntime.getService(ServiceType.SPEECH_GENERATION);
    if (speechService) {
      await speechService.initialize(mockRuntime);
      console.log('Speech service initialized');
    }

    console.log('\nAvailable services after initialization:');
    console.log('Services:', Array.from(mockRuntime.services.keys()).join(', '));
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
