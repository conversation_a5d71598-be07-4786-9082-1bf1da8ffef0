// <PERSON>ript to test the transcription service
const { ServiceType } = require('./packages/core/dist/index.js');

// Create a mock runtime for testing
const mockRuntime = {
  getSetting: (key) => {
    if (key === 'TRANSCRIPTION_PROVIDER') return 'deepgram';
    if (key === 'DEEPGRAM_API_KEY') return '****************************************';
    return null;
  },
  character: {
    name: 'MxSTER',
    settings: {
      voice: {
        transcription: 'deepgram',
        elevenlabs: {
          voiceId: 'c6SfcYrb2t09NHXiT80T',
          model: 'eleven_multilingual_v2'
        }
      }
    }
  },
  services: new Map(),
  registerService: function(service) {
    console.log(`Registering service: ${service.constructor.name}`);
    this.services.set(service.serviceType, service);
  },
  getService: function(serviceType) {
    return this.services.get(serviceType);
  }
};

// Import the plugin-node module
async function main() {
  try {
    // Dynamically import the plugin-node module
    const { createNodePlugin } = await import('./node_modules/@elizaos/plugin-node/dist/index.js');
    
    // Create the node plugin
    const nodePlugin = createNodePlugin();
    console.log('Created node plugin:', nodePlugin.name);
    
    // Register services
    for (const service of nodePlugin.services) {
      mockRuntime.registerService(service);
    }
    
    // Get the transcription service
    const transcriptionService = mockRuntime.getService(ServiceType.TRANSCRIPTION);
    
    if (!transcriptionService) {
      console.error('Transcription service not found after registration!');
      return;
    }
    
    console.log('Transcription service found:', transcriptionService.constructor.name);
    
    // Initialize the service
    await transcriptionService.initialize(mockRuntime);
    
    console.log('Transcription service initialized successfully');
    
    // List all registered services
    console.log('\nAll registered services:');
    for (const [type, service] of mockRuntime.services.entries()) {
      console.log(`- ${type}: ${service.constructor.name}`);
    }
    
  } catch (error) {
    console.error('Error testing transcription service:', error);
  }
}

main();
