# Discord Voice Integration Setup Guide

This guide provides step-by-step instructions for setting up the Discord voice integration for your ElizaOS agent.

## Issue

The Discord voice integration was not working properly due to the following issues:

1. Missing ServiceType import in the Discord client
2. Conflicting plugin-node dependencies
3. Missing environment variables

## Solution

We've created several scripts to fix these issues:

1. `fix-transcription.js`: Removes the conflicting plugin-node dependency
2. `fix-discord-client.js`: Fixes the ServiceType import in the Discord client
3. `check-character-file.js`: Checks if the character file has the correct plugin configuration
4. `fix-env-file.js`: Adds the missing environment variables to the .env file
5. `test-discord-voice.js`: Tests if the Discord voice integration is set up correctly

## Setup Instructions

### 1. Fix the conflicting plugin-node dependency

Run the following command:

```bash
node fix-transcription.js
```

This will remove the conflicting `@elizaos-plugins/plugin-node` dependency and ensure that only the correct `@elizaos/plugin-node` dependency is used.

### 2. Fix the Discord client implementation

Run the following command:

```bash
node fix-discord-client.js
```

This will fix the ServiceType import in the Discord client.

### 3. Build the project

Run the following command:

```bash
pnpm build
```

This will build the project with the fixed Discord client implementation.

### 4. Check the character file

Run the following command:

```bash
node check-character-file.js
```

This will check if the character file has the correct plugin configuration and add any missing plugins or settings.

### 5. Fix the environment variables

Run the following command:

```bash
node fix-env-file.js
```

This will add any missing environment variables to the .env file.

### 6. Update your .env file

Open your `.env` file and update the following environment variables:

```
DEEPGRAM_API_KEY=your_deepgram_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
DISCORD_API_TOKEN=your_discord_bot_token
DISCORD_VOICE_CHANNEL_ID=your_discord_voice_channel_id
```

### 7. Test the Discord voice integration

Run the following command:

```bash
node test-discord-voice.js
```

This will test if the Discord voice integration is set up correctly.

### 8. Restart your ElizaOS agent

Run the following command:

```bash
pnpm restart
```

This will restart your ElizaOS agent with the fixed Discord voice integration.

## Troubleshooting

If you encounter any issues, check the following:

1. Make sure your Discord bot is added to your server with the correct permissions
2. Make sure your Discord bot has access to the voice channel
3. Make sure your API keys are correct
4. Check the logs for any error messages

### Common Error Messages

#### ServiceType is not defined

This error occurs when the ServiceType import is missing in the Discord client. Run the `fix-discord-client.js` script to fix this issue.

#### Service transcription not found

This error occurs when the transcription service is not properly registered. Make sure the `@elizaos/plugin-node` plugin is correctly configured in your character file and that the DEEPGRAM_API_KEY is set in your .env file.

#### Speech generation service not found

This error occurs when the speech generation service is not properly registered. Make sure the ELEVENLABS_API_KEY is set in your .env file.

## Additional Resources

- [Discord Developer Portal](https://discord.com/developers/applications)
- [Deepgram](https://deepgram.com/)
- [ElevenLabs](https://elevenlabs.io/)
- [ElizaOS Documentation](https://docs.elizaos.com)
