{"name": "@elizaos/agent", "version": "0.25.9", "main": "src/index.ts", "type": "module", "scripts": {"start": "node --loader ts-node/esm src/index.ts", "dev": "node --loader ts-node/esm src/index.ts", "check-types": "tsc --noEmit", "test": "jest"}, "nodemonConfig": {"watch": ["src", "../core/dist"], "ext": "ts,json", "exec": "node --enable-source-maps --loader ts-node/esm src/index.ts"}, "dependencies": {"@elizaos-plugins/client-discord": "workspace:0.25.6-alpha.1", "@elizaos-plugins/client-telegram": "workspace:0.25.6-alpha.1", "@elizaos-plugins/client-twitter": "workspace:0.25.6-alpha.1", "@elizaos-plugins/plugin-coinmarketcap": "workspace:0.25.6-alpha.1", "@elizaos-plugins/plugin-giphy": "workspace:^", "@elizaos-plugins/plugin-image-generation": "workspace:^", "@elizaos-plugins/plugin-open-weather": "workspace:^", "@elizaos-plugins/plugin-tts": "workspace:^", "@elizaos-plugins/plugin-video-generation": "workspace:^", "@elizaos-plugins/plugin-web-search": "workspace:^", "@elizaos/client-direct": "workspace:*", "@elizaos/core": "workspace:*", "@elizaos/plugin-bootstrap": "workspace:*", "@types/node": "^22.13.5", "json5": "2.2.3", "ts-node": "^10.9.2", "yargs": "17.7.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "jest": "^29.7.0", "ts-jest": "^29.2.6"}}