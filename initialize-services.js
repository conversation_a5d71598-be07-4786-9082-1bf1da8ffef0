// Script to manually initialize the transcription service
const { createNodePlugin } = require('./packages/plugin-node/dist/index.js');
const { ServiceType } = require('./packages/core/dist/index.js');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create a mock runtime for initialization
const mockRuntime = {
  getSetting: (key) => process.env[key],
  character: {
    settings: {
      transcription: 'deepgram',
      voice: {
        transcriptionProvider: 'deepgram',
        elevenlabs: {
          voiceId: process.env.ELEVENLABS_VOICE_ID,
          model: 'eleven_multilingual_v2'
        }
      }
    }
  }
};

async function initializeServices() {
  try {
    // Create the node plugin
    const nodePlugin = createNodePlugin();
    
    console.log('Node Plugin Services:');
    nodePlugin.services.forEach(service => {
      console.log(`- ${service.type}: ${service.constructor.name}`);
    });
    
    // Find the transcription service
    const transcriptionService = nodePlugin.services.find(service => service.type === ServiceType.TRANSCRIPTION);
    
    if (!transcriptionService) {
      console.error('Transcription service not found in the node plugin');
      return;
    }
    
    console.log('\nInitializing Transcription Service...');
    
    // Initialize the transcription service
    await transcriptionService.initialize(mockRuntime);
    
    console.log('Transcription Service initialized successfully');
    console.log('Provider:', transcriptionService.transcriptionProvider);
    
    // Find the speech service
    const speechService = nodePlugin.services.find(service => service.type === ServiceType.SPEECH_GENERATION);
    
    if (!speechService) {
      console.error('Speech service not found in the node plugin');
      return;
    }
    
    console.log('\nInitializing Speech Service...');
    
    // Initialize the speech service
    await speechService.initialize(mockRuntime);
    
    console.log('Speech Service initialized successfully');
    console.log('Provider:', speechService.provider);
    
    console.log('\nAll services initialized successfully');
  } catch (error) {
    console.error('Error initializing services:', error);
  }
}

initializeServices().catch(console.error);
