const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create dist directory if it doesn't exist
if (!fs.existsSync('dist')) {
  fs.mkdirSync('dist');
}

// Compile TypeScript files
console.log('Compiling TypeScript files...');
try {
  execSync('tsc --declaration --outDir dist', { stdio: 'inherit' });
  console.log('TypeScript compilation successful!');
} catch (error) {
  console.error('TypeScript compilation failed:', error);
  process.exit(1);
}

// Copy package.json to dist
console.log('Copying package.json to dist...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
packageJson.main = 'index.js';
packageJson.types = 'index.d.ts';
fs.writeFileSync(path.join('dist', 'package.json'), JSON.stringify(packageJson, null, 2));

console.log('Build completed successfully!');
