// <PERSON>ript to fix the compiled Discord client
const fs = require('fs');
const path = require('path');

// Path to the compiled Discord client file
const compiledDiscordClientPath = path.join(__dirname, 'packages', 'client-discord', 'dist', 'index.js');

// Read the compiled Discord client file
let compiledDiscordClientContent = fs.readFileSync(compiledDiscordClientPath, 'utf8');

// Find the line where the DiscordClient constructor is defined
const discordClientConstructorRegex = /class\s+DiscordClient\s+extends\s+EventEmitter\s*{[\s\S]*?constructor\s*\(\s*runtime\s*\)\s*{/;
const discordClientConstructorMatch = compiledDiscordClientContent.match(discordClientConstructorRegex);

if (!discordClientConstructorMatch) {
  console.error('Could not find DiscordClient constructor in compiled file!');
  process.exit(1);
}

// Find the line where the transcription service is checked
const transcriptionServiceCheckRegex = /const\s+transcriptionService\s*=\s*this\.runtime\.getService\s*\(\s*ServiceType\s*\.\s*TRANSCRIPTION\s*\)/;
const transcriptionServiceCheckMatch = compiledDiscordClientContent.match(transcriptionServiceCheckRegex);

if (transcriptionServiceCheckMatch) {
  console.log('Transcription service check already exists with ServiceType!');
} else {
  console.log('Transcription service check needs to be fixed...');
  
  // Find the line where the transcription service is checked with undefined ServiceType
  const brokenTranscriptionServiceCheckRegex = /const\s+transcriptionService\s*=\s*this\.runtime\.getService\s*\(\s*ServiceType\s*\.\s*TRANSCRIPTION\s*\)/;
  const brokenTranscriptionServiceCheckMatch = compiledDiscordClientContent.match(brokenTranscriptionServiceCheckRegex);
  
  if (brokenTranscriptionServiceCheckMatch) {
    console.log('Found broken transcription service check!');
  } else {
    console.log('Could not find broken transcription service check!');
  }
  
  // Add ServiceType import at the top of the file
  const importRegex = /import\s*{([^}]*)}\s*from\s*["']@elizaos\/core["']/;
  const importMatch = compiledDiscordClientContent.match(importRegex);
  
  if (importMatch) {
    console.log('Found import statement!');
    const importStatement = importMatch[0];
    const importContent = importMatch[1];
    
    if (!importContent.includes('ServiceType')) {
      const newImportStatement = importStatement.replace('{' + importContent + '}', '{' + importContent + ', ServiceType}');
      compiledDiscordClientContent = compiledDiscordClientContent.replace(importStatement, newImportStatement);
      console.log('Added ServiceType to import statement!');
    } else {
      console.log('ServiceType already in import statement!');
    }
  } else {
    console.log('Could not find import statement!');
  }
  
  // Replace all occurrences of ServiceType.TRANSCRIPTION with a string
  compiledDiscordClientContent = compiledDiscordClientContent.replace(/ServiceType\.TRANSCRIPTION/g, '"transcription"');
  console.log('Replaced ServiceType.TRANSCRIPTION with "transcription"!');
  
  // Replace all occurrences of ServiceType.SPEECH_GENERATION with a string
  compiledDiscordClientContent = compiledDiscordClientContent.replace(/ServiceType\.SPEECH_GENERATION/g, '"speech_generation"');
  console.log('Replaced ServiceType.SPEECH_GENERATION with "speech_generation"!');
}

// Write the updated compiled Discord client file
fs.writeFileSync(compiledDiscordClientPath, compiledDiscordClientContent);
console.log('Compiled Discord client file updated successfully!');

// Now let's also fix the voice.ts file
const voiceFilePath = path.join(__dirname, 'packages', 'client-discord', 'src', 'voice.ts');
let voiceFileContent = fs.readFileSync(voiceFilePath, 'utf8');

// Replace ServiceType.TRANSCRIPTION with "transcription"
voiceFileContent = voiceFileContent.replace(/ServiceType\.TRANSCRIPTION/g, '"transcription"');
console.log('Replaced ServiceType.TRANSCRIPTION with "transcription" in voice.ts!');

// Replace ServiceType.SPEECH_GENERATION with "speech_generation"
voiceFileContent = voiceFileContent.replace(/ServiceType\.SPEECH_GENERATION/g, '"speech_generation"');
console.log('Replaced ServiceType.SPEECH_GENERATION with "speech_generation" in voice.ts!');

// Write the updated voice.ts file
fs.writeFileSync(voiceFilePath, voiceFileContent);
console.log('Voice.ts file updated successfully!');

// Now let's also fix the client.ts file
const clientFilePath = path.join(__dirname, 'packages', 'client-discord', 'src', 'client.ts');
let clientFileContent = fs.readFileSync(clientFilePath, 'utf8');

// Replace ServiceType.TRANSCRIPTION with "transcription"
clientFileContent = clientFileContent.replace(/ServiceType\.TRANSCRIPTION/g, '"transcription"');
console.log('Replaced ServiceType.TRANSCRIPTION with "transcription" in client.ts!');

// Replace ServiceType.SPEECH_GENERATION with "speech_generation"
clientFileContent = clientFileContent.replace(/ServiceType\.SPEECH_GENERATION/g, '"speech_generation"');
console.log('Replaced ServiceType.SPEECH_GENERATION with "speech_generation" in client.ts!');

// Write the updated client.ts file
fs.writeFileSync(clientFilePath, clientFileContent);
console.log('Client.ts file updated successfully!');

console.log('\nAll files updated successfully! Please rebuild the project with:');
console.log('pnpm build');
console.log('\nThen restart your ElizaOS agent with:');
console.log('pnpm restart');
