import {
    <PERSON><PERSON>ator,
    IAgentRuntime,
    Memory,
    elizaLogger,
    stringToUuid,
} from "@elizaos/core";
import { MisterService } from "../services/misterService.js";

// Cache to prevent duplicate evaluations for the same message
const evaluationCache = new Map<string, boolean>();

// Keywords that trigger MISTER delegation
const CRYPTO_KEYWORDS = [
    // Price queries
    "price", "cost", "value", "worth", "trading", "market cap", "volume", "mcap",
    "marketcap", "market value", "valuation", "pricing", "quote", "rate",

    // Cardano ecosystem tokens
    "ada", "cardano", "snek", "hosky", "min", "sundae", "meld", "copi", "indy",
    "iag", "ntx", "stuff", "bbsnek", "sugr", "nikepig", "fldt", "lq", "wrt",
    "agix", "wmt", "wmtx", "lenfi", "fren", "bank", "chad", "clay", "book",
    "newm", "pavia", "cornucopias", "djed", "shen", "optim", "vyfi", "splash",

    // Major cryptocurrencies
    "bitcoin", "btc", "ethereum", "eth", "bnb", "solana", "sol", "xrp", "ripple",
    "doge", "dogecoin", "avax", "avalanche", "matic", "polygon", "dot", "polkadot",
    "link", "chainlink", "uni", "uniswap", "ltc", "litecoin", "atom", "cosmos",
    "near", "algo", "algorand", "xlm", "stellar", "vet", "vechain", "icp",

    // Crypto general terms
    "crypto", "cryptocurrency", "token", "coin", "blockchain", "defi", "nft",
    "staking", "yield", "liquidity", "farming", "mining", "wallet", "exchange",
    "dex", "cex", "swap", "bridge", "protocol", "smart contract", "dao",

    // Analysis terms
    "analyze", "analysis", "risk", "cabal", "holder", "distribution", "chart",
    "technical analysis", "ta", "bullish", "bearish", "moon", "dump", "pump",
    "fundamentals", "tokenomics", "utility", "use case", "roadmap", "team",
    "partnerships", "adoption", "metrics", "on-chain", "whale", "retail",

    // MISTER specific
    "ticker", "alpha", "gem", "play", "speculative", "degen", "ape", "diamond hands",
    "hodl", "fud", "fomo", "rekt", "moon", "lambo", "bag", "shill", "research",

    // Market terms
    "bull", "bear", "trend", "support", "resistance", "breakout", "dip",
    "rally", "correction", "crash", "ath", "all time high", "all time low",
    "consolidation", "accumulation", "distribution", "volume profile", "rsi",
    "macd", "fibonacci", "moving average", "candlestick", "pattern",

    // News and updates
    "news", "update", "announcement", "partnership", "listing", "launch",
    "mainnet", "testnet", "upgrade", "fork", "governance", "proposal", "vote",

    // Summary and TLDR
    "tldr", "tl;dr", "summarize", "summary", "recap", "overview", "brief"
];

const MISTER_PHRASES = [
    // Ticker requests
    "what's the ticker",
    "give me a ticker",
    "what's the play",
    "any alpha",
    "got any plays",
    "what should i buy",
    "investment advice",
    "trading opportunity",

    // Analysis requests
    "deep analysis",
    "analyze this",
    "risk analysis",
    "token analysis",
    "project analysis",
    "cabal check",
    "holder analysis",
    "whale analysis",
    "distribution analysis",

    // Market updates
    "market update",
    "cardano update",
    "ada update",
    "crypto update",
    "portfolio update",
    "market summary",
    "daily recap",

    // News requests
    "recent news",
    "latest news",
    "what's happening",
    "any updates",
    "breaking news",
    "market news",

    // Specific analysis patterns
    "i need deep analysis on",
    "what's the recent news on",
    "tell me about",
    "research on",
    "due diligence on",
    "dd on",

    // TLDR and summary requests
    "tldr",
    "tl;dr",
    "too long didn't read",
    "give me a summary",
    "summarize this",
    "brief overview",
    "quick recap"
];

export const misterEvaluator: Evaluator = {
    name: "MISTER_DELEGATION",
    alwaysRun: true,
    similes: [
        "CRYPTO_QUERY",
        "CARDANO_ANALYSIS",
        "PRICE_CHECK",
        "MARKET_ANALYSIS",
        "TICKER_REQUEST"
    ],
    description: "Evaluates if a message should be delegated to MISTER for crypto analysis",

    validate: async (runtime: IAgentRuntime, message: Memory): Promise<boolean> => {
        try {
            const messageText = message.content.text?.toLowerCase() || "";
            const originalText = message.content.text || "";

            // Check if DELEGATE_TO_MISTER action was already executed for this message
            if (message.content.action === "DELEGATE_TO_MISTER") {
                elizaLogger.debug("🔄 MISTER EVALUATOR: Skipping - action already executed");
                return false;
            }

            // 1. Check for exact phrase matches first (highest priority)
            const hasExactPhrase = MISTER_PHRASES.some(phrase =>
                messageText.includes(phrase.toLowerCase())
            );

            if (hasExactPhrase) {
                elizaLogger.debug("MISTER delegation triggered by exact phrase match");
                return true;
            }

            // 2. Check for ticker patterns with $ symbol (AGGRESSIVE)
            const tickerPattern = /\$[A-Z]{2,10}\b/g;
            const hasTicker = tickerPattern.test(originalText);

            if (hasTicker) {
                elizaLogger.debug("MISTER delegation triggered by ticker pattern ($SYMBOL)");
                return true;
            }

            // 2b. Check for any $ symbol followed by letters (even more aggressive)
            const anyDollarPattern = /\$[A-Za-z]+/g;
            const hasAnyDollarTicker = anyDollarPattern.test(originalText);

            if (hasAnyDollarTicker) {
                elizaLogger.debug("MISTER delegation triggered by any $ symbol pattern");
                return true;
            }

            // 3. Check for specific analysis request patterns
            const analysisPatterns = [
                /deep analysis on\s+\w+/i,
                /analyze\s+\w+\s+(token|coin|project)/i,
                /recent news on\s+\w+/i,
                /what's.*news.*on\s+\w+/i,
                /tell me about\s+\$?\w+\s+(token|coin|crypto)/i,
                /research on\s+\$?\w+/i,
                /dd on\s+\$?\w+/i,
                /risk.*analysis.*\$?\w+/i,
                /cabal.*check.*\$?\w+/i
            ];

            const hasAnalysisPattern = analysisPatterns.some(pattern =>
                pattern.test(originalText)
            );

            if (hasAnalysisPattern) {
                elizaLogger.debug("MISTER delegation triggered by analysis pattern");
                return true;
            }

            // 4. Check for crypto keyword matches (need at least 1)
            const hasKeyword = CRYPTO_KEYWORDS.some(keyword =>
                messageText.includes(keyword.toLowerCase())
            );

            if (hasKeyword) {
                elizaLogger.debug("MISTER delegation triggered by keyword match");
                return true;
            }

            // 5. Check for price/value queries with any token mention (SUPER AGGRESSIVE)
            const pricePatterns = [
                /price.*of.*\$?\w+/i,
                /value.*of.*\$?\w+/i,
                /worth.*of.*\$?\w+/i,
                /cost.*of.*\$?\w+/i,
                /how much.*\$?\w+/i,
                /\$?\w+.*trading.*at/i,
                /\$?\w+.*market.*cap/i,
                /what's.*price.*\$?\w+/i,
                /\$?\w+.*price/i,
                /price.*\$?\w+/i
            ];

            const hasPricePattern = pricePatterns.some(pattern =>
                pattern.test(originalText)
            );

            if (hasPricePattern) {
                elizaLogger.debug("MISTER delegation triggered by price query pattern");
                return true;
            }

            // 6. SUPER AGGRESSIVE: Any mention of "price" + any $ symbol
            if (messageText.includes("price") && originalText.includes("$")) {
                elizaLogger.debug("MISTER delegation triggered by price + $ symbol combination");
                return true;
            }

            return false;

        } catch (error) {
            elizaLogger.error("Error in MISTER validation:", error);
            return false;
        }
    },

    handler: async (runtime: IAgentRuntime, message: Memory): Promise<Memory[]> => {
        try {
            const messageText = message.content.text;

            if (!messageText) {
                elizaLogger.warn("No message text provided for MISTER delegation");
                return [];
            }

            // Create cache key from message ID and text to prevent duplicate evaluations
            const cacheKey = `${message.id}-${messageText}`;

            if (evaluationCache.has(cacheKey)) {
                elizaLogger.debug("🔄 MISTER EVALUATOR: Skipping duplicate evaluation for same message");
                return [];
            }

            // Mark this message as being evaluated
            evaluationCache.set(cacheKey, true);

            // Clean up old cache entries (keep only last 100)
            if (evaluationCache.size > 100) {
                const firstKey = evaluationCache.keys().next().value;
                if (firstKey) {
                    evaluationCache.delete(firstKey);
                }
            }

            elizaLogger.info("🚀 MISTER EVALUATOR: Delegating to MISTER:", messageText);

            const misterService = new MisterService();

            // Get user context for MISTER
            const userId = message.userId || "unknown";
            const conversationId = message.roomId || "unknown";

            elizaLogger.debug(`Delegation context - userId: ${userId}, conversationId: ${conversationId}`);

            // Send to MISTER
            const misterResponse = await misterService.generateResponse(messageText, {
                userId,
                conversationId
            });

            elizaLogger.debug(`MISTER response received - length: ${misterResponse.length} characters`);

            // Create response memory WITHOUT embedding - let system generate fresh embedding
            const responseMemory: Memory = {
                id: stringToUuid(`mister-response-${Date.now()}-${Math.random()}`),
                userId: message.userId,
                agentId: runtime.agentId,
                roomId: message.roomId,
                content: {
                    text: misterResponse,
                    source: "MISTER",
                    action: "MISTER_DELEGATION"
                },
                createdAt: Date.now()
                // NO embedding property - let the system generate it with consistent dimensions
                // This ensures MISTER responses use the same embedding model as the main system
            };

            elizaLogger.success("✅ MISTER EVALUATOR: Successfully delegated and received response");
            return [responseMemory];

        } catch (error) {
            elizaLogger.error("❌ MISTER EVALUATOR: Error in delegation handler:", error);

            // Create error response memory WITHOUT embedding - let system generate fresh embedding
            const errorMemory: Memory = {
                id: stringToUuid(`mister-error-${Date.now()}-${Math.random()}`),
                userId: message.userId,
                agentId: runtime.agentId,
                roomId: message.roomId,
                content: {
                    text: "I'm having trouble connecting to my crypto analysis system right now. Please try again later.",
                    source: "MISTER",
                    action: "MISTER_DELEGATION",
                    error: error instanceof Error ? error.message : "Unknown error"
                },
                createdAt: Date.now()
                // NO embedding property - let the system generate it with consistent dimensions
            };

            return [errorMemory];
        }
    },

    examples: []
};
