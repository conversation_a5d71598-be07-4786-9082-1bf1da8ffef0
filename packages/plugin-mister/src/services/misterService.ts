import { elizaLogger } from "@elizaos/core";

export interface MisterRequest {
    messages: Array<{
        role: string;
        content: string;
    }>;
    metadata?: {
        source?: string;
        userId?: string;
        conversationId?: string;
    };
}

export interface MisterResponse {
    text: string;
    files?: any[];
    reasoningDetails?: any[];
    toolCalls?: any[];
    toolResults?: any[];
    finishReason?: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    warnings?: any[];
    request?: any;
    sources?: any[];
}

export class MisterService {
    private baseURL: string;
    private endpoint: string;
    private timeout: number;

    constructor() {
        this.baseURL = "https://misterexc6.ngrok.io";
        this.endpoint = "/api/agents/MISTERAgent/generate";
        this.timeout = 30000; // 30 seconds for MISTER's response time
    }

    /**
     * Check if message requires extended timeout for deep analysis or ticker queries
     */
    private requiresExtendedTimeout(message: string): boolean {
        const extendedTimeoutPatterns = [
            // Deep analysis patterns
            /deep analysis/i,
            /analyze.*\$?\w+.*(token|coin|project)/i,
            /risk.*analysis/i,
            /cabal.*check/i,
            /holder.*analysis/i,
            /whale.*analysis/i,
            /distribution.*analysis/i,
            /dd on/i,
            /due diligence/i,
            /research on/i,
            /comprehensive.*analysis/i,
            /detailed.*analysis/i,

            // Ticker queries (require research and analysis)
            /what's the ticker/i,
            /give me a ticker/i,
            /what's the play/i,
            /got any plays/i,
            /any alpha/i,
            /investment advice/i,
            /trading opportunity/i,

            // TLDR requests
            /tldr/i,
            /tl;dr/i,
            /too long didn't read/i,
            /summarize/i,
            /summary/i
        ];

        return extendedTimeoutPatterns.some(pattern => pattern.test(message));
    }

    /**
     * Send a message to MISTER agent and get response
     */
    async generateResponse(
        message: string,
        options: {
            userId?: string;
            conversationId?: string;
        } = {}
    ): Promise<string> {
        try {
            elizaLogger.debug("Sending message to MISTER:", message);

            const requestBody: MisterRequest = {
                messages: [
                    {
                        role: "user",
                        content: message
                    }
                ],
                metadata: {
                    source: "eliza",
                    userId: options.userId || undefined,
                    conversationId: options.conversationId || undefined,
                },
            };

            elizaLogger.debug("Request body:", JSON.stringify(requestBody, null, 2));

            // Use extended timeout for deep analysis queries
            const isDeepAnalysis = this.requiresExtendedTimeout(message);
            const timeoutDuration = isDeepAnalysis ? 120000 : this.timeout; // 2 minutes for deep analysis, 30 seconds for others

            if (isDeepAnalysis) {
                elizaLogger.debug("Using extended timeout (2 minutes) for complex query requiring analysis/research");
            }

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

            const response = await fetch(`${this.baseURL}${this.endpoint}`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorText = await response.text();
                elizaLogger.error(`MISTER API Error - Status: ${response.status}, Response: ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const data: MisterResponse = await response.json();

            if (!data.text) {
                throw new Error("MISTER API returned empty response");
            }

            elizaLogger.debug("Received response from MISTER:", data.text);
            return data.text;

        } catch (error) {
            elizaLogger.error("MISTER Service Error:", error);
            elizaLogger.error("Error details:", {
                name: error instanceof Error ? error.name : 'Unknown',
                message: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined
            });

            // Provide user-friendly error messages
            if (error instanceof Error) {
                if (error.name === "AbortError") {
                    const requiresExtendedTimeout = this.requiresExtendedTimeout(message);
                    if (requiresExtendedTimeout) {
                        return "MISTER's analysis is taking longer than expected (over 2 minutes). Complex queries like deep analysis, ticker research, and TLDR summaries require more processing time. Please try again or ask for a simpler query.";
                    } else {
                        return "MISTER is taking longer than usual to respond. Please try again.";
                    }
                } else if (error.message.includes("HTTP 503")) {
                    return "MISTER is temporarily unavailable. Please try again later.";
                } else if (error.message.includes("HTTP 429")) {
                    return "Rate limit exceeded. Please wait a moment before trying again.";
                } else if (error.message.includes("fetch")) {
                    return "Unable to connect to MISTER. Please check your internet connection.";
                } else {
                    return "Sorry, I'm having trouble connecting to MISTER right now. Please try again later.";
                }
            } else {
                return "Sorry, I'm having trouble connecting to MISTER right now. Please try again later.";
            }
        }
    }

    /**
     * Check if MISTER service is available
     */
    async healthCheck(): Promise<boolean> {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(`${this.baseURL}/health`, {
                method: "GET",
                signal: controller.signal,
            });

            clearTimeout(timeoutId);
            return response.ok;
        } catch (error) {
            elizaLogger.warn("MISTER health check failed:", error);
            return false;
        }
    }
}
