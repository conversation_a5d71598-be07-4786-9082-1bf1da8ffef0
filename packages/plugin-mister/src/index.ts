import { Plugin } from "@elizaos/core";
import { delegateToMisterAction } from "./actions/delegateToMister.js";
import { misterEvaluator } from "./evaluators/misterEvaluator.js";

export const misterPlugin: Plugin = {
    name: "mister",
    description: "MISTER Agent delegation plugin for specialized Cardano crypto analysis",
    actions: [delegateToMisterAction],
    providers: [],
    evaluators: [misterEvaluator],
    services: [],
    clients: []
};

export default misterPlugin;

// Export components for direct use if needed
export { delegateToMisterAction } from "./actions/delegateToMister.js";
export { misterEvaluator } from "./evaluators/misterEvaluator.js";
export { MisterService } from "./services/misterService.js";
