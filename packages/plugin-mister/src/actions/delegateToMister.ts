import {
    <PERSON>,
    IAgentRuntime,
    <PERSON>,
    el<PERSON><PERSON><PERSON><PERSON>,
    HandlerCallback,
} from "@elizaos/core";
import { MisterService } from "../services/misterService.js";

// Cache to prevent duplicate delegations for the same message
const delegationCache = new Map<string, boolean>();

// Keywords that trigger MISTER delegation
const CRYPTO_KEYWORDS = [
    // Price queries
    "price", "cost", "value", "worth", "trading", "market cap", "volume", "mcap",
    "marketcap", "market value", "valuation", "pricing", "quote", "rate",

    // Cardano ecosystem tokens
    "ada", "cardano", "snek", "hosky", "min", "sundae", "meld", "copi", "indy",
    "iag", "ntx", "stuff", "bbsnek", "sugr", "nikepig", "fldt", "lq", "wrt",
    "agix", "wmt", "wmtx", "lenfi", "fren", "bank", "chad", "clay", "book",
    "newm", "pavia", "cornucopias", "djed", "shen", "optim", "vyfi", "splash",

    // Major cryptocurrencies
    "bitcoin", "btc", "ethereum", "eth", "bnb", "solana", "sol", "xrp", "ripple",
    "doge", "dogecoin", "avax", "avalanche", "matic", "polygon", "dot", "polkadot",
    "link", "chainlink", "uni", "uniswap", "ltc", "litecoin", "atom", "cosmos",
    "near", "algo", "algorand", "xlm", "stellar", "vet", "vechain", "icp",

    // Crypto general terms
    "crypto", "cryptocurrency", "token", "coin", "blockchain", "defi", "nft",
    "staking", "yield", "liquidity", "farming", "mining", "wallet", "exchange",
    "dex", "cex", "swap", "bridge", "protocol", "smart contract", "dao",

    // Analysis terms
    "analyze", "analysis", "risk", "cabal", "holder", "distribution", "chart",
    "technical analysis", "ta", "bullish", "bearish", "moon", "dump", "pump",
    "fundamentals", "tokenomics", "utility", "use case", "roadmap", "team",
    "partnerships", "adoption", "metrics", "on-chain", "whale", "retail",

    // MISTER specific
    "ticker", "alpha", "gem", "play", "speculative", "degen", "ape", "diamond hands",
    "hodl", "fud", "fomo", "rekt", "moon", "lambo", "bag", "shill", "research",

    // Market terms
    "bull", "bear", "trend", "support", "resistance", "breakout", "dip",
    "rally", "correction", "crash", "ath", "all time high", "all time low",
    "consolidation", "accumulation", "distribution", "volume profile", "rsi",
    "macd", "fibonacci", "moving average", "candlestick", "pattern",

    // News and updates
    "news", "update", "announcement", "partnership", "listing", "launch",
    "mainnet", "testnet", "upgrade", "fork", "governance", "proposal", "vote",

    // Summary and TLDR
    "tldr", "tl;dr", "summarize", "summary", "recap", "overview", "brief"
];

const MISTER_PHRASES = [
    // Ticker requests
    "what's the ticker",
    "give me a ticker",
    "what's the play",
    "any alpha",
    "got any plays",
    "what should i buy",
    "investment advice",
    "trading opportunity",

    // Analysis requests
    "deep analysis",
    "analyze this",
    "risk analysis",
    "token analysis",
    "project analysis",
    "cabal check",
    "holder analysis",
    "whale analysis",
    "distribution analysis",

    // Market updates
    "market update",
    "cardano update",
    "ada update",
    "crypto update",
    "portfolio update",
    "market summary",
    "daily recap",

    // News requests
    "recent news",
    "latest news",
    "what's happening",
    "any updates",
    "breaking news",
    "market news",

    // Specific analysis patterns
    "i need deep analysis on",
    "what's the recent news on",
    "tell me about",
    "research on",
    "due diligence on",
    "dd on",

    // TLDR and summary requests
    "tldr",
    "tl;dr",
    "too long didn't read",
    "give me a summary",
    "summarize this",
    "brief overview",
    "quick recap"
];

export const delegateToMisterAction: Action = {
    name: "DELEGATE_TO_MISTER",
    similes: [
        "ASK_MISTER",
        "CRYPTO_QUERY",
        "CARDANO_ANALYSIS",
        "PRICE_CHECK",
        "MARKET_ANALYSIS",
        "GET_PRICE",
        "TICKER_QUERY",
        "TOKEN_ANALYSIS"
    ],
    description: "REQUIRED for ALL crypto price queries, token analysis, and Cardano-related questions. Delegates to specialized MISTER agent.",
    suppressInitialMessage: true,

    validate: async (runtime: IAgentRuntime, message: Memory): Promise<boolean> => {
        try {
            const messageText = message.content.text?.toLowerCase() || "";
            const originalText = message.content.text || "";

            elizaLogger.info(`🔍 MISTER ACTION: Validating message: "${originalText}"`);

            // 1. Check for exact phrase matches first (highest priority)
            const hasExactPhrase = MISTER_PHRASES.some(phrase =>
                messageText.includes(phrase.toLowerCase())
            );

            if (hasExactPhrase) {
                elizaLogger.info("🎯 MISTER ACTION: TRIGGERED by exact phrase match");
                return true;
            }

            // 2. Check for ticker patterns with $ symbol (AGGRESSIVE)
            const tickerPattern = /\$[A-Z]{2,10}\b/g;
            const hasTicker = tickerPattern.test(originalText);

            if (hasTicker) {
                elizaLogger.info("🎯 MISTER ACTION: TRIGGERED by ticker pattern ($SYMBOL)");
                return true;
            }

            // 2b. Check for any $ symbol followed by letters (even more aggressive)
            const anyDollarPattern = /\$[A-Za-z]+/g;
            const hasAnyDollarTicker = anyDollarPattern.test(originalText);

            if (hasAnyDollarTicker) {
                elizaLogger.debug("MISTER delegation triggered by any $ symbol pattern");
                return true;
            }

            // 3. Check for specific analysis request patterns
            const analysisPatterns = [
                /deep analysis on\s+\w+/i,
                /analyze\s+\w+\s+(token|coin|project)/i,
                /recent news on\s+\w+/i,
                /what's.*news.*on\s+\w+/i,
                /tell me about\s+\$?\w+\s+(token|coin|crypto)/i,
                /research on\s+\$?\w+/i,
                /dd on\s+\$?\w+/i,
                /risk.*analysis.*\$?\w+/i,
                /cabal.*check.*\$?\w+/i
            ];

            const hasAnalysisPattern = analysisPatterns.some(pattern =>
                pattern.test(originalText)
            );

            if (hasAnalysisPattern) {
                elizaLogger.debug("MISTER delegation triggered by analysis pattern");
                return true;
            }

            // 4. Check for crypto keyword matches (need at least 1)
            const hasKeyword = CRYPTO_KEYWORDS.some(keyword =>
                messageText.includes(keyword.toLowerCase())
            );

            if (hasKeyword) {
                elizaLogger.debug("MISTER delegation triggered by keyword match");
                return true;
            }

            // 5. Check for token names in ALL CAPS (likely tickers)
            const capsTokenPattern = /\b[A-Z]{2,10}\b/g;
            const capsMatches = originalText.match(capsTokenPattern) || [];
            const hasCapsToken = capsMatches.length > 0 && capsMatches.some(token =>
                // Filter out common non-crypto words
                !["THE", "AND", "FOR", "ARE", "BUT", "NOT", "YOU", "ALL", "CAN", "HER", "WAS", "ONE", "OUR", "HAD", "BUT", "WHAT", "WHEN", "WHERE", "WHO", "WHY", "HOW", "THIS", "THAT", "THEY", "THEM", "HAVE", "FROM", "BEEN", "SAID", "EACH", "WHICH", "THEIR", "TIME", "WILL", "ABOUT", "WOULD", "THERE", "COULD", "OTHER", "AFTER", "FIRST", "WELL", "MANY", "SOME", "THESE", "MAY", "THEN", "THAN", "ONLY", "ITS", "ALSO", "BACK", "GOOD", "JUST", "BEING", "NOW", "MADE", "BEFORE", "HERE", "THROUGH", "MUCH", "WHERE", "SHOULD", "WELL", "WITHOUT", "MOST", "NEED", "RIGHT", "STILL", "WANT", "THOSE", "CAME", "TAKE", "USED", "WORK", "LIFE", "BECOME", "UNDER", "LAST", "NEVER", "PLACE", "ANOTHER", "KNOW", "WHILE", "SAME", "OLD", "SEE", "HIM", "TWO", "MORE", "GO", "DO", "NO", "WAY", "COULD", "MY", "THAN", "FIRST", "WATER", "BEEN", "CALL", "WHO", "OIL", "SIT", "NOW", "FIND", "LONG", "DOWN", "DAY", "DID", "GET", "COME", "MADE", "MAY", "PART"].includes(token)
            );

            if (hasCapsToken) {
                elizaLogger.debug("MISTER delegation triggered by CAPS token pattern");
                return true;
            }

            // 6. Check for price/value queries with any token mention (SUPER AGGRESSIVE)
            const pricePatterns = [
                /price.*of.*\$?\w+/i,
                /value.*of.*\$?\w+/i,
                /worth.*of.*\$?\w+/i,
                /cost.*of.*\$?\w+/i,
                /how much.*\$?\w+/i,
                /\$?\w+.*trading.*at/i,
                /\$?\w+.*market.*cap/i,
                /what's.*price.*\$?\w+/i,
                /\$?\w+.*price/i,
                /price.*\$?\w+/i
            ];

            const hasPricePattern = pricePatterns.some(pattern =>
                pattern.test(originalText)
            );

            if (hasPricePattern) {
                elizaLogger.debug("MISTER delegation triggered by price query pattern");
                return true;
            }

            // 6b. SUPER AGGRESSIVE: Any mention of "price" + any $ symbol
            if (messageText.includes("price") && originalText.includes("$")) {
                elizaLogger.debug("MISTER delegation triggered by price + $ symbol combination");
                return true;
            }

            elizaLogger.info("❌ MISTER ACTION: No triggers matched - not delegating");
            return false;

        } catch (error) {
            elizaLogger.error("Error in MISTER validation:", error);
            return false;
        }
    },

    handler: async (
        runtime: IAgentRuntime,
        message: Memory,
        state?: any,
        options?: any,
        callback?: HandlerCallback
    ): Promise<boolean> => {
        try {
            const messageText = message.content.text;

            if (!messageText) {
                elizaLogger.warn("No message text provided for MISTER delegation");
                return false;
            }

            // Create cache key from message ID and text to prevent duplicate delegations
            const cacheKey = `${message.id}-${messageText}`;

            if (delegationCache.has(cacheKey)) {
                elizaLogger.debug("🔄 MISTER ACTION: Skipping duplicate delegation for same message");
                return false;
            }

            // Mark this message as being delegated
            delegationCache.set(cacheKey, true);

            // Clean up old cache entries (keep only last 100)
            if (delegationCache.size > 100) {
                const firstKey = delegationCache.keys().next().value;
                if (firstKey) {
                    delegationCache.delete(firstKey);
                }
            }

            elizaLogger.info("🚀 MISTER ACTION HANDLER: Delegating to MISTER:", messageText);

            // Mark state as delegating to prevent RAG searches
            if (state) {
                state.isDelegating = true;
            }

            const misterService = new MisterService();

            // Get user context for MISTER
            const userId = message.userId || "unknown";
            const conversationId = message.roomId || "unknown";

            elizaLogger.debug(`Delegation context - userId: ${userId}, conversationId: ${conversationId}`);

            // Send to MISTER
            const misterResponse = await misterService.generateResponse(messageText, {
                userId,
                conversationId
            });

            elizaLogger.debug(`MISTER response received - length: ${misterResponse.length} characters`);

            // Send response back through callback
            if (callback) {
                elizaLogger.debug("Sending response via callback...");

                // CRITICAL FIX: Skip embedding generation for MISTER responses
                // MISTER service creates its own 6144D embeddings, which conflict with ElizaOS 1536D embeddings
                elizaLogger.debug("🔧 MISTER RESPONSE: Skipping embedding generation to prevent dimension mismatch");

                await callback({
                    text: misterResponse,
                    content: {
                        text: misterResponse,
                        source: "MISTER",
                        action: "DELEGATE_TO_MISTER"
                    },
                    // Force skip embedding to prevent 6144D vs 1536D conflict
                    skipEmbedding: true
                });
                elizaLogger.debug("Callback executed successfully with embedding skip");
            } else {
                elizaLogger.warn("No callback provided for MISTER response");
            }

            elizaLogger.success("Successfully delegated to MISTER and received response");
            return true;

        } catch (error) {
            elizaLogger.error("Error in MISTER delegation handler:", error);

            // Fallback response
            if (callback) {
                await callback({
                    text: "I'm having trouble connecting to my crypto analysis system right now. Please try again later.",
                    content: {
                        text: "MISTER delegation failed",
                        error: error instanceof Error ? error.message : "Unknown error",
                        action: "DELEGATE_TO_MISTER"
                    },
                    // Skip embedding for error responses too
                    skipEmbedding: true
                });
            }

            return false;
        }
    },

    examples: [
        [
            {
                user: "{{user1}}",
                content: {
                    text: "price of $MISTER"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Checking $MISTER price data for you.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "$SNEK price"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Getting $SNEK price data.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "What's the price of ADA?"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Pulling ADA market data.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "I need deep analysis on $SNEK"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Running deep analysis on $SNEK.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "What's the recent news on HOSKY?"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Checking HOSKY news.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "What's the ticker?"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Finding the latest alpha.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "Cabal check on $MIN"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Running cabal analysis on $MIN.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "Bitcoin analysis"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Analyzing Bitcoin data.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ],
        [
            {
                user: "{{user1}}",
                content: {
                    text: "Tell me about LENFI token"
                }
            },
            {
                user: "{{agent}}",
                content: {
                    text: "Researching LENFI.",
                    action: "DELEGATE_TO_MISTER"
                }
            }
        ]
    ]
};
