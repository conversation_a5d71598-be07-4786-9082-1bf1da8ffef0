# @elizaos/plugin-mister

<PERSON><PERSON>ER Agent delegation plugin for Eliza - specialized Cardano crypto analysis.

## Overview

This plugin allows your <PERSON> agent to delegate crypto and Cardano-related queries to MISTER, a specialized crypto analysis agent with deep knowledge of the Cardano ecosystem. Your agent will continue to handle all other conversations normally.

## Features

- **Automatic Detection**: Recognizes crypto/Cardano-related queries
- **Seamless Delegation**: Sends relevant queries to MISTER's API
- **Fallback Handling**: Graceful error handling with user-friendly messages
- **Zero Interference**: Your existing Eliza functionality remains completely intact
- **Easy Toggle**: Can be easily enabled/disabled by adding/removing the plugin

## Trigger Keywords

The plugin automatically detects and delegates messages containing:

### Crypto Keywords
- Price, trading, market cap, volume
- Bitcoin, Ethereum, ADA, Cardano
- Cardano tokens: SNEK, HOSKY, MIN, etc.
- Analysis terms: risk, cabal, holder distribution
- Market terms: bullish, bearish, pump, dump

### Exact Phrases
- "What's the ticker?"
- "Give me a ticker"
- "Market update"
- "Cardano update"
- "Portfolio risk"
- "Token risk"

### Special Triggers
- Messages containing $ symbol (token tickers)
- Short messages with crypto context

## Installation

1. Build the plugin:
```bash
cd packages/plugin-mister
npm run build
```

2. Add to your character file:
```json
{
  "plugins": ["@elizaos/plugin-mister"]
}
```

## Usage Examples

```
User: "What's the price of ADA?"
→ Delegates to MISTER for current ADA price and analysis

User: "What's the ticker?"
→ Delegates to MISTER for high-potential Cardano plays

User: "How's the weather?"
→ Handled by your normal Eliza agent

User: "Analyze SNEK token risk"
→ Delegates to MISTER for comprehensive risk analysis
```

## Configuration

No additional configuration required. The plugin uses MISTER's default endpoint:
- **Base URL**: `https://misterexc6.ngrok.io`
- **Endpoint**: `/api/agents/MISTERAgent/generate`

## Error Handling

The plugin includes robust error handling:
- Connection timeouts (30 seconds)
- Rate limiting detection
- Service unavailability
- Network errors

All errors result in user-friendly fallback messages.

## Safety Features

- **Non-destructive**: Does not modify any existing Eliza functionality
- **Isolated**: Runs as a separate action that can be easily disabled
- **Fallback**: Always provides a response, even if MISTER is unavailable
- **Logging**: Comprehensive logging for debugging

## Disabling the Plugin

To disable MISTER delegation, simply remove the plugin from your character file:

```json
{
  "plugins": []
}
```

Your agent will immediately revert to handling all messages with your configured provider.

## Development

```bash
# Build
npm run build

# Watch mode
npm run dev

# Clean
npm run clean
```

## Support

- **Creator**: @CASHCOLDGAME
- **MISTER Endpoint**: https://misterexc6.ngrok.io
- **Plugin Issues**: Check Eliza logs for detailed error information
