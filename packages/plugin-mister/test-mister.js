#!/usr/bin/env node

/**
 * Simple test script for MISTER plugin
 * Tests the delegation action and MISTER service
 */

import { MisterService } from './dist/services/misterService.js';
import { delegateToMisterAction } from './dist/actions/delegateToMister.js';

async function testMisterService() {
    console.log('🧪 Testing MISTER Service...\n');
    
    const misterService = new MisterService();
    
    // Test health check
    console.log('1. Testing health check...');
    try {
        const isHealthy = await misterService.healthCheck();
        console.log(`   Health check: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}\n`);
    } catch (error) {
        console.log(`   Health check failed: ${error.message}\n`);
    }
    
    // Test simple query
    console.log('2. Testing simple crypto query...');
    try {
        const response = await misterService.generateResponse("What's the price of ADA?");
        console.log(`   Response: ${response.substring(0, 100)}...\n`);
    } catch (error) {
        console.log(`   Query failed: ${error.message}\n`);
    }
}

async function testActionValidation() {
    console.log('🔍 Testing Action Validation...\n');
    
    // Mock runtime and message objects
    const mockRuntime = {};
    
    const testCases = [
        { text: "What's the price of ADA?", expected: true },
        { text: "What's the ticker?", expected: true },
        { text: "Analyze SNEK token risk", expected: true },
        { text: "How's the weather today?", expected: false },
        { text: "Tell me a joke", expected: false },
        { text: "Bitcoin price analysis", expected: true },
        { text: "Check $HOSKY volume", expected: true },
    ];
    
    for (const testCase of testCases) {
        const mockMessage = {
            content: { text: testCase.text }
        };
        
        try {
            const shouldDelegate = await delegateToMisterAction.validate(mockRuntime, mockMessage);
            const result = shouldDelegate === testCase.expected ? '✅' : '❌';
            console.log(`   ${result} "${testCase.text}" -> ${shouldDelegate} (expected: ${testCase.expected})`);
        } catch (error) {
            console.log(`   ❌ "${testCase.text}" -> Error: ${error.message}`);
        }
    }
    console.log();
}

async function main() {
    console.log('🚀 MISTER Plugin Test Suite\n');
    console.log('=' .repeat(50));
    
    await testActionValidation();
    await testMisterService();
    
    console.log('=' .repeat(50));
    console.log('✨ Test suite completed!\n');
    console.log('To use the plugin in your character:');
    console.log('1. Add "@elizaos/plugin-mister" to your character\'s plugins array');
    console.log('2. The plugin will automatically detect crypto queries');
    console.log('3. Check the logs to see delegation in action');
}

main().catch(console.error);
