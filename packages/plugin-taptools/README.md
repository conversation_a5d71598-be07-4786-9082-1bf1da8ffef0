# @elizaos/plugin-taptools

A plugin for <PERSON> that enables Cardano token data retrieval using the Taptools API.

## Features

- Real-time Cardano token price checking
- Detailed trading statistics for Cardano tokens
- Top volume tokens listing
- Support for all Cardano tokens listed on Taptools
- Natural language processing for token queries

## Installation

```bash
npm install @elizaos/plugin-taptools
```

## Configuration

1. Get your API key from [Taptools](https://taptools.io)

2. Set up your environment variables:

```bash
TAPTOOLS_API_KEY=your_api_key
```

3. Register the plugin in your Eliza configuration:

```typescript
import { TaptoolsPlugin } from "@elizaos/plugin-taptools";

// In your Eliza configuration
plugins: [
    new TaptoolsPlugin(),
    // ... other plugins
];
```

## Usage

The plugin responds to natural language queries about Cardano tokens. Here are some examples:

### Get Token Price

```plaintext
"What's the current price of ADA?"
"Show me SNEK price"
"Get the price of MISTER"
```

### Get Trading Stats

```plaintext
"Show me detailed stats for ADA"
"What are the trading metrics for SNEK?"
"Give me all the stats for MISTER token"
```

### Get Top Volume Tokens

```plaintext
"Show me the top volume Cardano tokens"
"What are the top 10 Cardano tokens by volume in the last week?"
"Show me the top 20 tokens by volume in the last month"
```

### Supported Tokens

The plugin supports all Cardano tokens listed on Taptools, including:

- ADA (Cardano's native token)
- SNEK
- MISTER
- CHAD
- HOSKY
- SUNDAE
- MIN
- LQ
- LENFI
- AGIX
- COPI
- INDY
- VYFI
- CLAY
- TALOS
- IAG
- BOO
- DJED
- WMT
- And many more...

### Available Actions

#### GET_CARDANO_PRICE

Fetches the current price of a Cardano token.

```typescript
// Example response format
{
  symbol: "ADA",
  name: "Cardano",
  price: 0.45,
  priceChange24h: 2.35,
  marketCap: 16200000000,
  volume24h: 245800000,
  supply: 36000000000,
  maxSupply: 45000000000,
  lastUpdated: "2023-06-01T12:00:00Z"
}
```

#### GET_CARDANO_TRADING_STATS

Fetches detailed trading statistics for a Cardano token.

```typescript
// Example response format
{
  symbol: "ADA",
  name: "Cardano",
  price: 0.45,
  priceChange24h: 2.35,
  priceChange7d: 5.67,
  priceChange30d: -1.23,
  volume24h: 245800000,
  volumeChange24h: 12.5,
  marketCap: 16200000000,
  fullyDilutedMarketCap: 20250000000,
  supply: 36000000000,
  maxSupply: 45000000000,
  holders: 3500000,
  transactions24h: 125000,
  lastUpdated: "2023-06-01T12:00:00Z"
}
```

#### GET_TOP_VOLUME_CARDANO

Fetches the top volume Cardano tokens.

```typescript
// Example response format
[
  {
    symbol: "ADA",
    name: "Cardano",
    price: 0.45,
    priceChange24h: 2.35,
    volume: 245800000,
    marketCap: 16200000000,
    rank: 1
  },
  {
    symbol: "SNEK",
    name: "SNEK",
    price: 0.000123,
    priceChange24h: 5.67,
    volume: 1200000,
    marketCap: 12300000,
    rank: 2
  },
  // ... more tokens
]
```

## License

MIT
