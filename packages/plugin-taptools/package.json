{"name": "@elizaos/plugin-taptools", "version": "0.1.0", "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "dependencies": {"@elizaos/core": "workspace:^", "axios": "^1.8.1", "zod": "^3.22.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "tsup": "^8.3.5"}, "scripts": {"build": "tsup --format esm --dts", "dev": "tsup --format esm --dts --watch", "clean": "rm -rf dist", "lint": "biome lint .", "lint:fix": "biome check --apply .", "format": "biome format .", "format:fix": "biome format --write .", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "publishConfig": {"access": "public"}}