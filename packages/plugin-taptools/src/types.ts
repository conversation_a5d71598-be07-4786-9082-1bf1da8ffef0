import type { Content } from "@elizaos/core";

// Action content types
export interface GetPriceContent extends Content {
    symbol: string;
}

export interface GetTradingStatsContent extends Content {
    symbol: string;
}

export interface GetTopVolumeContent extends Content {
    timeframe?: string;
    limit?: number;
}

// API response types
export interface TokenPriceData {
    symbol: string;
    name: string;
    price: number;
    priceChange24h: number;
    marketCap: number;
    volume24h: number;
    supply: number;
    maxSupply?: number;
    lastUpdated: string;
}

export interface TokenTradingStats {
    symbol: string;
    name: string;
    price: number;
    priceChange24h: number;
    priceChange7d: number;
    priceChange30d: number;
    volume24h: number;
    volumeChange24h: number;
    marketCap: number;
    fullyDilutedMarketCap?: number;
    supply: number;
    maxSupply?: number;
    holders?: number;
    transactions24h?: number;
    lastUpdated: string;
}

export interface TopVolumeToken {
    symbol: string;
    name: string;
    price: number;
    priceChange24h: number;
    volume: number;
    marketCap: number;
    rank: number;
}
