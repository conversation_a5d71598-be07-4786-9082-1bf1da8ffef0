import type { IAgentRuntime } from "@elizaos/core";
import { z } from "zod";

export const taptoolsEnvSchema = z.object({
    TAPTOOLS_API_KEY: z
        .string()
        .min(1, "Taptools API key is required"),
});

export type TaptoolsConfig = z.infer<typeof taptoolsEnvSchema>;

export async function validateTaptoolsConfig(
    runtime: IAgentRuntime
): Promise<TaptoolsConfig> {
    try {
        const config = {
            TAPTOOLS_API_KEY: runtime.getSetting("TAPTOOLS_API_KEY"),
        };

        return taptoolsEnvSchema.parse(config);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const errorMessages = error.errors
                .map((err) => `${err.path.join(".")}: ${err.message}`)
                .join("\n");
            throw new Error(
                `Taptools configuration validation failed:\n${errorMessages}`
            );
        }
        throw error;
    }
}
