import type { Plugin } from "@elizaos/core";
import getPrice from "./actions/getPrice";
import getTradingStats from "./actions/getTradingStats";
import getTopVolume from "./actions/getTopVolume";

export const taptoolsPlugin: Plugin = {
    name: "taptools",
    description: "Taptools Cardano Token Plugin for Eliza",
    actions: [getPrice, getTradingStats, getTopVolume],
    evaluators: [],
    providers: [],
};

export default taptoolsPlugin;
