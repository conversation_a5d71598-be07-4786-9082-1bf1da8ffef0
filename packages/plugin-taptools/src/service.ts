import axios from "axios";
import type { TokenPriceData, TokenTradingStats, TopVolumeToken } from "./types";

const BASE_URL = "https://openapi.taptools.io/api/v1";

export const createTaptoolsService = (apiKey: string) => {
    const client = axios.create({
        baseURL: BASE_URL,
        headers: {
            "Authorization": `ApiAuth<PERSON>ey ${apiKey}`,
            "Accept": "application/json",
        },
    });

    /**
     * Get token price data (quote)
     * @param symbol The token symbol or policy ID
     */
    const getTokenPrice = async (symbol: string): Promise<TokenPriceData> => {
        try {
            // Normalize the symbol
            const normalizedSymbol = symbol.toUpperCase().trim();

            // Make API call to get token price
            const response = await client.get(`/token/quote`, {
                params: {
                    symbol: normalizedSymbol,
                },
            });

            if (!response.data || response.data.error) {
                throw new Error(response.data?.error || "Failed to fetch token price");
            }

            // Map the API response to our TokenPriceData interface
            return {
                symbol: normalizedSymbol,
                name: response.data.name || normalizedSymbol,
                price: response.data.price || 0,
                priceChange24h: response.data.priceChange24h || 0,
                marketCap: response.data.marketCap || 0,
                volume24h: response.data.volume24h || 0,
                supply: response.data.supply || 0,
                maxSupply: response.data.maxSupply || null,
                lastUpdated: response.data.lastUpdated || new Date().toISOString(),
            };
        } catch (error) {
            console.error("Error fetching token price:", error);
            throw new Error(`Failed to get price for ${symbol}: ${error.message}`);
        }
    };

    /**
     * Get token trading statistics
     * @param symbol The token symbol or policy ID
     */
    const getTokenTradingStats = async (symbol: string): Promise<TokenTradingStats> => {
        try {
            // Normalize the symbol
            const normalizedSymbol = symbol.toUpperCase().trim();

            // Make API call to get token trading stats
            const response = await client.get(`/token/trading/stats`, {
                params: {
                    symbol: normalizedSymbol,
                },
            });

            if (!response.data || response.data.error) {
                throw new Error(response.data?.error || "Failed to fetch token trading stats");
            }

            // Map the API response to our TokenTradingStats interface
            return {
                symbol: normalizedSymbol,
                name: response.data.name || normalizedSymbol,
                price: response.data.price || 0,
                priceChange24h: response.data.priceChange24h || 0,
                priceChange7d: response.data.priceChange7d || 0,
                priceChange30d: response.data.priceChange30d || 0,
                volume24h: response.data.volume24h || 0,
                volumeChange24h: response.data.volumeChange24h || 0,
                marketCap: response.data.marketCap || 0,
                fullyDilutedMarketCap: response.data.fullyDilutedMarketCap || 0,
                supply: response.data.supply || 0,
                maxSupply: response.data.maxSupply || null,
                holders: response.data.holders || 0,
                transactions24h: response.data.transactions24h || 0,
                lastUpdated: response.data.lastUpdated || new Date().toISOString(),
            };
        } catch (error) {
            console.error("Error fetching token trading stats:", error);
            throw new Error(`Failed to get trading stats for ${symbol}: ${error.message}`);
        }
    };

    /**
     * Get top volume tokens
     * @param timeframe Timeframe for volume calculation (1h, 4h, 12h, 24h, 7d, 30d, 180d, 1y, all)
     * @param limit Number of tokens to return (max 100)
     */
    const getTopVolumeTokens = async (
        timeframe: string = "24h",
        limit: number = 100
    ): Promise<TopVolumeToken[]> => {
        try {
            // Calculate pagination parameters
            const perPage = Math.min(limit, 100);
            const totalPages = Math.ceil(limit / perPage);
            let allTokens: TopVolumeToken[] = [];

            // Make paginated API calls to get top volume tokens
            for (let page = 1; page <= totalPages; page++) {
                const response = await client.get(`/token/top/volume`, {
                    params: {
                        timeframe,
                        page,
                        perPage,
                    },
                });

                if (!response.data || response.data.error) {
                    throw new Error(response.data?.error || "Failed to fetch top volume tokens");
                }

                const tokens = response.data.tokens || [];
                allTokens = [...allTokens, ...tokens.map((token: any) => ({
                    symbol: token.symbol,
                    name: token.name,
                    price: token.price || 0,
                    priceChange24h: token.priceChange24h || 0,
                    volume: token.volume || 0,
                    marketCap: token.marketCap || 0,
                    rank: token.rank || 0,
                }))];

                // If we've collected enough tokens or there are no more pages, break
                if (allTokens.length >= limit || tokens.length < perPage) {
                    break;
                }
            }

            // Return only the requested number of tokens
            return allTokens.slice(0, limit);
        } catch (error) {
            console.error("Error fetching top volume tokens:", error);
            throw new Error(`Failed to get top volume tokens: ${error.message}`);
        }
    };

    return {
        getTokenPrice,
        getTokenTradingStats,
        getTopVolumeTokens,
    };
};
