import { z } from "zod";
import type { GetTopVolumeContent } from "../../types";

const getTopVolumeSchema = z.object({
    timeframe: z.string().optional(),
    limit: z.number().min(1).max(100).optional(),
});

export function isGetTopVolumeContent(content: any): content is GetTopVolumeContent {
    try {
        getTopVolumeSchema.parse(content);
        return true;
    } catch (error) {
        return false;
    }
}
