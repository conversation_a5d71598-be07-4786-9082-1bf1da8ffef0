import {
    composeContext,
    eliza<PERSON>ogger,
    generateObjectDeprecated,
    type HandlerCallback,
    type IAgentRuntime,
    type Memory,
    ModelClass,
    type State,
    type Action,
} from "@elizaos/core";
import { validateTaptoolsConfig } from "../../environment";
import { topVolumeExamples } from "./examples";
import { createTaptoolsService } from "../../service";
import { getTopVolumeTemplate } from "./template";
import type { GetTopVolumeContent } from "../../types";
import { isGetTopVolumeContent } from "./validation";

export default {
    name: "GET_TOP_VOLUME_CARDANO",
    similes: [
        "TOP_CARDANO_TOKENS",
        "TOP_VOLUME_ADA",
        "CARDANO_TOP_TOKENS",
        "ADA_TOP_VOLUME",
        "CARDANO_VOLUME_LEADERS",
        "TOP_TRADING_CARDANO",
    ],
    // eslint-disable-next-line
    validate: async (runtime: IAgentRuntime, _message: Memory) => {
        await validateTaptoolsConfig(runtime);
        return true;
    },
    description: "Get the top volume Cardano tokens from Taptools",
    handler: async (
        runtime: IAgentRuntime,
        message: Memory,
        state: State,
        options: { [key: string]: unknown } = {},
        callback?: HandlerCallback
    ): Promise<boolean> => {
        elizaLogger.log("Starting Taptools GET_TOP_VOLUME_CARDANO handler...");

        // Initialize or update state
        let currentState = state;
        if (!currentState) {
            currentState = (await runtime.composeState(message)) as State;
        } else {
            currentState = await runtime.updateRecentMessageState(currentState);
        }

        try {
            // Compose and generate top volume content
            const topVolumeContext = composeContext({
                state: currentState,
                template: getTopVolumeTemplate,
            });

            const content = (await generateObjectDeprecated({
                runtime,
                context: topVolumeContext,
                modelClass: ModelClass.SMALL,
            })) as unknown as GetTopVolumeContent;

            // Validate content
            if (!isGetTopVolumeContent(content)) {
                throw new Error("Invalid top volume content");
            }

            // Get top volume tokens from Taptools
            const config = await validateTaptoolsConfig(runtime);
            const taptoolsService = createTaptoolsService(config.TAPTOOLS_API_KEY);
            
            try {
                const timeframe = content.timeframe || "24h";
                const limit = content.limit || 10;
                
                const topTokens = await taptoolsService.getTopVolumeTokens(timeframe, limit);
                
                if (callback) {
                    // Format the data for display
                    const timeframeDisplay = {
                        "1h": "1 hour",
                        "4h": "4 hours",
                        "12h": "12 hours",
                        "24h": "24 hours",
                        "7d": "7 days",
                        "30d": "30 days",
                        "180d": "180 days",
                        "1y": "1 year",
                        "all": "all time"
                    }[timeframe] || timeframe;
                    
                    let responseText = `Top ${limit} Cardano Tokens by Volume (${timeframeDisplay}):\n\n`;
                    
                    topTokens.forEach((token, index) => {
                        const formattedPrice = new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 6,
                        }).format(token.price);
                        
                        const formattedVolume = new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                            notation: 'compact',
                            compactDisplay: 'short',
                        }).format(token.volume);
                        
                        const priceChangeFormatted = token.priceChange24h > 0 
                            ? `+${token.priceChange24h.toFixed(2)}%` 
                            : `${token.priceChange24h.toFixed(2)}%`;
                        
                        responseText += `${index + 1}. ${token.symbol} - ${formattedVolume} volume - ${formattedPrice} - ${priceChangeFormatted}\n`;
                    });
                    
                    responseText += "\nUse 'GET_CARDANO_PRICE' or 'GET_CARDANO_TRADING_STATS' for more details on any token.";
                    
                    callback({
                        text: responseText,
                        content: {
                            topTokens: topTokens,
                            timeframe: timeframe,
                            limit: limit
                        },
                    });
                }

                return true;
            } catch (error) {
                elizaLogger.error("Error in GET_TOP_VOLUME_CARDANO handler:", error);
                if (callback) {
                    callback({
                        text: `Error fetching top volume tokens: ${error.message}`,
                        content: { error: error.message },
                    });
                }
                return false;
            }
        } catch (error) {
            elizaLogger.error("Error in GET_TOP_VOLUME_CARDANO handler:", error);
            if (callback) {
                callback({
                    text: `Error fetching top volume tokens: ${error.message}`,
                    content: { error: error.message },
                });
            }
            return false;
        }
    },
    examples: topVolumeExamples,
} as Action;
