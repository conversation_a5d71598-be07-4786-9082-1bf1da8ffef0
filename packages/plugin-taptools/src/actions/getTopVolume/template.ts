export const getTopVolumeTemplate = `
# INSTRUCTIONS: Extract the timeframe and limit from the message

You are extracting parameters for a request to get the top volume Cardano tokens. The user is asking about the tokens with the highest trading volume on the Cardano blockchain.

## PARAMETERS:
1. timeframe: The time period for volume calculation. Options are [1h, 4h, 12h, 24h, 7d, 30d, 180d, 1y, all]. Default is 24h.
2. limit: The number of tokens to return. Maximum is 100. Default is 10.

## EXAMPLES:
Message: "Show me the top volume Cardano tokens"
Parameters: { "timeframe": "24h", "limit": 10 }

Message: "What are the top 10 Cardano tokens by volume in the last week?"
Parameters: { "timeframe": "7d", "limit": 10 }

Message: "Show me the top 20 tokens by volume in the last month"
Parameters: { "timeframe": "30d", "limit": 20 }

Message: "What are the top 5 tokens by volume today?"
Parameters: { "timeframe": "24h", "limit": 5 }

Message: "Show me the top 50 tokens by volume in the last hour"
Parameters: { "timeframe": "1h", "limit": 50 }

## TASK:
Extract the timeframe and limit from the following message:
"{{message.content.text}}"

## RESPONSE FORMAT:
Respond with a JSON object containing the parameters:
{
  "timeframe": "TIMEFRAME",
  "limit": NUMBER
}

If the timeframe is not specified, use "24h".
If the limit is not specified, use 10.
`;
