import {
    composeContext,
    elizaLogger,
    generateObjectDeprecated,
    type HandlerCallback,
    type IAgentRuntime,
    type Memory,
    ModelClass,
    type State,
    type Action,
} from "@elizaos/core";
import { validateTaptoolsConfig } from "../../environment";
import { priceExamples } from "./examples";
import { createTaptoolsService } from "../../service";
import { getPriceTemplate } from "./template";
import type { GetPriceContent } from "../../types";
import { isGetPriceContent } from "./validation";

export default {
    name: "GET_CARDANO_PRICE",
    similes: [
        "CHECK_CARDANO_PRICE",
        "PRICE_CHECK_CARDANO",
        "GET_ADA_TOKEN_PRICE",
        "CHECK_ADA_TOKEN_PRICE",
        "GET_CARDANO_TOKEN_PRICE",
        "CHECK_CARDANO_TOKEN_PRICE",
    ],
    // eslint-disable-next-line
    validate: async (runtime: IAgentRuntime, _message: Memory) => {
        await validateTaptoolsConfig(runtime);
        return true;
    },
    description: "Get the current price and market data of a Cardano token from Taptools",
    handler: async (
        runtime: IAgentRuntime,
        message: Memory,
        state: State,
        options: { [key: string]: unknown } = {},
        callback?: HandlerCallback
    ): Promise<boolean> => {
        elizaLogger.log("Starting Taptools GET_CARDANO_PRICE handler...");

        // Initialize or update state
        let currentState = state;
        if (!currentState) {
            currentState = (await runtime.composeState(message)) as State;
        } else {
            currentState = await runtime.updateRecentMessageState(currentState);
        }

        try {
            // Compose and generate price check content
            const priceContext = composeContext({
                state: currentState,
                template: getPriceTemplate,
            });

            const content = (await generateObjectDeprecated({
                runtime,
                context: priceContext,
                modelClass: ModelClass.SMALL,
            })) as unknown as GetPriceContent;

            // Validate content
            if (!isGetPriceContent(content)) {
                throw new Error("Invalid price check content");
            }

            // Get price from Taptools
            const config = await validateTaptoolsConfig(runtime);
            const taptoolsService = createTaptoolsService(config.TAPTOOLS_API_KEY);
            
            try {
                const priceData = await taptoolsService.getTokenPrice(content.symbol);
                
                if (callback) {
                    // Format the price data for display
                    const formattedPrice = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 6,
                    }).format(priceData.price);
                    
                    const formattedMarketCap = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        notation: 'compact',
                        compactDisplay: 'short',
                    }).format(priceData.marketCap);
                    
                    const formattedVolume = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        notation: 'compact',
                        compactDisplay: 'short',
                    }).format(priceData.volume24h);
                    
                    const priceChangeFormatted = priceData.priceChange24h > 0 
                        ? `+${priceData.priceChange24h.toFixed(2)}%` 
                        : `${priceData.priceChange24h.toFixed(2)}%`;
                    
                    callback({
                        text: `${priceData.name} (${priceData.symbol}) data:
• Price: ${formattedPrice}
• 24h Change: ${priceChangeFormatted}
• 24h Volume: ${formattedVolume}
• Market Cap: ${formattedMarketCap}
• Supply: ${new Intl.NumberFormat('en-US').format(priceData.supply)}${priceData.maxSupply ? ` / ${new Intl.NumberFormat('en-US').format(priceData.maxSupply)}` : ''}`,
                        content: {
                            tokenData: priceData
                        },
                    });
                }

                return true;
            } catch (error) {
                elizaLogger.error("Error in GET_CARDANO_PRICE handler:", error);
                if (callback) {
                    callback({
                        text: `Error fetching price: ${error.message}`,
                        content: { error: error.message },
                    });
                }
                return false;
            }
        } catch (error) {
            elizaLogger.error("Error in GET_CARDANO_PRICE handler:", error);
            if (callback) {
                callback({
                    text: `Error fetching price: ${error.message}`,
                    content: { error: error.message },
                });
            }
            return false;
        }
    },
    examples: priceExamples,
} as Action;
