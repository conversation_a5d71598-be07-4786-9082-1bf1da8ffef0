export const getPriceTemplate = `
# INSTRUCTIONS: Extract the token symbol from the message

You are extracting the Cardano token symbol from a message. The user is asking about the price of a cryptocurrency token on the Cardano blockchain.

Common Cardano tokens include:
- ADA (Card<PERSON>'s native token)
- SNEK
- MISTER
- CHAD
- HOSKY
- SUNDAE
- MIN
- LQ
- LENFI
- AGIX
- COPI
- INDY
- VYFI
- CLAY
- TALOS
- IAG
- BOO
- DJED
- WMT

But the user might ask about ANY Cardano token, not just these common ones.

## EXAMPLES:
Message: "What's the price of ADA?"
Symbol: "ADA"

Message: "How is <PERSON><PERSON><PERSON> doing?"
Symbol: "SNEK"

Message: "Check the price of MISTER"
Symbol: "MISTER"

Message: "What's the current value of HOSKY?"
Symbol: "HOSKY"

## TASK:
Extract the token symbol from the following message:
"{{message.content.text}}"

## RESPONSE FORMAT:
Respond with a JSON object containing the symbol:
{
  "symbol": "TOKEN_SYMBOL"
}
`;
