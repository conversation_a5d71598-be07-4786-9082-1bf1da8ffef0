import type { ActionExample } from "@elizaos/core";

export const priceExamples: ActionExample[][] = [
    [
        {
            user: "{{user1}}",
            content: {
                text: "What's the current price of ADA?",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: "Let me check the current ADA price data for you.",
                action: "GET_CARDANO_PRICE",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: `Cardano (ADA) data:
• Price: $0.45
• 24h Change: +2.35%
• 24h Volume: $245.8M
• Market Cap: $16.2B
• Supply: 36,000,000,000`,
            },
        },
    ],
    [
        {
            user: "{{user1}}",
            content: {
                text: "How is <PERSON><PERSON><PERSON> doing?",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: "I'll check the latest SNEK token data for you.",
                action: "GET_CARDANO_PRICE",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: `SNEK (SNEK) data:
• Price: $0.000123
• 24h Change: +5.67%
• 24h Volume: $1.2M
• Market Cap: $12.3M
• Supply: 100,000,000,000`,
            },
        },
    ],
    [
        {
            user: "{{user1}}",
            content: {
                text: "Check the price of MISTER",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: "Let me get the latest MISTER token data for you.",
                action: "GET_CARDANO_PRICE",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: `MISTER (MISTER) data:
• Price: $0.00789
• 24h Change: -1.23%
• 24h Volume: $345.6K
• Market Cap: $7.89M
• Supply: 1,000,000,000`,
            },
        },
    ],
];
