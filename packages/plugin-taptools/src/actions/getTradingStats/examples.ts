import type { ActionExample } from "@elizaos/core";

export const tradingStatsExamples: ActionExample[][] = [
    [
        {
            user: "{{user1}}",
            content: {
                text: "Show me detailed stats for ADA",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: "Let me get the detailed trading statistics for ADA.",
                action: "GET_CARDANO_TRADING_STATS",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: `Cardano (ADA) Trading Stats:
• Price: $0.45
• 24h Change: +2.35%
• 7d Change: +5.67%
• 30d Change: -1.23%
• 24h Volume: $245.8M
• Volume Change 24h: +12.5%
• Market Cap: $16.2B
• Supply: 36,000,000,000 / 45,000,000,000
• Holders: 3,500,000
• Transactions 24h: 125,000`,
            },
        },
    ],
    [
        {
            user: "{{user1}}",
            content: {
                text: "What are the trading metrics for SNEK?",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: "I'll fetch the comprehensive trading metrics for SNEK.",
                action: "GET_CARDANO_TRADING_STATS",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: `SNEK (SNEK) Trading Stats:
• Price: $0.000123
• 24h Change: +5.67%
• 7d Change: +12.34%
• 30d Change: +45.67%
• 24h Volume: $1.2M
• Volume Change 24h: +8.9%
• Market Cap: $12.3M
• Supply: 100,000,000,000
• Holders: 45,000
• Transactions 24h: 2,500`,
            },
        },
    ],
    [
        {
            user: "{{user1}}",
            content: {
                text: "Give me all the stats for MISTER token",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: "I'll gather all the trading statistics for MISTER token.",
                action: "GET_CARDANO_TRADING_STATS",
            },
        },
        {
            user: "{{agent}}",
            content: {
                text: `MISTER (MISTER) Trading Stats:
• Price: $0.00789
• 24h Change: -1.23%
• 7d Change: +4.56%
• 30d Change: +23.45%
• 24h Volume: $345.6K
• Volume Change 24h: -2.3%
• Market Cap: $7.89M
• Supply: 1,000,000,000
• Holders: 12,500
• Transactions 24h: 850`,
            },
        },
    ],
];
