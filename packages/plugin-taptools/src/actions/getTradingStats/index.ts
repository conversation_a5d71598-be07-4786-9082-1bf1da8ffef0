import {
    composeContext,
    eliza<PERSON>ogger,
    generateObjectDeprecated,
    type HandlerCallback,
    type IAgentRuntime,
    type Memory,
    ModelClass,
    type State,
    type Action,
} from "@elizaos/core";
import { validateTaptoolsConfig } from "../../environment";
import { tradingStatsExamples } from "./examples";
import { createTaptoolsService } from "../../service";
import { getTradingStatsTemplate } from "./template";
import type { GetTradingStatsContent } from "../../types";
import { isGetTradingStatsContent } from "./validation";

export default {
    name: "GET_CARDANO_TRADING_STATS",
    similes: [
        "CHECK_CARDANO_STATS",
        "GET_ADA_TOKEN_STATS",
        "CHECK_ADA_TOKEN_STATS",
        "GET_CARDANO_TOKEN_STATS",
        "CHECK_CARDANO_TOKEN_STATS",
        "GET_CARDANO_METRICS",
        "TOKEN_ANALYSIS_CARDANO",
    ],
    // eslint-disable-next-line
    validate: async (runtime: IAgentRuntime, _message: Memory) => {
        await validateTaptoolsConfig(runtime);
        return true;
    },
    description: "Get detailed trading statistics for a Cardano token from Taptools",
    handler: async (
        runtime: IAgentRuntime,
        message: Memory,
        state: State,
        options: { [key: string]: unknown } = {},
        callback?: HandlerCallback
    ): Promise<boolean> => {
        elizaLogger.log("Starting Taptools GET_CARDANO_TRADING_STATS handler...");

        // Initialize or update state
        let currentState = state;
        if (!currentState) {
            currentState = (await runtime.composeState(message)) as State;
        } else {
            currentState = await runtime.updateRecentMessageState(currentState);
        }

        try {
            // Compose and generate trading stats content
            const statsContext = composeContext({
                state: currentState,
                template: getTradingStatsTemplate,
            });

            const content = (await generateObjectDeprecated({
                runtime,
                context: statsContext,
                modelClass: ModelClass.SMALL,
            })) as unknown as GetTradingStatsContent;

            // Validate content
            if (!isGetTradingStatsContent(content)) {
                throw new Error("Invalid trading stats content");
            }

            // Get trading stats from Taptools
            const config = await validateTaptoolsConfig(runtime);
            const taptoolsService = createTaptoolsService(config.TAPTOOLS_API_KEY);
            
            try {
                const statsData = await taptoolsService.getTokenTradingStats(content.symbol);
                
                if (callback) {
                    // Format the data for display
                    const formattedPrice = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 6,
                    }).format(statsData.price);
                    
                    const formattedMarketCap = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        notation: 'compact',
                        compactDisplay: 'short',
                    }).format(statsData.marketCap);
                    
                    const formattedVolume = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        notation: 'compact',
                        compactDisplay: 'short',
                    }).format(statsData.volume24h);
                    
                    const formatPercentage = (value: number) => value > 0 
                        ? `+${value.toFixed(2)}%` 
                        : `${value.toFixed(2)}%`;
                    
                    callback({
                        text: `${statsData.name} (${statsData.symbol}) Trading Stats:
• Price: ${formattedPrice}
• 24h Change: ${formatPercentage(statsData.priceChange24h)}
• 7d Change: ${formatPercentage(statsData.priceChange7d)}
• 30d Change: ${formatPercentage(statsData.priceChange30d)}
• 24h Volume: ${formattedVolume}
• Volume Change 24h: ${formatPercentage(statsData.volumeChange24h)}
• Market Cap: ${formattedMarketCap}
• Supply: ${new Intl.NumberFormat('en-US').format(statsData.supply)}${statsData.maxSupply ? ` / ${new Intl.NumberFormat('en-US').format(statsData.maxSupply)}` : ''}
${statsData.holders ? `• Holders: ${new Intl.NumberFormat('en-US').format(statsData.holders)}` : ''}
${statsData.transactions24h ? `• Transactions 24h: ${new Intl.NumberFormat('en-US').format(statsData.transactions24h)}` : ''}`,
                        content: {
                            tokenStats: statsData
                        },
                    });
                }

                return true;
            } catch (error) {
                elizaLogger.error("Error in GET_CARDANO_TRADING_STATS handler:", error);
                if (callback) {
                    callback({
                        text: `Error fetching trading stats: ${error.message}`,
                        content: { error: error.message },
                    });
                }
                return false;
            }
        } catch (error) {
            elizaLogger.error("Error in GET_CARDANO_TRADING_STATS handler:", error);
            if (callback) {
                callback({
                    text: `Error fetching trading stats: ${error.message}`,
                    content: { error: error.message },
                });
            }
            return false;
        }
    },
    examples: tradingStatsExamples,
} as Action;
