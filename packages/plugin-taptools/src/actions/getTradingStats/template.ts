export const getTradingStatsTemplate = `
# INSTRUCTIONS: Extract the token symbol from the message

You are extracting the Cardano token symbol from a message. The user is asking about detailed trading statistics of a cryptocurrency token on the Cardano blockchain.

Common Cardano tokens include:
- ADA (Card<PERSON>'s native token)
- SNEK
- MISTER
- CHAD
- HOSKY
- SUNDAE
- MIN
- LQ
- LENFI
- AGIX
- COPI
- INDY
- VYFI
- CLAY
- TALOS
- IAG
- BOO
- DJED
- WMT

But the user might ask about ANY Cardano token, not just these common ones.

## EXAMPLES:
Message: "Show me detailed stats for ADA"
Symbol: "ADA"

Message: "What are the trading metrics for SNEK?"
Symbol: "SNEK"

Message: "Give me all the stats for <PERSON><PERSON><PERSON> token"
Symbol: "MISTER"

Message: "I need comprehensive data on HOSKY"
Symbol: "HOSKY"

## TASK:
Extract the token symbol from the following message:
"{{message.content.text}}"

## RESPONSE FORMAT:
Respond with a JSON object containing the symbol:
{
  "symbol": "TOKEN_SYMBOL"
}
`;
