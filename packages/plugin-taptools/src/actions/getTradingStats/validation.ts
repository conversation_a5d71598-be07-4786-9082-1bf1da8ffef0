import { z } from "zod";
import type { GetTradingStatsContent } from "../../types";

const getTradingStatsSchema = z.object({
    symbol: z.string().min(1, "Token symbol is required"),
});

export function isGetTradingStatsContent(content: any): content is GetTradingStatsContent {
    try {
        getTradingStatsSchema.parse(content);
        return true;
    } catch (error) {
        return false;
    }
}
